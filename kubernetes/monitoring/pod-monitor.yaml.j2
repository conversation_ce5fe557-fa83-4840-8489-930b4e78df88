---
apiVersion: monitoring.coreos.com/v1
kind: PodMonitor
metadata:
  name: {{ folder }}-{{ job_name }}-jobmanager-podmonitor
  labels:
    release: prom-op
  namespace: pinot
spec:
  selector:
    matchLabels:
      app: {{ folder }}-{{ job_name }}
      component: jobmanager
      type: flink-native-kubernetes
  namespaceSelector:
    matchNames:
      - pinot
  podMetricsEndpoints:
    - path: /
      interval: 10s
      relabelings:
        # The Kubernetes service discovery will create a target for every port on the
        # Flink containers. So first, keep only the targets for the RPC port.
        - sourceLabels:
            - __meta_kubernetes_pod_container_port_number
          action: keep
          regex: "6123"
        # Next, we don't actually want the RPC port. So replace the port number for
        # the container port label and the endpoint label to 9249.
        - targetLabel: __meta_kubernetes_pod_container_port_number
          replacement: "9249"
        - targetLabel: endpoint
          replacement: "9249"
        # Finally, override the special __address__ label that specifies what IP:Port
        # Prometheus should scrape with the right value.
        - sourceLabels:
            - __meta_kubernetes_pod_ip
            - __meta_kubernetes_pod_container_port_number
          separator: ":"
          targetLabel: __address__
---
apiVersion: monitoring.coreos.com/v1
kind: PodMonitor
metadata:
  name: {{ folder }}-{{ job_name }}-taskmanager-podmonitor
  labels:
    release: prom-op
  namespace: pinot
spec:
  selector:
    matchLabels:
      app: {{ folder }}-{{ job_name }}
      component: taskmanager
      type: flink-native-kubernetes
  namespaceSelector:
    matchNames:
      - pinot
  podMetricsEndpoints:
    - path: /
      interval: 10s
      relabelings:
        # The Kubernetes service discovery will create a target for every port on the
        # Flink containers. So first, keep only the targets for the RPC port.
        - sourceLabels:
            - __meta_kubernetes_pod_container_port_number
          action: keep
          regex: "6122"
        # Next, we don't actually want the RPC port. So replace the port number for
        # the container port label and the endpoint label to 9249.
        - targetLabel: __meta_kubernetes_pod_container_port_number
          replacement: "9249"
        - targetLabel: endpoint
          replacement: "9249"
        # Finally, override the special __address__ label that specifies what IP:Port
        # Prometheus should scrape with the right value.
        - sourceLabels:
            - __meta_kubernetes_pod_ip
            - __meta_kubernetes_pod_container_port_number
          separator: ":"
          targetLabel: __address__
...
