apiVersion: v1
kind: ServiceAccount
metadata:
  name: flink-service-account
  namespace: pinot
  labels:
    app: flink
---
kind: Role
apiVersion: rbac.authorization.k8s.io/v1
metadata:
  name: flink-edit
  namespace: pinot
  labels:
    app: flink
rules:
  - apiGroups: [""]
    resources: ["pods"]
    verbs: ["create", "get", "delete", "list", "watch"]
  - apiGroups: [""]
    resources: ["pods/log"]
    verbs: ["get", list"]
  - apiGroups: [""]
    resources: ["pods/exec"]
    verbs: ["create", "get"]
  - apiGroups: [""]
    resources: ["configmaps"]
    verbs: ["create", "update", "get", "delete", "list", "watch", "patch"]
  - apiGroups: ["apps"]
    resources: ["deployments"]
    verbs: ["create", "get", "delete", "list", "watch"]
  - apiGroups: ["apps"]
    resources: ["deployments/status"]
    verbs: ["get"]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: flink-edit-rolebinding
  namespace: pinot
  labels:
    app: flink
roleRef:
  kind: Role
  name: flink-edit
  apiGroup: rbac.authorization.k8s.io
subjects:
  - kind: ServiceAccount
    name: flink-service-account
    namespace: pinot
