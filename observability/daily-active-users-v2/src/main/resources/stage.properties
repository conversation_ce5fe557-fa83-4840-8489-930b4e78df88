
# Properties for stage environment

#Checkpoints
CHECKPOINTS_STORAGE_LOCATION = s3a://grofers-test-dse-singapore/flink-streams/observability/jumbo-daily-active-users/checkpoints/

# Events Source
EVENTS_SOURCE_BROKERS = ****************************************.groupblinkit-agent.kafka.discoveryv2.prod-y.ap-southeast-1.warpstream.com:9092
EVENTS_SOURCE_CONSUMER_GROUP_ID = dau-jumbo-test-events-consumer_group-v1
EVENTS_SOURCE_TOPIC = jumbo_transformed.blinkit.click_events

#Watermark Idleness Configs
IDLENESS_TIME_IN_SECS = 600

# DAU Window Configs
DAU_WINDOW_SIZE_IN_DAYS = 1
MERCHANT_DAU_METRICS_SINK_BROKERS = b-1.testdatacommon.vkkz3t.c5.kafka.ap-southeast-1.amazonaws.com:9092,b-2.testdatacommon.vkkz3t.c5.kafka.ap-southeast-1.amazonaws.com:9092
MERCHANT_DAU_METRICS_SINK_TOPIC = dau-jumbo-test-merchant

CITY_DAU_METRICS_SINK_BROKERS = b-1.testdatacommon.vkkz3t.c5.kafka.ap-southeast-1.amazonaws.com:9092,b-2.testdatacommon.vkkz3t.c5.kafka.ap-southeast-1.amazonaws.com:9092
CITY_DAU_METRICS_SINK_TOPIC = dau-jumbo-test-city
