package com.grofers.observability.daumetrics.datatypes;

import lombok.Value;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.annotation.JsonProperty;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.annotation.JsonSerialize;

@Value
@JsonSerialize
public class MerchantDAUMetric {
    @JsonProperty("app")
    String app;

    @JsonProperty("city")
    String city;

    @JsonProperty("merchant_id")
    Integer merchantId;

    @JsonProperty("window_start")
    Long windowStart;

    @JsonProperty("window_end")
    Long windowEnd;

    @JsonProperty("unique_device_count")
    Integer uniqueDeviceCount;

    public MerchantDAUMetric(
            String app,
            String city,
            Integer merchantId,
            Long windowStart,
            Long windowEnd,
            Integer uniqueDeviceCount) {
        this.app = app;
        this.city = city;
        this.merchantId = merchantId;
        this.windowStart = windowStart;
        this.windowEnd = windowEnd;
        this.uniqueDeviceCount = uniqueDeviceCount;
    }
}
