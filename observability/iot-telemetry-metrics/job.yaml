name: IOT-Telemetry-Metrics
image: 442534439095.dkr.ecr.ap-southeast-1.amazonaws.com/data/flink-streams/observability/iot-telemetry-metrics
flink_config:
  jobmanager.memory.process.size: 1536m
  taskmanager.memory.process.size: 3072m
  taskmanager.numberOfTaskSlots: 2
deployment_config:
  kubernetes.rest-service.exposed.type: NodePort
  metrics.reporters: prom
  metrics.reporter.prom.class: org.apache.flink.metrics.prometheus.PrometheusReporterFactory
allow_non_restored_state: False # Set to True when changes are made to Operator
secrets:
  master:
    SINK_BOOTSTRAP_SERVER_URL_STAGE: "data/kafka/test-data-common:bootstrap.servers"
    SINK_BOOTSTRAP_SERVER_URL_PROD: "data/kafka/prod-data-corestr:bootstrap.servers"
