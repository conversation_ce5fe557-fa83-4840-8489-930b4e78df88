CHECKPOINTS_STORAGE_LOCATION = s3a://prod-data-flink-states/flink-streams/observability/iot-telemetry-metrics/checkpoints/
IOT_TELEMETRY_EVENTS_SOURCE_BROKERS = b-1.prod-data-events.771kbr.c4.kafka.ap-southeast-1.amazonaws.com:9092,b-2.prod-data-events.771kbr.c4.kafka.ap-southeast-1.amazonaws.com:9092,b-3.prod-data-events.771kbr.c4.kafka.ap-southeast-1.amazonaws.com:9092
IOT_TELEMETRY_EVENTS_SOURCE_CONSUMER_GROUP_ID = blinkit.data.flink-iot-telemetry-metrics-pipeline.v1
IOT_TELEMETRY_EVENTS_SOURCE_TOPIC = blinkit.iot-management-service.telemetry-events
IDLENESS_TIME_IN_SECS = 60
ALLOWED_LATE_EVENTS_IN_SECS = 600
IOT_TELEMETRY_METRICS_SINK_TOPIC = observability.iot.telemetry-events
