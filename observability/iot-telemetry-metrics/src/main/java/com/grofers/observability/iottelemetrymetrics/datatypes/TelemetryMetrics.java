package com.grofers.observability.iottelemetrymetrics.datatypes;

public class TelemetryMetrics {
    private final String paramKey;
    private final String deviceType;
    private final Long startTimestamp;
    private final Long endTimestamp;
    private final Long duration;
    private final int entityId;
    private final String entityType;
    private final String tenant;
    private final String externalDeviceId;
    private final String hardwareIdentifier;
    private final String vendor;

    public TelemetryMetrics(
            String paramKey,
            String deviceType,
            Long startTimestamp,
            Long endTimestamp,
            int entityId,
            String entityType,
            String tenant,
            String externalDeviceId,
            String hardwareIdentifier,
            String vendor) {
        this.paramKey = paramKey;
        this.deviceType = deviceType;
        this.startTimestamp = startTimestamp;
        this.endTimestamp = endTimestamp;
        this.duration = endTimestamp - startTimestamp;
        this.entityId = entityId;
        this.entityType = entityType;
        this.tenant = tenant;
        this.externalDeviceId = externalDeviceId;
        this.hardwareIdentifier = hardwareIdentifier;
        this.vendor = vendor;
    }

    public int getEntityId() {
        return this.entityId;
    }

    @Override
    public String toString() {
        return String.format(
            "{"
            + "\"param_key\":\"%s\","
            + "\"device_type\":\"%s\","
            + "\"start_timestamp\":\"%s\","
            + "\"end_timestamp\":\"%s\","
            + "\"duration\":%d,"
            + "\"entity_id\":%d,"
            + "\"entity_type\":\"%s\","
            + "\"tenant\":\"%s\","
            + "\"external_device_id\":\"%s\","
            + "\"hardware_identifier\":\"%s\","
            + "\"vendor\":\"%s\""
            + "}",
            paramKey,
            deviceType,
            startTimestamp,
            endTimestamp,
            duration,
            entityId,
            entityType,
            tenant,
            externalDeviceId,
            hardwareIdentifier,
            vendor
        );
    }
}