package com.grofers.observability.iottelemetrymetrics.helper;

import com.grofers.observability.iottelemetrymetrics.datatypes.TelemetryEvent;

public final class KeyGeneratorHelper {
    private static final String KEY_SEPARATOR = "_";

    private KeyGeneratorHelper() {
        // Private constructor to prevent instantiation
    }

    /**
     * Generates a composite key combining customerId and widgetTitle
     * @param event TelemetryEvent containing customer and widget information
     * @return String in format "customerId_widgetTitle"
     */
    public static String generateIotTelemetryKey(TelemetryEvent event) {
        return String.join(
                KEY_SEPARATOR,
                event.getExternalDeviceId(),
                event.getParamKey()
        );
    }
}
