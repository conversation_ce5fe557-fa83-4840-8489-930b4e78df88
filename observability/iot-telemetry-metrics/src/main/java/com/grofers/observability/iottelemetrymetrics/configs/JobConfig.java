package com.grofers.observability.iottelemetrymetrics.configs;

public final class JobConfig {

    public static String CHECKPOINTS_STORAGE_LOCATION;
    public static String IOT_TELEMETRY_EVENTS_SOURCE_BROKERS;
    public static String IOT_TELEMETRY_EVENTS_SOURCE_CONSUMER_GROUP_ID;
    public static String[] IOT_TELEMETRY_EVENTS_SOURCE_TOPIC;
    public static int IDLENESS_TIME_IN_SECS;
    public static int ALLOWED_LATE_EVENTS_IN_SECS;
    public static String IOT_TELEMETRY_METRICS_SINK_TOPIC;

    public JobConfig(
            String CHECKPOINTS_STORAGE_LOCATION,
            String IOT_TELEMETRY_EVENTS_SOURCE_BROKERS,
            String IOT_TELEMETRY_EVENTS_CONSUMER_GROUP_ID,
            String IOT_TELEMETRY_EVENTS_SOURCE_TOPIC,
            int IDLENESS_TIME_IN_SECS,
            int ALLOWED_LATE_EVENTS_IN_SECS,
            String IOT_TELEMETRY_METRICS_SINK_TOPIC) {

        JobConfig.CHECKPOINTS_STORAGE_LOCATION = CHECKPOINTS_STORAGE_LOCATION;
        JobConfig.IOT_TELEMETRY_EVENTS_SOURCE_BROKERS = IOT_TELEMETRY_EVENTS_SOURCE_BROKERS;
        JobConfig.IOT_TELEMETRY_EVENTS_SOURCE_CONSUMER_GROUP_ID =
                IOT_TELEMETRY_EVENTS_CONSUMER_GROUP_ID;
        JobConfig.IOT_TELEMETRY_EVENTS_SOURCE_TOPIC =
                IOT_TELEMETRY_EVENTS_SOURCE_TOPIC.split(",");
        JobConfig.IDLENESS_TIME_IN_SECS = IDLENESS_TIME_IN_SECS;
        JobConfig.ALLOWED_LATE_EVENTS_IN_SECS = ALLOWED_LATE_EVENTS_IN_SECS;
        JobConfig.IOT_TELEMETRY_METRICS_SINK_TOPIC = IOT_TELEMETRY_METRICS_SINK_TOPIC;
    }
}
