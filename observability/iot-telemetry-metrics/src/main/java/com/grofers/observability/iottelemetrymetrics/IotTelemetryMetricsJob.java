/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.grofers.observability.iottelemetrymetrics;

import static java.time.Duration.ofSeconds;
import java.util.Set;

import org.apache.flink.api.common.ExecutionConfig;
import org.apache.flink.api.common.eventtime.WatermarkStrategy;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.connector.base.DeliveryGuarantee;
import org.apache.flink.connector.kafka.sink.KafkaSink;
import org.apache.flink.connector.kafka.source.KafkaSource;
import org.apache.flink.connector.kafka.source.enumerator.initializer.OffsetsInitializer;
import org.apache.flink.streaming.api.CheckpointingMode;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.datastream.DataStreamSource;
import org.apache.flink.streaming.api.environment.CheckpointConfig;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.kafka.clients.consumer.OffsetResetStrategy;

import com.grofers.observability.iottelemetrymetrics.configs.JobConfig;
import com.grofers.observability.iottelemetrymetrics.configs.JobConfigManager;
import com.grofers.observability.iottelemetrymetrics.datatypes.TelemetryEvent;
import com.grofers.observability.iottelemetrymetrics.datatypes.TelemetryMetrics;
import com.grofers.observability.iottelemetrymetrics.helper.KeyGeneratorHelper;
import com.grofers.observability.iottelemetrymetrics.serdes.JsonDeserializationSchema;
import com.grofers.observability.iottelemetrymetrics.serdes.TelemetryMetricsSerializationSchema;

/**
 * Flink Streaming Job for iot telemetry events
 */
public class IotTelemetryMetricsJob {

    private final DataStreamSource<TelemetryEvent> TelemetryEventDataStreamSource;
    private final KafkaSink<TelemetryMetrics> iotTelemetryMetricsSink;
    private static final Set<String> EVENT_FILTERS = Set.of("hooter_on", "door_state", "room_temp", "TW");

    public IotTelemetryMetricsJob(
            DataStreamSource<TelemetryEvent> TelemetryEventDataStreamSource,
            KafkaSink<TelemetryMetrics> iotTelemetryMetricsSink) {
        this.TelemetryEventDataStreamSource = TelemetryEventDataStreamSource;
        this.iotTelemetryMetricsSink = iotTelemetryMetricsSink;
    }

    public static void main(String[] args) throws Exception {

        // Sets up the execution environment, which is the main entry point
        // to building Flink applications.
        final StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment();

        // Parameters for environment
        ParameterTool params = ParameterTool.fromArgs(args);
        String userEnv = params.getRequired("environment");

        // Set PropertiesPath for userEnv
        JobConfigManager.setJobConfigsPath(userEnv);
        // Get PropertiesPath for userEnv
        String jobPropertiesPath = JobConfigManager.getJobConfigsPath(userEnv);

        // Fetch job config
        JobConfigManager.setJobConfigs(env, jobPropertiesPath);
        JobConfigManager.getJobConfigs(env);

        // Checkpoint Configs
        // start a checkpoint every 600000 ms
        env.enableCheckpointing(600000);
        // set mode to exactly-once (default)
        env.getCheckpointConfig().setCheckpointingMode(CheckpointingMode.EXACTLY_ONCE);
        // make sure 120000 ms of progress happen between checkpoints
        env.getCheckpointConfig().setMinPauseBetweenCheckpoints(220000);
        // checkpoints have to complete within a minute, or are discarded
        env.getCheckpointConfig().setCheckpointTimeout(210000);
        // only two consecutive checkpoint failures are tolerated
        env.getCheckpointConfig().setTolerableCheckpointFailureNumber(2);
        // allow only one checkpoint to be in progress at the same time
        env.getCheckpointConfig().setMaxConcurrentCheckpoints(1);
        // enable externalized checkpoints which are retained after job cancellation
        env.getCheckpointConfig()
                .setExternalizedCheckpointCleanup(
                        CheckpointConfig.ExternalizedCheckpointCleanup.RETAIN_ON_CANCELLATION);
        // enables the experimental unaligned checkpoints
        env.getCheckpointConfig().enableUnalignedCheckpoints();
        // sets the checkpoint storage where checkpoint snapshots will be written
        env.getCheckpointConfig().setCheckpointStorage(JobConfig.CHECKPOINTS_STORAGE_LOCATION);
        // Enable checkpoint compression
        ExecutionConfig executionConfig = new ExecutionConfig();
        executionConfig.setUseSnapshotCompression(true);

        // Determine the environment (prod or stage) and set properties accordingly
        String envPrefix = userEnv.equalsIgnoreCase("prod") ? "PROD" : "STAGE";
        String bootstrapServerUrl = System.getenv().getOrDefault("SINK_BOOTSTRAP_SERVER_URL_" + envPrefix, "localhost:9092");

        env.setMaxParallelism(2);

        KafkaSource<TelemetryEvent> TelemetryEventKafkaSource
                = KafkaSource.<TelemetryEvent>builder()
                        .setBootstrapServers(JobConfig.IOT_TELEMETRY_EVENTS_SOURCE_BROKERS)
                        .setGroupId(JobConfig.IOT_TELEMETRY_EVENTS_SOURCE_CONSUMER_GROUP_ID)
                        .setValueOnlyDeserializer(new JsonDeserializationSchema())
                        .setStartingOffsets(
                                OffsetsInitializer.committedOffsets(OffsetResetStrategy.LATEST))
                        .setTopics(JobConfig.IOT_TELEMETRY_EVENTS_SOURCE_TOPIC)
                        .setProperty("partition.discovery.interval.ms", "600000")
                        .setProperty("reconnect.backoff.max.ms", "300000")
                        .setProperty("reconnect.backoff.ms", "60000")
                        .build();

        WatermarkStrategy<TelemetryEvent> TelemetryEventWatermarkStrategy
                = WatermarkStrategy.<TelemetryEvent>forBoundedOutOfOrderness(ofSeconds(600))
                        .withIdleness(ofSeconds(JobConfig.IDLENESS_TIME_IN_SECS))
                        .withTimestampAssigner(
                                (TelemetryEvent, time)
                                -> TelemetryEvent.getEventEpochTime());

        DataStreamSource<TelemetryEvent> TelemetryEventDataStreamSource
                = env.fromSource(
                        TelemetryEventKafkaSource,
                        TelemetryEventWatermarkStrategy,
                        "iot-telemetry-events-kafka-source",
                        TypeInformation.of(TelemetryEvent.class));

        KafkaSink<TelemetryMetrics> iotTelemetryMetricsSink
                = KafkaSink.<TelemetryMetrics>builder()
                        .setBootstrapServers(bootstrapServerUrl)
                        .setDeliverGuarantee(DeliveryGuarantee.AT_LEAST_ONCE)
                        .setRecordSerializer(
                                new TelemetryMetricsSerializationSchema(JobConfig.IOT_TELEMETRY_METRICS_SINK_TOPIC))
                        .build();

        IotTelemetryMetricsJob IotTelemetryMetricsJob
                = new IotTelemetryMetricsJob(
                        TelemetryEventDataStreamSource, iotTelemetryMetricsSink);

        IotTelemetryMetricsJob.execute(env);
    }

    private void execute(StreamExecutionEnvironment env) throws Exception {

        DataStream<TelemetryEvent> filteredIotTelemetryStream
                = TelemetryEventDataStreamSource
                        .name("iot-telemetry-events-source")
                        .uid("iot-telemetry-events-source")
                        .setParallelism(2)
                        .filter(event -> EVENT_FILTERS.contains(event.getParamKey()))
                        .name("filter-iot-telemetry")
                        .uid("filter-iot-telemetry");

        filteredIotTelemetryStream
                .keyBy(KeyGeneratorHelper::generateIotTelemetryKey)
                .process(new TelemetryMetricsCheckWindow())
                .name("process-iot-telemetry")
                .uid("process-iot-telemetry")
                .sinkTo(iotTelemetryMetricsSink)
                .name("iot-telemetry-metrics-sink")
                .uid("iot-telemetry-metrics-sink");

        env.execute("IOT Telemetry Metrics Job");
    }
}
