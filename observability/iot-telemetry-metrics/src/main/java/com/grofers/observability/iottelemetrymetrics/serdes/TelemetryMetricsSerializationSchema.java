package com.grofers.observability.iottelemetrymetrics.serdes;

import java.nio.charset.StandardCharsets;

import org.apache.flink.api.common.serialization.SerializationSchema;
import org.apache.flink.connector.kafka.sink.KafkaRecordSerializationSchema;
import org.apache.kafka.clients.producer.ProducerRecord;

import com.grofers.observability.iottelemetrymetrics.datatypes.TelemetryMetrics;

public class TelemetryMetricsSerializationSchema
        implements KafkaRecordSerializationSchema<TelemetryMetrics> {
    private static final long serialVersionUID = 1L;
    private final String topic;

    public TelemetryMetricsSerializationSchema(String topic) {
        this.topic = topic;
    }

    @Override
    public void open(
            SerializationSchema.InitializationContext context, KafkaSinkContext sinkContext)
            throws Exception {
        KafkaRecordSerializationSchema.super.open(context, sinkContext);
    }

    @Override
    public ProducerRecord<byte[], byte[]> serialize(
            TelemetryMetrics result, KafkaSinkContext kafkaSinkContext, Long aLong) {
        byte[] key = String.valueOf(result.getEntityId()).getBytes(StandardCharsets.UTF_8);
        byte[] value = result.toString().getBytes(StandardCharsets.UTF_8);
        return new ProducerRecord<>(this.topic, key, value);
    }
}
