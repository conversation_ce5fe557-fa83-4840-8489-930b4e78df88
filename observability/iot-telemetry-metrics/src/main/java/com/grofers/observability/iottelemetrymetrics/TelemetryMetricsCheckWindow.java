package com.grofers.observability.iottelemetrymetrics;

import java.util.HashMap;
import java.util.Map;

import org.apache.flink.api.common.state.ValueState;
import org.apache.flink.api.common.state.ValueStateDescriptor;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.KeyedProcessFunction;
import org.apache.flink.util.Collector;

import com.grofers.observability.iottelemetrymetrics.datatypes.TelemetryEvent;
import com.grofers.observability.iottelemetrymetrics.datatypes.TelemetryMetrics;

public class TelemetryMetricsCheckWindow extends KeyedProcessFunction<String, TelemetryEvent, TelemetryMetrics> {

    private static final long METRIC_THRESHOLD_SECONDS = 600L;
    private static final long SESSION_TIMEOUT_MILLIS = 24 * 60 * 60 * 1000L;

    private ValueState<Long> startMetricTimestamp;
    private ValueState<Long> sessionTimeoutTs;
    private ValueState<TelemetryEvent> lastStartEvent;

    private static final Map<String, String> EVENT_MAP = new HashMap<>();

    static {
        EVENT_MAP.put("hooter_on", "hooter_on");
        EVENT_MAP.put("door_state", "door_open");
        EVENT_MAP.put("room_temp", "room_temp_breached");
        EVENT_MAP.put("TW", "frozen_room_off");
    }

    @Override
    public void open(Configuration parameters) throws Exception {
        startMetricTimestamp = getRuntimeContext().getState(
                new ValueStateDescriptor<>("startMetricTimestamp", Long.class));
        sessionTimeoutTs = getRuntimeContext().getState(
                new ValueStateDescriptor<>("sessionTimeoutTs", Long.class));
                lastStartEvent = getRuntimeContext().getState(
        new ValueStateDescriptor<>("lastStartEvent", TelemetryEvent.class));
    }

    @Override
    public void processElement(
            TelemetryEvent event,
            Context context,
            Collector<TelemetryMetrics> collector) throws Exception {

        Long startTimestamp = startMetricTimestamp.value();
        Long eventTimestamp = event.getEventEpochTime();
        if (eventTimestamp == null) {
            return;
        }
        if (event.getParamValue() == null || event.getParamValue().isEmpty()) {
            return;
        }
        boolean isMetricsOn = event.isMetricsOn();

        if (isMetricsOn) {
            if (startTimestamp == null) {
                startMetricTimestamp.update(eventTimestamp);
                long timeoutTs = eventTimestamp * 1000L + SESSION_TIMEOUT_MILLIS;
                context.timerService().registerEventTimeTimer(timeoutTs);
                sessionTimeoutTs.update(timeoutTs);
                lastStartEvent.update(event);
            }
        } else {
            if (startTimestamp != null && (eventTimestamp - startTimestamp > METRIC_THRESHOLD_SECONDS)) {
                emitTelemetryMetrics(event, startTimestamp, eventTimestamp, collector);
            }
            Long timerTs = sessionTimeoutTs.value();
            if (timerTs != null) {
                context.timerService().deleteEventTimeTimer(timerTs);
            }
            clearState();
        }
    }

    @Override
    public void onTimer(long timestamp, OnTimerContext ctx, Collector<TelemetryMetrics> collector) throws Exception {
        Long startTs = startMetricTimestamp.value();
        Long timeoutTs = sessionTimeoutTs.value();
        TelemetryEvent startEvent = lastStartEvent.value();
        if (startTs != null && timeoutTs != null && timeoutTs.equals(timestamp) && startEvent != null) {
            emitTelemetryMetrics(startEvent, startTs, timeoutTs/1000L, collector);
            clearState();
        }
    }

    private void clearState() throws Exception {
        sessionTimeoutTs.clear();
        startMetricTimestamp.clear();
        lastStartEvent.clear();
    }

    private void emitTelemetryMetrics(TelemetryEvent event, long startTime, long endTime, Collector<TelemetryMetrics> collector) {
        collector.collect(new TelemetryMetrics(
                EVENT_MAP.getOrDefault(event.getParamKey(), event.getParamKey()),
                event.getDeviceType(),
                startTime,
                endTime,
                event.getEntityId(),
                event.getEntityType(),
                event.getTenant(),
                event.getExternalDeviceId(),
                event.getHardwareIdentifier(),
                event.getVendor()
        ));
    }
}