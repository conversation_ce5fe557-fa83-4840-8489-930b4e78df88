package com.grofers.observability.iottelemetrymetrics.serdes;

import java.io.IOException;

import org.apache.flink.api.common.serialization.DeserializationSchema;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.MapperFeature;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.ObjectMapper;

import com.grofers.observability.iottelemetrymetrics.datatypes.TelemetryEvent;

public class JsonDeserializationSchema implements DeserializationSchema<TelemetryEvent> {
    private static final long serialVersionUID = 1L;
    private static final ObjectMapper objectMapper =
            new ObjectMapper().configure(MapperFeature.AUTO_DETECT_GETTERS, false);

    @Override
    public TelemetryEvent deserialize(byte[] bytes) throws IOException {
        return objectMapper.readValue(bytes, TelemetryEvent.class);
    }

    @Override
    public boolean isEndOfStream(TelemetryEvent TelemetryEvent) {
        return false;
    }

    @Override
    public TypeInformation<TelemetryEvent> getProducedType() {
        return null;
    }
}
