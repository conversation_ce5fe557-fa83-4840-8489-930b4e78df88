package com.grofers.observability.iottelemetrymetrics.datatypes;

import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.annotation.JsonProperty;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@JsonIgnoreProperties(ignoreUnknown = true)
public class TelemetryEvent {
    private static final Logger LOG = LoggerFactory.getLogger(TelemetryEvent.class);
    private final String paramKey;
    private final String paramValue;
    private final int trackingDeviceId;
    private final String deviceType;
    private final Long eventTimestamp;
    private final int trackingEntityId;
    private final int entityId;
    private final String entityType;
    private final String tenant;
    private final String externalDeviceId;
    private final String hardwareIdentifier;
    private final String vendor;

    public TelemetryEvent(
            @JsonProperty(value = "param_key", required = true) String paramKey,
            @JsonProperty(value = "param_value", required = true) String paramValue,
            @JsonProperty(value = "tracking_device_id", required = true) int trackingDeviceId,
            @JsonProperty(value = "device_type", required = true) String deviceType,
            @JsonProperty(value = "event_timestamp", required = true) Long eventTimestamp,
            @JsonProperty(value = "tracking_entity_id", required = true) int trackingEntityId,
            @JsonProperty(value = "entity_id", required = true) int entityId,
            @JsonProperty(value = "entity_type", required = true) String entityType,
            @JsonProperty(value = "tenant", required = true) String tenant,
            @JsonProperty(value = "external_device_id", required = true) String externalDeviceId,
            @JsonProperty(value = "hardware_identifier", required = true) String hardwareIdentifier,
            @JsonProperty(value = "vendor", required = true) String vendor) {
        this.paramKey = paramKey;
        this.paramValue = paramValue;
        this.trackingDeviceId = trackingDeviceId;
        this.deviceType = deviceType;
        this.eventTimestamp = eventTimestamp;
        this.trackingEntityId = trackingEntityId;
        this.entityId = entityId;
        this.entityType = entityType;
        this.tenant = tenant;
        this.externalDeviceId = externalDeviceId;
        this.hardwareIdentifier = hardwareIdentifier;
        this.vendor = vendor;
    }

    public String getParamKey() {
        return paramKey;
    }

    public String getParamValue() {
        return paramValue;
    }

    public int getTrackingDeviceId() {
        return trackingDeviceId;
    }

    public String getDeviceType() {
        return deviceType;
    }

    public Long getEventEpochTime() {
        return eventTimestamp;
    }

    public int getTrackingEntityId() {
        return trackingEntityId;
    }

    public int getEntityId() {
        return entityId;
    }

    public String getEntityType() {
        return entityType;
    }

    public String getTenant() {
        return tenant;
    }

    public String getExternalDeviceId() {
        return externalDeviceId;
    }

    public String getHardwareIdentifier() {
        return hardwareIdentifier;
    }

    public String getVendor() {
        return vendor;
    }

    public boolean isMetricsOn() {
        try{
            switch (this.paramKey) {
                case "hooter_on":
                    return "true".equals(this.paramValue);
                case "door_state":
                    return "OPEN".equals(this.paramValue);
                case "room_temp":
                    return Double.parseDouble(this.paramValue) > -16.0; 
                case "TW":
                    return Double.parseDouble(this.paramValue) < 0.1;
            }
        } catch (NumberFormatException e) {
            LOG.info("Invalid number format for paramKey: {}, paramValue: {}, entityID: {}, timestamp: {}", this.paramKey, this.paramValue, this.entityId, this.eventTimestamp);
            return true;
        }
        return false;
    }
}