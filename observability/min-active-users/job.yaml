name: Active Users Per Minute
image: 442534439095.dkr.ecr.ap-southeast-1.amazonaws.com/data/flink-streams/observability/min-active-users
flink_config:
  jobmanager.memory.process.size: 1536m
  taskmanager.memory.process.size: 4096m
  taskmanager.numberOfTaskSlots: "1"
  taskmanager.cpu.cores: 4.0
  parallelism.default: "10"
deployment_config:
  kubernetes.rest-service.exposed.type: NodePort
  metrics.reporters: prom
  metrics.reporter.prom.class: org.apache.flink.metrics.prometheus.PrometheusReporter
allow_non_restored_state: False # Set to True when changes are made to Operator UIDs
codeartifact_access: True
