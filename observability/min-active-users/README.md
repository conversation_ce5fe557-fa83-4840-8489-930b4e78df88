# Minute Active Users Metrics

This Flink streaming job calculates minute-level active user metrics and outputs them in Prometheus format.

## Overview

The job processes JumboEvents from Kafka, aggregates unique users per minute using tumbling windows, and outputs metrics in Prometheus format to Kafka for consumption by metrics collectors.

## Features

- **Minute-level aggregation**: Uses 1-minute tumbling windows to aggregate user activity
- **Unique user counting**: Deduplicates users within each minute window using UniqueUserEvictor
- **Prometheus format**: Outputs metrics in Prometheus format compatible with metrics-collector
- **City and app-level breakdown**: Metrics are broken down by city and app channel
- **Real-time processing**: Continuous event time triggers for near real-time metrics

## Architecture

```
JumboEvents (Kafka) → Filter Events → Key by (app, city) → 1-min Window → Unique User Count → Prometheus Metrics (Kafka)
```

## Event Filtering

The job filters for the following user activity events:
- App Launch
- App Launch Traits
- Checkout Step Viewed
- Cart Viewed
- Search Results Viewed
- Product Added
- Homepage Visit
- Cart Footer Strip Clicked

## Output Format

The job outputs metrics in the following Prometheus format:

```json
{
  "metric_name": "minute_active_users",
  "metric_type": "gauge",
  "help": "Number of active users per minute",
  "labels": {
    "app": "android|ios",
    "city": "city_name"
  },
  "metric_value": 123.0,
  "namespace": "blinkit",
  "subsystem": "observability",
  "timestamp_ms": 1234567890000
}
```

## Configuration

### Environment Variables

- `environment`: Deployment environment (stage/prod)

### Properties Files

- `stage.properties`: Configuration for staging environment
- `prod.properties`: Configuration for production environment

### Key Configuration Parameters

- `EVENTS_SOURCE_TOPIC`: Kafka topic for input JumboEvents
- `PROMETHEUS_METRICS_SINK_TOPIC`: Kafka topic for output Prometheus metrics
- `IDLENESS_TIME_IN_SECS`: Watermark idleness timeout (600s)
- `ALLOWED_LATENESS_IN_SECS`: Allowed lateness for events (60s)

## Deployment

1. Build the project:
   ```bash
   mvn clean package
   ```

2. Build Docker image:
   ```bash
   docker build -t min-active-users .
   ```

3. Deploy using job.yaml configuration

## Monitoring

The job exposes Flink metrics via Prometheus reporter. Key metrics to monitor:

- Window processing latency
- Event throughput
- Checkpoint duration
- Backpressure indicators

## Related Jobs

- `hourly-active-users-v2`: Hourly aggregation of active users
- `daily-active-users-v2`: Daily aggregation of active users
- `apperrors`: Reference implementation for Prometheus metrics format
