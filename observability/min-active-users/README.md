# Minute Active Users Metrics

This Flink streaming job calculates minute-level active user metrics at multiple aggregation levels and outputs them in Prometheus format.

## Overview

The job processes JumboEvents from Kafka, aggregates unique users per minute using tumbling windows at three different levels (pan-India, city, and merchant), and outputs all metrics in Prometheus format to a single Kafka topic for consumption by metrics collectors.

## Features

- **Multi-level aggregation**: Supports pan-India, city, and merchant level aggregations
- **Minute-level granularity**: Uses 1-minute tumbling windows for real-time insights
- **Unique user counting**: Deduplicates users within each minute window using UniqueUserEvictor
- **Prometheus format**: Outputs metrics in Prometheus format compatible with metrics-collector
- **Unified sink**: All aggregation levels output to the same Kafka topic
- **Real-time processing**: Continuous event time triggers for near real-time metrics

## Architecture

```
JumboEvents (Kafka) → Filter Events →
├── Key by (app) → Pan-India Window → Unique User Count ┐
├── Key by (app, city) → City Window → Unique User Count ├→ Union → Prometheus Metrics (Kafka)
└── Key by (app, city, merchant) → Merchant Window → Unique User Count ┘
```

## Event Filtering

The job filters for the following user activity events:
- App Launch
- App Launch Traits
- Checkout Step Viewed
- Cart Viewed
- Search Results Viewed
- Product Added
- Homepage Visit
- Cart Footer Strip Clicked

## Aggregation Levels

### 1. Pan-India Level
- **Key**: `app` (android/ios)
- **Metric**: `minute_active_users_pan_india`
- **Labels**: `app`, `level=pan_india`

### 2. City Level
- **Key**: `app`, `city`
- **Metric**: `minute_active_users_city`
- **Labels**: `app`, `city`, `level=city`

### 3. Merchant Level
- **Key**: `app`, `city`, `merchant_id`
- **Metric**: `minute_active_users_merchant`
- **Labels**: `app`, `city`, `merchant_id`, `level=merchant`

## Output Format

The job outputs metrics in the following Prometheus format:

```json
{
  "metric_name": "minute_active_users_city",
  "metric_type": "gauge",
  "help": "Number of active users per minute at city level",
  "labels": {
    "app": "android",
    "city": "bangalore",
    "level": "city"
  },
  "metric_value": 123.0,
  "namespace": "blinkit",
  "subsystem": "observability",
  "timestamp_ms": 1234567890000
}
```

## Configuration

### Environment Variables

- `environment`: Deployment environment (stage/prod)

### Properties Files

- `stage.properties`: Configuration for staging environment
- `prod.properties`: Configuration for production environment

### Key Configuration Parameters

- `EVENTS_SOURCE_TOPIC`: Kafka topic for input JumboEvents
- `PROMETHEUS_METRICS_SINK_TOPIC`: Kafka topic for output Prometheus metrics
- `IDLENESS_TIME_IN_SECS`: Watermark idleness timeout (600s)
- `ALLOWED_LATENESS_IN_SECS`: Allowed lateness for events (60s)

## Deployment

1. Build the project:
   ```bash
   mvn clean package
   ```

2. Build Docker image:
   ```bash
   docker build -t min-active-users .
   ```

3. Deploy using job.yaml configuration

## Monitoring

The job exposes Flink metrics via Prometheus reporter. Key metrics to monitor:

- Window processing latency
- Event throughput
- Checkpoint duration
- Backpressure indicators

## Related Jobs

- `hourly-active-users-v2`: Hourly aggregation of active users
- `daily-active-users-v2`: Daily aggregation of active users
- `apperrors`: Reference implementation for Prometheus metrics format
