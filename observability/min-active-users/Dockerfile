FROM public.ecr.aws/zomato/flink:1.19.0-java11


RUN wget -P $FLINK_HOME/lib/ https://repo1.maven.org/maven2/org/apache/kafka/kafka-clients/3.4.0/kafka-clients-3.4.0.jar; \
    wget -P $FLINK_HOME/lib/ https://repo.maven.apache.org/maven2/org/apache/flink/flink-s3-fs-hadoop/1.19.0/flink-s3-fs-hadoop-1.19.0.jar; \
    wget -P $FLINK_HOME/lib/ https://repo.maven.apache.org/maven2/org/apache/flink/flink-json/1.19.0/flink-json-1.19.0.jar;

# Create the usrlib directory if it doesn't exist
RUN mkdir -p $FLINK_HOME/usrlib

COPY src/main/resources /opt/flink/src/main/resources

# Copy the JAR file to the Flink usrlib directory with the expected name
COPY target/min-active-users-1.0.jar $FLINK_HOME/usrlib/min-active-users.jar

# Set the main class
ENV FLINK_MAIN_CLASS=com.grofers.observability.upmmetrics.UsersPerMinuteJob

