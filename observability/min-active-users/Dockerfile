FROM flink:1.15.2-scala_2.12-java11


RUN wget -P $FLINK_HOME/lib/ https://repo.maven.apache.org/maven2/org/apache/flink/flink-json/1.15.2/flink-json-1.15.2.jar; \
    wget -P $FLINK_HOME/lib/ https://repo.maven.apache.org/maven2/org/apache/flink/flink-s3-fs-hadoop/1.15.2/flink-s3-fs-hadoop-1.15.2.jar; \
    wget -P $FLINK_HOME/lib/ https://repo.maven.apache.org/maven2/org/apache/flink/flink-statebackend-rocksdb/1.15.2/flink-statebackend-rocksdb-1.15.2.jar; \
    wget -P $FLINK_HOME/lib/ https://repo1.maven.org/maven2/org/apache/kafka/kafka-clients/2.8.0/kafka-clients-2.8.0.jar;


# Create the usrlib directory if it doesn't exist
RUN mkdir -p $FLINK_HOME/usrlib

COPY src/main/resources /opt/flink/src/main/resources

# Copy the JAR file to the Flink usrlib directory with the expected name
COPY target/min-active-users-1.0.jar $FLINK_HOME/usrlib/min-active-users.jar

# Set the main class
ENV FLINK_MAIN_CLASS=com.grofers.observability.maumetrics.MinActiveUsersJob
