# Properties for stage environment

#Checkpoints
CHECKPOINTS_STORAGE_LOCATION = s3a://grofers-test-dse-singapore/flink-streams/observability/min-active-users/checkpoints/

# Events Source
EVENTS_SOURCE_BROKERS = ****************************************.groupblinkit-agent.kafka.discoveryv2.prod-y.ap-southeast-1.warpstream.com:9092
EVENTS_SOURCE_CONSUMER_GROUP_ID = flink-observability-min-active-users-consumer_group-test
EVENTS_SOURCE_TOPIC = jumbo_transformed.blinkit.click_events

#Watermark Idleness Configs
IDLENESS_TIME_IN_SECS = 600
ALLOWED_LATENESS_IN_SECS = 60

# Prometheus Metrics Sink
PROMETHEUS_METRICS_SINK_BROKERS = b-1.testdatacommon.vkkz3t.c5.kafka.ap-southeast-1.amazonaws.com:9092,b-2.testdatacommon.vkkz3t.c5.kafka.ap-southeast-1.amazonaws.com:9092
PROMETHEUS_METRICS_SINK_TOPIC = observability.metrics.min-active-users-prometheus-test

#  PROMETHEUS_METRICS_SINK_BROKERS = vpce-06233f70360f757f8-enprgsf3.vpce-svc-0dcaa5761ef958410.ap-southeast-1.vpce.amazonaws.com:9093,vpce-06233f70360f757f8-enprgsf3.vpce-svc-0dcaa5761ef958410.ap-southeast-1.vpce.amazonaws.com:9094,vpce-06233f70360f757f8-enprgsf3.vpce-svc-0dcaa5761ef958410.ap-southeast-1.vpce.amazonaws.com:9095
#  PROMETHEUS_METRICS_SINK_TOPIC = blinkit.data-platform.metrics-collector.aggregated-metrics-v1