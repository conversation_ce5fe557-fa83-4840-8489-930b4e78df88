# Properties for prod environment

#Checkpoints
CHECKPOINTS_STORAGE_LOCATION = s3a://prod-data-flink-states/flink-streams/observability/min-active-users/checkpoints/

# Events Source
EVENTS_SOURCE_BROKERS = ****************************************.groupblinkit-agent.kafka.discoveryv2.prod-y.ap-southeast-1.warpstream.com:9092
EVENTS_SOURCE_CONSUMER_GROUP_ID = flink-observability-min-active-users-consumer_group
EVENTS_SOURCE_TOPIC = jumbo_transformed.blinkit.click_events

#Watermark Idleness Configs
IDLENESS_TIME_IN_SECS = 600
ALLOWED_LATENESS_IN_SECS = 60

# Prometheus Metrics Sink
PROMETHEUS_METRICS_SINK_BROKERS = b-1.prod-data-corestr.uynfnv.c4.kafka.ap-southeast-1.amazonaws.com:9092,b-2.prod-data-corestr.uynfnv.c4.kafka.ap-southeast-1.amazonaws.com:9092,b-3.prod-data-corestr.uynfnv.c4.kafka.ap-southeast-1.amazonaws.com:9092
PROMETHEUS_METRICS_SINK_TOPIC = observability.metrics.min-active-users-prometheus
