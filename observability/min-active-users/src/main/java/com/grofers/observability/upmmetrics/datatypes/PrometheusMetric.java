package com.grofers.observability.upmmetrics.datatypes;

import java.util.Map;

public class PrometheusMetric {
    private final String metricName;
    private final String metricType;
    private final String metricHelp;
    private final Map<String, String> groupingLabels;
    private final Map<String, String> labels;
    private final String metricValue;
    private final Long metricTimestamp;

    public PrometheusMetric(
            String metricName,
            String metricType,
            String helpString,
            Map<String, String> groupingLabels,
            Map<String, String> labels,
            String metricValue,
            Long metricTimestamp) {
        this.metricName = metricName;
        this.metricType = metricType;
        this.metricHelp = helpString;
        this.groupingLabels = groupingLabels;
        this.labels = labels;
        this.metricValue = metricValue;
        this.metricTimestamp = metricTimestamp;
    }

    public String getMetricName() {
        return metricName;
    }

    public String getMetricType() {
        return metricType;
    }

    public String getMetricHelp() {
        return metricHelp;
    }

    public Map<String, String> getLabels() {
        return labels;
    }

    public String getMetricValue() {
        return metricValue;
    }

    public Long getMetricTimestamp() {
        return metricTimestamp;
    }

    public String getLabelString() {
        String labelString = "";
        if (labels.entrySet().size() > 0) {
            StringBuilder builder = new StringBuilder();
            labels.forEach(
                    (labelName, labelValue) -> {
                        builder.append(String.format("%s=\"%s\",", labelName, labelValue));
                    });
            labelString = "{" + builder.substring(0, builder.length() - 1) + "}";
        }
        return labelString;
    }
}
