package com.grofers.observability.maumetrics.serdes;

import com.grofers.observability.maumetrics.datatypes.KafkaMetricMessage;
import java.nio.charset.StandardCharsets;
import org.apache.flink.api.common.serialization.SerializationSchema;
import org.apache.flink.connector.kafka.sink.KafkaRecordSerializationSchema;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.core.JsonProcessingException;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.kafka.clients.producer.ProducerRecord;

public class KafkaMetricMessageSerializationSchema
        implements KafkaRecordSerializationSchema<KafkaMetricMessage> {
    private static final long serialVersionUID = 1L;
    private final String topic;

    public KafkaMetricMessageSerializationSchema(String topic) {
        this.topic = topic;
    }

    @Override
    public void open(
            SerializationSchema.InitializationContext context, KafkaSinkContext sinkContext)
            throws Exception {
        KafkaRecordSerializationSchema.super.open(context, sinkContext);
    }

    @Override
    public ProducerRecord<byte[], byte[]> serialize(
            KafkaMetricMessage result, KafkaSinkContext kafkaSinkContext, Long aLong) {
        ObjectMapper mapper = new ObjectMapper();
        try {
            return new ProducerRecord<>(
                    this.topic, mapper.writeValueAsString(result).getBytes(StandardCharsets.UTF_8));
        } catch (JsonProcessingException e) {
            throw new IllegalArgumentException(
                    String.format("Could not serialize record: %s", result), e);
        }
    }
}
