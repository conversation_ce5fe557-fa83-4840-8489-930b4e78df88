package com.grofers.observability.upmmetrics;

import static java.time.Duration.ofSeconds;

import com.grofers.gandalf.events.JumboEvent;
import com.grofers.gandalf.serdes.JumboEventDeserializer;
import com.grofers.observability.upmmetrics.configs.JobConfig;
import com.grofers.observability.upmmetrics.configs.JobConfigManager;
import com.grofers.observability.upmmetrics.datatypes.KafkaMetricMessage;
import com.grofers.observability.upmmetrics.datatypes.PrometheusMetric;
import com.grofers.observability.upmmetrics.serdes.KafkaMetricMessageSerializationSchema;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import org.apache.flink.api.common.ExecutionConfig;
import org.apache.flink.api.common.JobExecutionResult;
import org.apache.flink.api.common.eventtime.WatermarkStrategy;
import org.apache.flink.api.common.functions.FilterFunction;
import org.apache.flink.api.java.functions.KeySelector;
import org.apache.flink.api.java.tuple.Tuple2;
import org.apache.flink.api.java.tuple.Tuple3;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.connector.base.DeliveryGuarantee;
import org.apache.flink.connector.kafka.sink.KafkaSink;
import org.apache.flink.connector.kafka.source.KafkaSource;
import org.apache.flink.connector.kafka.source.enumerator.initializer.OffsetsInitializer;
import org.apache.flink.contrib.streaming.state.EmbeddedRocksDBStateBackend;
import org.apache.flink.streaming.api.CheckpointingMode;
import org.apache.flink.streaming.api.environment.CheckpointConfig;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.datastream.DataStreamSource;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.streaming.api.functions.windowing.ProcessWindowFunction;
import org.apache.flink.streaming.api.windowing.assigners.TumblingEventTimeWindows;
import org.apache.flink.streaming.api.windowing.time.Time;
import org.apache.flink.streaming.api.windowing.triggers.ContinuousEventTimeTrigger;
import org.apache.flink.streaming.api.windowing.windows.TimeWindow;
import org.apache.flink.util.Collector;
import org.apache.kafka.clients.consumer.OffsetResetStrategy;

/** Flink Streaming Job for calculating Minute Active Users */
public class UsersPerMinuteJob {

    private final DataStreamSource<JumboEvent> impressionEventsSource;
    private final KafkaSink<KafkaMetricMessage> prometheusMetricsSink;

    public UsersPerMinuteJob(
            DataStreamSource<JumboEvent> impressionEventsSource,
            KafkaSink<KafkaMetricMessage> prometheusMetricsSink) {
        this.impressionEventsSource = impressionEventsSource;
        this.prometheusMetricsSink = prometheusMetricsSink;
    }

    public static void main(String[] args) throws Exception {
        // set up the streaming execution environment
        final StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment();

        // Parameters for environment
        ParameterTool params = ParameterTool.fromArgs(args);
        String userEnv = params.getRequired("environment");

        // Set PropertiesPath for userEnv
        JobConfigManager.setJobConfigsPath(userEnv);
        // Get PropertiesPath for userEnv
        String jobPropertiesPath = JobConfigManager.getJobConfigsPath(userEnv);
        // Fetch job config
        JobConfigManager.setJobConfigs(env, jobPropertiesPath);
        JobConfigManager.getJobConfigs(env);

        // Set up checkpointing
        env.enableCheckpointing(60000);
        // set mode to exactly-once (default)
        env.getCheckpointConfig().setCheckpointingMode(CheckpointingMode.EXACTLY_ONCE);
        // make sure 120000 ms of progress happen between checkpoints
        env.getCheckpointConfig().setMinPauseBetweenCheckpoints(240000);
        // checkpoints have to complete within a minute, or are discarded
        env.getCheckpointConfig().setCheckpointTimeout(900000);
        // only two consecutive checkpoint failures are tolerated
        env.getCheckpointConfig().setTolerableCheckpointFailureNumber(2);
        // allow only one checkpoint to be in progress at the same time
        env.getCheckpointConfig().setMaxConcurrentCheckpoints(1);
        // enable externalized checkpoints which are retained after job cancellation
        env.getCheckpointConfig()
                .setExternalizedCheckpointCleanup(
                        CheckpointConfig.ExternalizedCheckpointCleanup.RETAIN_ON_CANCELLATION);
        // enables the experimental unaligned checkpoints
        env.getCheckpointConfig().enableUnalignedCheckpoints();
        env.getCheckpointConfig().setCheckpointStorage(JobConfig.CHECKPOINTS_STORAGE_LOCATION);
        env.setStateBackend(new EmbeddedRocksDBStateBackend(true));
        // env.setParallelism(2);
        // Enable checkpoint compression
        ExecutionConfig executionConfig = new ExecutionConfig();
        executionConfig.setUseSnapshotCompression(true);

        KafkaSource<JumboEvent> impressionEventsSource =
                KafkaSource.<JumboEvent>builder()
                        .setBootstrapServers(JobConfig.EVENTS_SOURCE_BROKERS)
                        .setGroupId(JobConfig.EVENTS_SOURCE_CONSUMER_GROUP_ID)
                        .setClientIdPrefix("users_per_minute_blinkit-")
                        .setValueOnlyDeserializer(new JumboEventDeserializer())
                        .setStartingOffsets(
                                OffsetsInitializer.committedOffsets(OffsetResetStrategy.LATEST))
                        .setTopics(JobConfig.EVENTS_SOURCE_TOPIC)
                        .setProperty("partition.discovery.interval.ms", "600000")
                        .setProperty("reconnect.backoff.max.ms", "300000")
                        .setProperty("reconnect.backoff.ms", "60000")
                        .build();

        WatermarkStrategy<JumboEvent> mauEventsSourceWatermarkStrategy =
                WatermarkStrategy.<JumboEvent>forBoundedOutOfOrderness(ofSeconds(300))
                        .withIdleness(ofSeconds(JobConfig.IDLENESS_TIME_IN_SECS))
                        .withTimestampAssigner(
                                (event, timestamp) -> event.getEventTimestamp() * 1000);

        DataStreamSource<JumboEvent> impressionEventsStreamSource =
                env.fromSource(
                        impressionEventsSource,
                        mauEventsSourceWatermarkStrategy,
                        "impressionEventsSource");

        KafkaSink<KafkaMetricMessage> prometheusMetricsKafkaSink =
                KafkaSink.<KafkaMetricMessage>builder()
                        .setBootstrapServers(JobConfig.PROMETHEUS_METRICS_SINK_BROKERS)
                        .setDeliverGuarantee(DeliveryGuarantee.AT_LEAST_ONCE)
                        .setRecordSerializer(
                                new KafkaMetricMessageSerializationSchema(
                                        JobConfig.PROMETHEUS_METRICS_SINK_TOPIC))
                        .build();

        UsersPerMinuteJob usersPerMinuteJob =
                new UsersPerMinuteJob(impressionEventsStreamSource, prometheusMetricsKafkaSink);
        usersPerMinuteJob.execute(env);
    }

    private FilterFunction<JumboEvent> getFilterEventFunction() {
        return new FilterFunction<>() {
            private static final long serialVersionUID = 1L;
            private final String[] FILTER_EVENTS = {
                "App Launch",
                "App Launch Traits",
                "Checkout Step Viewed",
                "Cart Viewed",
                "Search Results Viewed",
                "Product Added",
                "Homepage Visit",
                "Cart Footer Strip Clicked"
            };

            @Override
            public boolean filter(JumboEvent event) {
                try {
                    // Basic null checks
                    if (event == null || event.getTraits() == null) {
                        return false;
                    }

                    // Check for required fields
                    String cityName = event.getTraits().getCityName();
                    if (cityName == null || cityName.isEmpty() || cityName.equalsIgnoreCase("null")) {
                        return false;
                    }

                    // Allow if hostAppType is null or equals "blinkit"
                    if (event.getTraits().getHostAppType() != null &&
                            !event.getTraits().getHostAppType().toLowerCase().equals("blinkit")) {
                        return false;
                    }

                    // Check for valid unique ID
                    String uniqueId = getUniqueId(event);
                    if (uniqueId == null || uniqueId.isEmpty() || uniqueId.equalsIgnoreCase("null")) {
                        return false;
                    }

                    // Check if event name is in the filter list
                    String eventName = event.getEventName();
                    if (eventName == null) {
                        return false;
                    }

                    for (String filterEvent : FILTER_EVENTS) {
                        if (eventName.equalsIgnoreCase(filterEvent)) {
                            return true;
                        }
                    }
                } catch (Exception e) {
                    // Log the error and filter out the problematic event
                    System.err.println("Error processing event: " + e.getMessage());
                    return false;
                }
                return false;
            }
        };
    }

    private static String getChannel(String eventChannel) {
        return eventChannel != null ? eventChannel : "blinkit";
    }

    public static String getUniqueId(JumboEvent event) {
        if (event.getSource().equalsIgnoreCase("Android")) {
            return event.getTraits().getDeviceUuid();
        } else if (event.getSource().equalsIgnoreCase("iOS")) {
            return event.getDeviceId();
        } else { // Modify the logic here if we want to include Web DAU too
            return "null";
        }
    }

    public JobExecutionResult execute(StreamExecutionEnvironment environment) throws Exception {

        DataStream<JumboEvent> filteredEvents =
                this.impressionEventsSource
                        .name("impression-event-source")
                        .uid("impression-events-source")
                        .filter(getFilterEventFunction())
                        .uid("filter-null-city-upm-event")
                        .name("filter-null-city-upm-event");

        // Pan-India level aggregation (by app only)
        DataStream<PrometheusMetric> panIndiaMetrics =
                filteredEvents
                        .keyBy(
                                new KeySelector<JumboEvent, String>() {
                                    @Override
                                    public String getKey(JumboEvent event) throws Exception {
                                        return getChannel(event.getTraits().getChannel());
                                    }
                                })
                        .window(TumblingEventTimeWindows.of(Time.minutes(1)))
                        .trigger(ContinuousEventTimeTrigger.of(Time.seconds(60)))
                        .process(new PanIndiaActiveUsersProcessFunction())
                        .name("pan-india-active-users-per-minute-window")
                        .uid("pan-india-active-users-per-minute-window");

        // City level aggregation (by app and city)
        DataStream<PrometheusMetric> cityMetrics =
                filteredEvents
                        .keyBy(
                                new KeySelector<JumboEvent, Tuple2<String, String>>() {
                                    @Override
                                    public Tuple2<String, String> getKey(JumboEvent event)
                                            throws Exception {
                                        return Tuple2.of(
                                                getChannel(event.getTraits().getChannel()),
                                                event.getTraits().getCityName());
                                    }
                                })
                        .window(TumblingEventTimeWindows.of(Time.minutes(1)))
                        .trigger(ContinuousEventTimeTrigger.of(Time.seconds(60)))
                        .process(new CityActiveUsersProcessFunction())
                        .name("city-active-users-per-minute-window")
                        .uid("city-active-users-per-minute-window");

        // Merchant level aggregation (by app, city, and merchant_id)
        DataStream<PrometheusMetric> merchantMetrics =
                filteredEvents
                        .filter(event -> event.getTraits().getMerchantId() != null)
                        .name("filter-null-merchant-events")
                        .uid("filter-null-merchant-events")
                        .keyBy(
                                new KeySelector<JumboEvent, Tuple3<String, String, Integer>>() {
                                    @Override
                                    public Tuple3<String, String, Integer> getKey(JumboEvent event)
                                            throws Exception {
                                        return Tuple3.of(
                                                getChannel(event.getTraits().getChannel()),
                                                event.getTraits().getCityName(),
                                                event.getTraits().getMerchantId());
                                    }
                                })
                        .window(TumblingEventTimeWindows.of(Time.minutes(1)))
                        .trigger(ContinuousEventTimeTrigger.of(Time.seconds(60)))
                        .process(new MerchantActiveUsersProcessFunction())
                        .name("merchant-active-users-per-minute-window")
                        .uid("merchant-active-users-per-minute-window");

        // Union all metrics streams
        DataStream<PrometheusMetric> allMetrics =
                panIndiaMetrics
                        .union(cityMetrics)
                        .union(merchantMetrics);

        allMetrics
                .map(
                        prometheusMetric -> {
                            return new KafkaMetricMessage(
                                    prometheusMetric.getMetricName(),
                                    "gauge",
                                    prometheusMetric.getMetricHelp(),
                                    prometheusMetric.getLabels(),
                                    Integer.valueOf(prometheusMetric.getMetricValue())
                                            .floatValue(),
                                    "blinkit",
                                    "observability",
                                    prometheusMetric.getMetricTimestamp());
                        })
                .name("prometheus-metric-map")
                .uid("prometheus-metric-map")
                .sinkTo(prometheusMetricsSink)
                .name("prometheus-metrics-sink")
                .uid("prometheus-metrics-sink");

        return environment.execute("observability.min-active-users");
    }

    // Pan-India level process function
    public static class PanIndiaActiveUsersProcessFunction
            extends ProcessWindowFunction<JumboEvent, PrometheusMetric, String, TimeWindow> {

        @Override
        public void process(
                String key,
                ProcessWindowFunction<JumboEvent, PrometheusMetric, String, TimeWindow>.Context
                        context,
                Iterable<JumboEvent> iterable,
                Collector<PrometheusMetric> collector) {
            Set<String> deviceIDs = new HashSet<>();
            for (JumboEvent e : iterable) {
                deviceIDs.add(getUniqueId(e));
            }

            Map<String, String> labels = new HashMap<>();
            labels.put("app", key);
            labels.put("level", "pan_india");

            collector.collect(
                    new PrometheusMetric(
                            "minute_active_users_pan_india",
                            "gauge",
                            "Number of active users per minute at pan-India level",
                            labels,
                            labels,
                            Integer.toString(deviceIDs.size()),
                            // Use window end time directly
                            context.window().getEnd()));
        }
    }

    // City level process function
    public static class CityActiveUsersProcessFunction
            extends ProcessWindowFunction<
                    JumboEvent, PrometheusMetric, Tuple2<String, String>, TimeWindow> {

        @Override
        public void process(
                Tuple2<String, String> key,
                ProcessWindowFunction<
                                        JumboEvent,
                                        PrometheusMetric,
                                        Tuple2<String, String>,
                                        TimeWindow>
                                .Context
                        context,
                Iterable<JumboEvent> iterable,
                Collector<PrometheusMetric> collector) {
            Set<String> deviceIDs = new HashSet<>();
            for (JumboEvent e : iterable) {
                deviceIDs.add(getUniqueId(e));
            }

            Map<String, String> labels = new HashMap<>();
            labels.put("app", key.f0);
            labels.put("city", key.f1);
            labels.put("level", "city");

            collector.collect(
                    new PrometheusMetric(
                            "minute_active_users_city",
                            "gauge",
                            "Number of active users per minute at city level",
                            labels,
                            labels,
                            Integer.toString(deviceIDs.size()),
                            // Use window end time directly
                            context.window().getEnd()));
        }
    }

    // Merchant level process function
    public static class MerchantActiveUsersProcessFunction
            extends ProcessWindowFunction<
                    JumboEvent, PrometheusMetric, Tuple3<String, String, Integer>, TimeWindow> {

        @Override
        public void process(
                Tuple3<String, String, Integer> key,
                ProcessWindowFunction<
                                        JumboEvent,
                                        PrometheusMetric,
                                        Tuple3<String, String, Integer>,
                                        TimeWindow>
                                .Context
                        context,
                Iterable<JumboEvent> iterable,
                Collector<PrometheusMetric> collector) {
            Set<String> deviceIDs = new HashSet<>();
            for (JumboEvent e : iterable) {
                deviceIDs.add(getUniqueId(e));
            }

            Map<String, String> labels = new HashMap<>();
            labels.put("app", key.f0);
            labels.put("city", key.f1);
            labels.put("merchant_id", key.f2.toString());
            labels.put("level", "merchant");

            collector.collect(
                    new PrometheusMetric(
                            "minute_active_users_merchant",
                            "gauge",
                            "Number of active users per minute at merchant level",
                            labels,
                            labels,
                            Integer.toString(deviceIDs.size()),
                            // Use window end time directly
                            context.window().getEnd()));
        }
    }
}
