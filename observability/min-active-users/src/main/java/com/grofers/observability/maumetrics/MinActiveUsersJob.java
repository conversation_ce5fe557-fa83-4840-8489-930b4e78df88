package com.grofers.observability.maumetrics;

import static java.time.Duration.ofSeconds;

import com.grofers.gandalf.events.JumboEvent;
import com.grofers.gandalf.serdes.JumboEventDeserializer;
import com.grofers.observability.maumetrics.configs.JobConfig;
import com.grofers.observability.maumetrics.configs.JobConfigManager;
import com.grofers.observability.maumetrics.datatypes.KafkaMetricMessage;
import com.grofers.observability.maumetrics.datatypes.PrometheusMetric;
import com.grofers.observability.maumetrics.serdes.KafkaMetricMessageSerializationSchema;

import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

import org.apache.flink.api.common.JobExecutionResult;
import org.apache.flink.api.common.eventtime.WatermarkStrategy;
import org.apache.flink.connector.base.DeliveryGuarantee;
import org.apache.flink.api.common.functions.FilterFunction;
import org.apache.flink.api.java.functions.KeySelector;
import org.apache.flink.api.java.tuple.Tuple2;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.connector.kafka.sink.KafkaSink;
import org.apache.flink.connector.kafka.source.KafkaSource;
import org.apache.flink.connector.kafka.source.enumerator.initializer.OffsetsInitializer;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.datastream.DataStreamSource;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.streaming.api.functions.windowing.ProcessWindowFunction;
import org.apache.flink.streaming.api.windowing.assigners.TumblingEventTimeWindows;
import org.apache.flink.streaming.api.windowing.time.Time;
import org.apache.flink.streaming.api.windowing.triggers.ContinuousEventTimeTrigger;
import org.apache.flink.streaming.api.windowing.windows.TimeWindow;
import org.apache.flink.util.Collector;
import org.apache.kafka.clients.consumer.OffsetResetStrategy;

/** Flink Streaming Job for calculating Minute Active Users */
public class MinActiveUsersJob {

    private final DataStreamSource<JumboEvent> impressionEventsSource;
    private final KafkaSink<KafkaMetricMessage> prometheusMetricsSink;

    public MinActiveUsersJob(
            DataStreamSource<JumboEvent> impressionEventsSource,
            KafkaSink<KafkaMetricMessage> prometheusMetricsSink) {
        this.impressionEventsSource = impressionEventsSource;
        this.prometheusMetricsSink = prometheusMetricsSink;
    }

    public static void main(String[] args) throws Exception {
        // set up the streaming execution environment
        final StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment();

        // Parameters for environment
        ParameterTool params = ParameterTool.fromArgs(args);
        String userEnv = params.getRequired("environment");

        // Set PropertiesPath for userEnv
        JobConfigManager.setJobConfigsPath(userEnv);
        // Get PropertiesPath for userEnv
        String jobPropertiesPath = JobConfigManager.getJobConfigsPath(userEnv);
        // Fetch job config
        JobConfigManager.setJobConfigs(env, jobPropertiesPath);
        JobConfigManager.getJobConfigs(env);

        // Set up checkpointing
        env.enableCheckpointing(60000);
        env.getCheckpointConfig().setCheckpointStorage(JobConfig.CHECKPOINTS_STORAGE_LOCATION);

        KafkaSource<JumboEvent> impressionEventsSource =
                KafkaSource.<JumboEvent>builder()
                        .setBootstrapServers(JobConfig.EVENTS_SOURCE_BROKERS)
                        .setGroupId(JobConfig.EVENTS_SOURCE_CONSUMER_GROUP_ID)
                        .setClientIdPrefix("min_active_users-")
                        .setValueOnlyDeserializer(new JumboEventDeserializer())
                        .setStartingOffsets(
                                OffsetsInitializer.committedOffsets(OffsetResetStrategy.LATEST))
                        .setTopics(JobConfig.EVENTS_SOURCE_TOPIC)
                        .setProperty("partition.discovery.interval.ms", "600000")
                        .setProperty("reconnect.backoff.max.ms", "300000")
                        .setProperty("reconnect.backoff.ms", "60000")
                        .build();

        WatermarkStrategy<JumboEvent> mauEventsSourceWatermarkStrategy =
                WatermarkStrategy.<JumboEvent>forBoundedOutOfOrderness(ofSeconds(300))
                        .withIdleness(ofSeconds(JobConfig.IDLENESS_TIME_IN_SECS))
                        .withTimestampAssigner(
                                (event, timestamp) -> event.getEventTimestamp() * 1000);

        DataStreamSource<JumboEvent> impressionEventsStreamSource =
                env.fromSource(
                        impressionEventsSource,
                        mauEventsSourceWatermarkStrategy,
                        "impressionEventsSource");

        KafkaSink<KafkaMetricMessage> prometheusMetricsKafkaSink =
                KafkaSink.<KafkaMetricMessage>builder()
                        .setBootstrapServers(JobConfig.PROMETHEUS_METRICS_SINK_BROKERS)
                        .setDeliverGuarantee(DeliveryGuarantee.AT_LEAST_ONCE)
                        .setRecordSerializer(
                                new KafkaMetricMessageSerializationSchema(
                                        JobConfig.PROMETHEUS_METRICS_SINK_TOPIC))
                        .build();

        MinActiveUsersJob minActiveUsersJob =
                new MinActiveUsersJob(
                        impressionEventsStreamSource,
                        prometheusMetricsKafkaSink);
        minActiveUsersJob.execute(env);
    }

    private FilterFunction<JumboEvent> getFilterEventFunction() {
        return new FilterFunction<>() {
            private final String[] FILTER_EVENTS = {
                "App Launch",
                "App Launch Traits",
                "Checkout Step Viewed",
                "Cart Viewed",
                "Search Results Viewed",
                "Product Added",
                "Homepage Visit",
                "Cart Footer Strip Clicked"
            };

            @Override
            public boolean filter(JumboEvent event) throws Exception {
                if (event.getTraits() == null
                        || event.getTraits().getCityName() == null
                        || event.getTraits().getCityName().isEmpty()
                        || event.getTraits().getCityName().equalsIgnoreCase("null")
                        || getUniqueId(event).equalsIgnoreCase("null")) {
                    return false;
                }

                for (String filterEvent : FILTER_EVENTS) {
                    if (event.getEvent().equalsIgnoreCase(filterEvent)) {
                        return true;
                    }
                }
                return false;
            }
        };
    }

    public static String getChannel(String channel) {
        if (channel == null || channel.isEmpty()) {
            return "unknown";
        }
        return channel.toLowerCase();
    }

    public static String getUniqueId(JumboEvent event) {
        if (event.getSource().equalsIgnoreCase("Android")) {
            return event.getTraits().getDeviceUuid();
        } else if (event.getSource().equalsIgnoreCase("iOS")) {
            return event.getDeviceId();
        } else { // Modify the logic here if we want to include Web DAU too
            return "null";
        }
    }

    public JobExecutionResult execute(StreamExecutionEnvironment environment) throws Exception {

        DataStream<JumboEvent> filteredEvents =
                this.impressionEventsSource
                        .name("impression-event-source")
                        .uid("impression-events-source")
                        .filter(getFilterEventFunction())
                        .uid("filter-null-city-mau-event")
                        .name("filter-null-city-mau-event");

        DataStream<PrometheusMetric> prometheusMetrics =
                filteredEvents
                        .keyBy(
                                new KeySelector<JumboEvent, Tuple2<String, String>>() {
                                    @Override
                                    public Tuple2<String, String> getKey(JumboEvent event)
                                            throws Exception {
                                        return Tuple2.of(
                                                getChannel(event.getTraits().getChannel()),
                                                event.getTraits().getCityName());
                                    }
                                })
                        .window(TumblingEventTimeWindows.of(Time.minutes(1)))
                        .trigger(ContinuousEventTimeTrigger.of(Time.seconds(60)))
                        .evictor(UniqueUserEvictor.create())
                        .process(new MinuteActiveUsersProcessFunction())
                        .name("minute-active-users-window")
                        .uid("minute-active-users-window");

        prometheusMetrics
                .map(
                        prometheusMetric ->
                                new KafkaMetricMessage(
                                        prometheusMetric.getMetricName(),
                                        "gauge",
                                        prometheusMetric.getMetricHelp(),
                                        prometheusMetric.getLabels(),
                                        Integer.valueOf(prometheusMetric.getMetricValue())
                                                .floatValue(),
                                        "blinkit",
                                        "observability",
                                        prometheusMetric.getMetricTimestamp()))
                .name("prometheus-metric-map")
                .uid("prometheus-metric-map")
                .sinkTo(prometheusMetricsSink)
                .name("prometheus-metrics-sink")
                .uid("prometheus-metrics-sink");

        return environment.execute("observability.min-active-users");
    }

    public static class MinuteActiveUsersProcessFunction
            extends ProcessWindowFunction<
                    JumboEvent, PrometheusMetric, Tuple2<String, String>, TimeWindow> {

        @Override
        public void process(
                Tuple2<String, String> key,
                ProcessWindowFunction<
                                        JumboEvent,
                                        PrometheusMetric,
                                        Tuple2<String, String>,
                                        TimeWindow>
                                .Context
                        context,
                Iterable<JumboEvent> iterable,
                Collector<PrometheusMetric> collector) {
            Set<String> deviceIDs = new HashSet<>();
            for (JumboEvent e : iterable) {
                deviceIDs.add(getUniqueId(e));
            }

            Map<String, String> labels = new HashMap<>();
            labels.put("app", key.f0);
            labels.put("city", key.f1);

            collector.collect(
                    new PrometheusMetric(
                            "minute_active_users",
                            "gauge",
                            "Number of active users per minute",
                            labels,
                            labels,
                            Integer.toString(deviceIDs.size()),
                            context.window().getEnd()));
        }
    }
}
