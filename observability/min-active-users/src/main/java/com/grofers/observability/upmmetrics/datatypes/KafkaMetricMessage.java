package com.grofers.observability.upmmetrics.datatypes;


import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Value;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.annotation.JsonProperty;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.PropertyNamingStrategies;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.annotation.JsonNaming;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.annotation.JsonSerialize;

@JsonSerialize
@Value
@AllArgsConstructor
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class KafkaMetricMessage {
    String metricName;
    String metricType;
    String help;
    Map<String, String> labels;
    Float metricValue;
    String namespace;
    String subsystem;

    @JsonProperty("timestamp_ms")
    Long metricTimestamp;
}
