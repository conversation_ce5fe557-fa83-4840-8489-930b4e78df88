package com.grofers.observability.haumetrics;

import static com.grofers.observability.haumetrics.HAUMetrics.getUniqueId;

import com.grofers.gandalf.events.JumboEvent;
import java.util.HashSet;
import java.util.Iterator;
import java.util.Set;
import org.apache.flink.streaming.api.windowing.evictors.Evictor;
import org.apache.flink.streaming.api.windowing.windows.Window;
import org.apache.flink.streaming.runtime.operators.windowing.TimestampedValue;

public class UniqueUserEvictor<W extends Window> implements Evictor<JumboEvent, W> {
    @Override
    public void evictBefore(
            Iterable<TimestampedValue<JumboEvent>> iterable,
            int i,
            W w,
            EvictorContext evictorContext) {}

    @Override
    public void evictAfter(
            Iterable<TimestampedValue<JumboEvent>> iterable,
            int i,
            W w,
            EvictorContext evictorContext) {
        this.evict(iterable, evictorContext);
    }

    private void evict(Iterable<TimestampedValue<JumboEvent>> elements, EvictorContext ctx) {
        Set<String> uniqueIds = new HashSet<>();
        Iterator<TimestampedValue<JumboEvent>> iterator = elements.iterator();
        while (iterator.hasNext()) {
            JumboEvent event = iterator.next().getValue();
            String uniqueId = getUniqueId(event);
            if (uniqueIds.contains(uniqueId)) {
                iterator.remove();
            } else {
                uniqueIds.add(uniqueId);
            }
        }
    }

    public static <W extends Window> UniqueUserEvictor<W> create() {
        return new UniqueUserEvictor<>();
    }
}
