package com.grofers.observability.haumetrics.configs;

public final class JobConfig {
    public static String CHECKPOINTS_STORAGE_LOCATION;
    public static String EVENTS_SOURCE_BROKERS;
    public static String EVENTS_SOURCE_CONSUMER_GROUP_ID;
    public static String[] EVENTS_SOURCE_TOPIC;
    public static int IDLENESS_TIME_IN_SECS;
    public static int ALLOWED_LATENESS_IN_SECS;
    public static String MERCHANT_HAU_METRICS_SINK_BROKERS;
    public static String MERCHANT_HAU_METRICS_SINK_TOPIC;
    public static String CITY_HAU_METRICS_SINK_BROKERS;
    public static String CITY_HAU_METRICS_SINK_TOPIC;

    public JobConfig(
            String CHECKPOINTS_STORAGE_LOCATION,
            String EVENTS_SOURCE_BROKERS,
            String EVENTS_SOURCE_CONSUMER_GROUP_ID,
            String EVENTS_SOURCE_TOPIC,
            int IDLENESS_TIME_IN_SECS,
            int ALLOWED_LATENESS_IN_SECS,
            String MERCHANT_HAU_METRICS_SINK_BROKERS,
            String MERCHANT_HAU_METRICS_SINK_TOPIC,
            String CITY_HAU_METRICS_SINK_BROKERS,
            String CITY_HAU_METRICS_SINK_TOPIC) {

        JobConfig.CHECKPOINTS_STORAGE_LOCATION = CHECKPOINTS_STORAGE_LOCATION;
        JobConfig.EVENTS_SOURCE_BROKERS = EVENTS_SOURCE_BROKERS;
        JobConfig.EVENTS_SOURCE_CONSUMER_GROUP_ID = EVENTS_SOURCE_CONSUMER_GROUP_ID;
        JobConfig.EVENTS_SOURCE_TOPIC = EVENTS_SOURCE_TOPIC.split(",");
        JobConfig.IDLENESS_TIME_IN_SECS = IDLENESS_TIME_IN_SECS;
        JobConfig.ALLOWED_LATENESS_IN_SECS = ALLOWED_LATENESS_IN_SECS;
        JobConfig.MERCHANT_HAU_METRICS_SINK_BROKERS = MERCHANT_HAU_METRICS_SINK_BROKERS;
        JobConfig.MERCHANT_HAU_METRICS_SINK_TOPIC = MERCHANT_HAU_METRICS_SINK_TOPIC;
        JobConfig.CITY_HAU_METRICS_SINK_BROKERS = CITY_HAU_METRICS_SINK_BROKERS;
        JobConfig.CITY_HAU_METRICS_SINK_TOPIC = CITY_HAU_METRICS_SINK_TOPIC;
    }
}
