
# Properties for prod environment

#Checkpoints
CHECKPOINTS_STORAGE_LOCATION = s3a://prod-data-flink-states/flink-streams/observability/hourly-active-users-v2/checkpoints/

# Events Source
EVENTS_SOURCE_BROKERS = ****************************************.groupblinkit-agent.kafka.discoveryv2.prod-y.ap-southeast-1.warpstream.com:9092
EVENTS_SOURCE_CONSUMER_GROUP_ID = flink-observability-hourly-active-users-consumer_group-v2
EVENTS_SOURCE_TOPIC = jumbo_transformed.blinkit.click_events

#Watermark Idleness Configs
IDLENESS_TIME_IN_SECS = 600
ALLOWED_LATENESS_IN_SECS = 600

CITY_HAU_METRICS_SINK_BROKERS = b-1.prod-data-corestr.uynfnv.c4.kafka.ap-southeast-1.amazonaws.com:9092,b-2.prod-data-corestr.uynfnv.c4.kafka.ap-southeast-1.amazonaws.com:9092,b-3.prod-data-corestr.uynfnv.c4.kafka.ap-southeast-1.amazonaws.com:9092
CITY_HAU_METRICS_SINK_TOPIC = observability.metrics.city-hourly-active-users-v2

MERCHANT_HAU_METRICS_SINK_BROKERS = b-1.prod-data-corestr.uynfnv.c4.kafka.ap-southeast-1.amazonaws.com:9092,b-2.prod-data-corestr.uynfnv.c4.kafka.ap-southeast-1.amazonaws.com:9092,b-3.prod-data-corestr.uynfnv.c4.kafka.ap-southeast-1.amazonaws.com:9092
MERCHANT_HAU_METRICS_SINK_TOPIC = observability.metrics.merchant-hourly-active-users-v2
