FROM public.ecr.aws/zomato/flink:1.15.2-java11

RUN wget -P $FLINK_HOME/lib/ https://repo.maven.apache.org/maven2/org/apache/flink/flink-json/1.15.2/flink-json-1.15.2.jar; \
    wget -P $FLINK_HOME/lib/ https://repo.maven.apache.org/maven2/org/apache/flink/flink-s3-fs-hadoop/1.15.2/flink-s3-fs-hadoop-1.15.2.jar; \
    wget -P $FLINK_HOME/lib/ https://repo.maven.apache.org/maven2/org/apache/flink/flink-statebackend-rocksdb/1.15.2/flink-statebackend-rocksdb-1.15.2.jar; \
    wget -P $FLINK_HOME/lib/ https://repo1.maven.org/maven2/org/apache/kafka/kafka-clients/2.8.0/kafka-clients-2.8.0.jar;

RUN mkdir -p $FLINK_HOME/usrlib

COPY src/main/resources /opt/flink/src/main/resources

COPY target/hourly-active-users-1.0.jar /opt/flink/usrlib/hourly-active-users-v2.jar
