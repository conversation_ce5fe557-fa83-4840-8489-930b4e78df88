name: Fact Order Item Details
image: 442534439095.dkr.ecr.ap-southeast-1.amazonaws.com/data/flink-streams/observability/fact-order-item-details
flink_config:
  taskmanager.memory.process.size: 8192m  
  jobmanager.memory.process.size: 3072m 
  taskmanager.memory.managed.size: 0

  taskmanager.numberOfTaskSlots: 4       
  parallelism.default: 6                 

  taskmanager.memory.network.fraction: 0.15    
  taskmanager.memory.jvm-overhead.fraction: 0.1 


  process.working-dir: fact-order-item-details/process
  state.backend.local-recovery: true
  taskmanager.resource-id: TaskManager_factorderitemdetails

  env.java.opts.taskmanager: "-XX:+UseG1GC -XX:MaxGCPauseMillis=100 -XX:+UnlockExperimentalVMOptions -XX:+UseCGroupMemoryLimitForHeap"
  env.java.opts.jobmanager: "-XX:+UseG1GC -XX:MaxGCPauseMillis=100"
deployment_config:
  kubernetes.rest-service.exposed.type: NodePort
  metrics.reporters: prom
  metrics.reporter.prom.factory.class: org.apache.flink.metrics.prometheus.PrometheusReporterFactory
allow_non_restored_state: False # Set to True when changes are made to Operator
codeartifact_access: True
secrets:
  master:
    # Redis configuration for product enrichment caching
    REDIS_HOST_STAGE: "data/services/order-lifecycle-streams/elasticache-redis:REDIS_HOST"
    REDIS_HOST_PROD: "data/services/order-lifecycle-streams/elasticache-redis:REDIS_HOST"
    REDIS_DB_STAGE: "data/services/order-lifecycle-streams/elasticache-redis:REDIS_DB"
    REDIS_DB_PROD: "data/services/order-lifecycle-streams/elasticache-redis:REDIS_DB"

    # CMS API configuration for product details
    CMS_API_URL_STAGE: "data/services/order-lifecycle-streams/cms:CMS_SERVICE_URL"
    CMS_API_URL_PROD: "data/services/order-lifecycle-streams/cms:CMS_SERVICE_URL"
    CMS_API_AUTHORIZATION_STAGE: "data/services/order-lifecycle-streams/cms:CMS_SERVICE_KEY"
    CMS_API_AUTHORIZATION_PROD: "data/services/order-lifecycle-streams/cms:CMS_SERVICE_KEY"

    # Product Information API configuration for product details (brand, name, type, categories)
    PI_SERVICE_URL_STAGE: "data/services/order-lifecycle-streams/product-information:PI_SERVICE_URL"
    PI_SERVICE_URL_PROD: "data/services/order-lifecycle-streams/product-information:PI_SERVICE_URL"
    PI_SERVICE_AUTHORIZATION_STAGE: "data/services/order-lifecycle-streams/product-information:PI_SERVICE_KEY"
    PI_SERVICE_AUTHORIZATION_PROD: "data/services/order-lifecycle-streams/product-information:PI_SERVICE_KEY"

  taskmanager:
    # Redis configuration for product enrichment caching (TaskManager needs these for enrichment function)
    REDIS_HOST_STAGE: "data/services/order-lifecycle-streams/elasticache-redis:REDIS_HOST"
    REDIS_HOST_PROD: "data/services/order-lifecycle-streams/elasticache-redis:REDIS_HOST"
    REDIS_DB_STAGE: "data/services/order-lifecycle-streams/elasticache-redis:REDIS_DB"
    REDIS_DB_PROD: "data/services/order-lifecycle-streams/elasticache-redis:REDIS_DB"

    # CMS API configuration for product details (TaskManager needs these for enrichment function)
    CMS_API_URL_STAGE: "data/services/order-lifecycle-streams/cms:CMS_SERVICE_URL"
    CMS_API_URL_PROD: "data/services/order-lifecycle-streams/cms:CMS_SERVICE_URL"
    CMS_API_AUTHORIZATION_STAGE: "data/services/order-lifecycle-streams/cms:CMS_SERVICE_KEY"
    CMS_API_AUTHORIZATION_PROD: "data/services/order-lifecycle-streams/cms:CMS_SERVICE_KEY"

    # Product Information API configuration for product details (brand, name, type, categories)
    PI_SERVICE_URL_STAGE: "data/services/order-lifecycle-streams/product-information:PI_SERVICE_URL"
    PI_SERVICE_URL_PROD: "data/services/order-lifecycle-streams/product-information:PI_SERVICE_URL"
    PI_SERVICE_AUTHORIZATION_STAGE: "data/services/order-lifecycle-streams/product-information:PI_SERVICE_KEY"
    PI_SERVICE_AUTHORIZATION_PROD: "data/services/order-lifecycle-streams/product-information:PI_SERVICE_KEY"
