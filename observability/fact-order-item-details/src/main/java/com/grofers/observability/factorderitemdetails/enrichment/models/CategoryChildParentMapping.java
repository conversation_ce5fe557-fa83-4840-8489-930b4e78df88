package com.grofers.observability.factorderitemdetails.enrichment.models;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.ArrayList;
import java.util.List;

/** Category child-parent mapping model for enrichment: */
@JsonIgnoreProperties(ignoreUnknown = true)
public class CategoryChildParentMapping {
    @JsonProperty("categories")
    private List<Category> categories;

    // Default constructor
    public CategoryChildParentMapping() {
        this.categories = new ArrayList<>();
    }

    // Constructor with categories
    public CategoryChildParentMapping(List<Category> categories) {
        this.categories = categories != null ? categories : new ArrayList<>();
    }

    // Getters and Setters
    public List<Category> getCategories() {
        return categories;
    }

    public void setCategories(List<Category> categories) {
        this.categories = categories;
    }

    @Override
    public String toString() {
        return "CategoryChildParentMapping{" + "categories=" + categories + '}';
    }
}
