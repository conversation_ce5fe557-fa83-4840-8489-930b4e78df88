package com.grofers.observability.factorderitemdetails;

public class Queries {
    public static final String BLINKIT_ORDER_LIFECYCLE_EVENTS =
            "CREATE TABLE IF NOT EXISTS order_lifecycle_events ( order_id INT, `order` ROW(    "
                + " datetime_created BIGINT,     datetime_updated BIGINT,     current_state_name"
                + " VARCHAR(50),     device_id STRING,     additional_charges_data ARRAY<ROW<     "
                + "    `id` INT,         `name` STRING,         `amount` DOUBLE,        "
                + " `charge_id` INT     >>,     source STRING,     actual_merchant_id INT,    "
                + " cart_id BIGINT,     total_cost DOUBLE,     delivery_cost DOUBLE,    "
                + " offer_details ROW(`total_discount` DOUBLE),     net_cost DOUBLE,    "
                + " procurement_amount DOUBLE,     wallet_amount DOUBLE,     items ARRAY<ROW(     "
                + "    `procured_quantity` INT,         `quantity` INT,         `cancellations`"
                + " ARRAY<ROW<             `reason` STRING,             `quantity` INT         >>,"
                + "         `categories` ARRAY<STRING>,         `product_id` INT,         `name`"
                + " STRING,         `cost` DOUBLE     )>,     order_events ARRAY<ROW(        "
                + " `event_state_change_to` STRING,         `event_type` STRING,        "
                + " `timestamp` STRING,         `extra` ROW(             `meta` ROW(              "
                + "   `batched_order_ids` ARRAY<STRING>,                 `picking_end_time` STRING"
                + "             ),             `partner_emp_id` STRING,             `picker_imei`"
                + " STRING,             `picker_name` STRING,             `timestamp` STRING,     "
                + "        `assignment_queued_ts` STRING         )     )>,     `slot_properties`"
                + " ROW(         `tags` ARRAY<STRING>,         `slot_charge` DOUBLE,        "
                + " `checkout_properties` ROW(             `slot_charge` DOUBLE         ),        "
                + " `serviceability` ROW(             `eta` INT,            "
                + " `serviceability_reason` STRING,             `surge_charge_v2` ROW(            "
                + "     `source` STRING,                 `surge_amount` DOUBLE             ),     "
                + "        `session_id` STRING,             `components` ARRAY<ROW(               "
                + "  `name` STRING,                 `duration` DOUBLE             )>         )    "
                + " ),     additional_charges_amount DOUBLE,     `city` STRING,    "
                + " checkout_merchant_name STRING,     merchant ROW(`city_id` INT),     customer"
                + " ROW(`id` INT),     cart ROW(`orders` ARRAY<ROW(         `id` INT,        "
                + " `type` VARCHAR(63)     )>),     org_channel_id VARCHAR(10),    "
                + " cart_rank_details ROW(`cart_rank_last_6m` INT,         `order_count_life_time`"
                + " INT) ), reason_code VARCHAR(50), flink_event_time_epoch AS"
                + " TO_TIMESTAMP_LTZ(`order`.datetime_updated, 0), WATERMARK FOR"
                + " flink_event_time_epoch AS flink_event_time_epoch - INTERVAL '60' seconds )"
                + " WITH (     'connector' = 'kafka',     'format' = 'json',     'topic' = '%s',  "
                + "   'properties.bootstrap.servers' = '%s',     'properties.group.id' = '%s',    "
                + " 'properties.auto.offset.reset' = 'latest',     'scan.startup.mode' ="
                + " 'group-offsets' )";

    public static final String ORDER_FACT_ITEM_METRIC =
            " INSERT INTO fact_order_item_details          SELECT o.order_id,"
                + " o.`order`.datetime_created*1000 AS insert_timestamp_epoch,       "
                + " o.`order`.datetime_updated*1000 AS update_timestamp_epoch,    "
                + " o.`order`.`customer`.id,  o.`order`.current_state_name,       "
                + " o.`order`.delivery_cost,   o.`order`.`offer_details`.`total_discount`,        "
                + " o.`order`.net_cost AS net_order_cost , o.`order`.procurement_amount,       "
                + " GetTypeOfOrderFunction(o.`order`.cart.orders, o.order_id) AS TYPE,       "
                + " o.`order`.cart_id,         o.`order`.actual_merchant_id AS merchant_id,       "
                + " o.`order`.wallet_amount,  o.`order`.additional_charges_amount,       "
                + " o.`order`.merchant.city_id AS city_id,  o.`order`.city AS city_name,       "
                + " o.`order`.checkout_merchant_name AS merchant_name, i.name as item_name,"
                + " i.product_id as item_product_id, i.cost as total_item_cost, i.quantity as"
                + " item_quantity, o.`order`.slot_properties.slot_charge AS slot_charge,"
                + " o.`order`.slot_properties.checkout_properties.slot_charge AS"
                + " checkout_slot_charge,         o.device_id AS device_id,  o.source AS SOURCE,  "
                + "   o.`order`.org_channel_id,  o.`order`.cart_rank_details.cart_rank_last_6m AS"
                + " cart_rank_last_6m,  o.`order`.cart_rank_details.order_count_life_time AS"
                + " order_count_life_time,  o.reason_code,"
                + " GetPickerDetailsFromOrderEventsFunction(o.`order`.order_events,"
                + " 'picker_imei','retail_picker_assignment', false) AS picker_employee_id,"
                + " GetPickerDetailsFromOrderEventsFunction(o.`order`.order_events, 'picker_name',"
                + " 'retail_picker_assignment', false) AS picker_name,"
                + " CAST(GetPickerDetailsFromOrderEventsFunction(o.`order`.order_events,"
                + " 'timestamp', 'picking_started', true) AS BIGINT) AS picking_start_time,"
                + " CAST(GetPickerDetailsFromOrderEventsFunction(o.`order`.order_events,"
                + " 'picking_end_time', 'BILLED', true) AS BIGINT) AS pick_completion_time,"
                + " CAST(GetPickerDetailsFromOrderEventsFunction(o.`order`.order_events,"
                + " 'assignment_queued_ts', 'retail_picklist_ready_to_assign', true) AS BIGINT) AS"
                + " picker_assignment_queued_time,"
                + " o.`order`.slot_properties.serviceability.serviceability_reason AS"
                + " serviceability_reason,  o.`order`.slot_properties.serviceability.eta AS"
                + " eta_shown, GetTimeStampFromOrderEventsFunction(o.`order`.order_events,"
                + " 'BILLED') AS order_billed_timestamp,"
                + " GetTimeStampFromOrderEventsFunction(o.`order`.order_events,"
                + " 'delivery_partner_assigned') AS rider_assigned_timestamp,"
                + " GetTimeStampFromOrderEventsFunction(o.`order`.order_events, 'DELIVERED') AS"
                + " delivery_timestamp,"
                + " GetTimeStampFromOrderEventsFunction(o.`order`.order_events,"
                + " 'retail_picker_assignment') AS picker_assigned_timestamp,"
                + " GetTimeStampFromOrderEventsFunction(o.`order`.order_events, 'ENROUTE') AS"
                + " order_enroute_timestamp,"
                + " GetTimeStampFromOrderEventsFunction(o.`order`.order_events, 'CREATED') AS"
                + " checkout_timestamp,"
                + " GetTimeStampFromOrderEventsFunction(o.`order`.order_events, 'APPROVED') AS"
                + " approved_timestamp,"
                + " GetPartnerEmpIdFromOrderEventsFunction(o.`order`.order_events) AS"
                + " delivery_fe_id,"
                + " GetIfBatchedOrderFromOrderEventsFunction(o.`order`.order_events) AS"
                + " is_batched_order, GetFillRateFunction(o.`order`.items, TRUE) AS fill_rate,    "
                + " GetFillRateFunction(o.`order`.items, FALSE) AS sm_fill_rate,"
                + " GetIfTagExistsOrderFunction(o.`order`.slot_properties, 'paas') AS"
                + " is_paas_order,  GetIfTagExistsOrderFunction(o.`order`.slot_properties,"
                + " 'defer_picking') AS is_defer_picking,"
                + " GetIfTagExistsOrderFunction(o.`order`.slot_properties, 'unicorn') AS"
                + " is_unicorn_order,"
                + " o.`order`.slot_properties.serviceability.surge_charge_v2.source AS surge_type,"
                + " GetTaskDuration(o.`order`.`slot_properties`.serviceability, ARRAY['picking',"
                + " 'billing', 'picker_eta']) AS instore_eta,"
                + " GetTaskDuration(o.`order`.`slot_properties`.serviceability, ARRAY['travel',"
                + " 'buffer', 'field_executive_eta']) AS outstore_eta,"
                + " GetTimeStampFromOrderEventsFunction(o.`order`.order_events,"
                + " 'reached_doorstep') AS reached_doorstep_timestamp,"
                + " o.`order`.slot_properties.serviceability.surge_charge_v2.surge_amount AS"
                + " surge_amount,         session_id AS session_id,"
                + " ExtractAdditionalChargeAmount(o.additional_charges_data, 3) AS"
                + " handling_charge,  ExtractAdditionalChargeAmount(o.additional_charges_data, 4)"
                + " AS convenience_charge,       "
                + " ExtractAdditionalChargeAmount(o.additional_charges_data, 5) AS night_charge,"
                + " ExtractAdditionalChargeAmount(o.additional_charges_data, 7) AS"
                + " small_cart_charge,  enrichment.l0_category,  enrichment.l0_category_id, "
                + " enrichment.l1_category,  enrichment.l1_category_id,  enrichment.l2_category, "
                + " enrichment.l2_category_id,  enrichment.brand_name,  enrichment.product_name, "
                + " enrichment.product_type  FROM  order_lifecycle_events AS o ,  LATERAL"
                + " TABLE(ExplodeOrderItemsFunction(o.`order`.items)) AS i(name, product_id, cost,"
                + " quantity),  LATERAL TABLE(ProductEnrichmentFunction(i.product_id)) AS"
                + " enrichment(l0_category, l0_category_id, l1_category, l1_category_id,"
                + " l2_category, l2_category_id, brand_name, product_name, product_type)  WHERE  ("
                + " GetTypeOfOrderFunction(o.`order`.cart.orders, o.order_id) IS NULL OR "
                + " GetTypeOfOrderFunction(o.`order`.cart.orders, o.order_id) IN "
                + " ('RetailForwardOrder') )    AND o.`order`.city NOT IN ('Not in service area', "
                + " 'Hapur', 'test1207898732')    AND o.`order`.city NOT LIKE '%B2B%' ";

    public static final String ORDER_ITEM_FACT_CREATE_TABLE =
            "CREATE TABLE IF NOT EXISTS fact_order_item_details   (  "
                    + " id INT,     "
                    + " insert_timestamp BIGINT,    "
                    + " update_timestamp BIGINT,     "
                    + " customer_id INT,       "
                    + " current_status VARCHAR(50),      "
                    + " delivery_cost DOUBLE,     "
                    + " discount DOUBLE,     "
                    + " net_order_cost DOUBLE,     "
                    + " procurement_amount DOUBLE,     "
                    + " type VARCHAR(63),     "
                    + " cart_id BIGINT,     "
                    + " merchant_id BIGINT,     "
                    + " wallet_amount  DOUBLE,     "
                    + " additional_charges_amount DOUBLE,     "
                    + " city_id INT,     "
                    + " city_name STRING,     "
                    + " merchant_name STRING,      "
                    + " item_name STRING,"
                    + " item_product_id INT,"
                    + " total_item_cost DOUBLE, "
                    + " item_quantity INT,"
                    + " slot_charge DOUBLE,     "
                    + " checkout_slot_charge DOUBLE,    "
                    + " device_id STRING,"
                    + " source STRING,     "
                    + " org_channel_id VARCHAR(10),     "
                    + " cart_rank_last_6m INT, "
                    + " order_count_life_time INT, "
                    + " reason_code VARCHAR(50),  "
                    + " picker_employee_id STRING, "
                    + " picker_name STRING, "
                    + " picking_start_time BIGINT,  "
                    + " pick_completion_time BIGINT, "
                    + " picker_assignment_queued_time BIGINT,  "
                    + " serviceability_reason STRING,     "
                    + " eta_shown BIGINT,     "
                    + " order_billed_timestamp BIGINT,     "
                    + " rider_assigned_timestamp BIGINT,     "
                    + " delivery_timestamp BIGINT,     "
                    + " picker_assigned_timestamp BIGINT,  "
                    + " order_enroute_timestamp BIGINT,     "
                    + " checkout_timestamp BIGINT,    "
                    + " approved_timestamp BIGINT,      "
                    + " delivery_fe_id STRING, "
                    + " is_batched_order BOOLEAN, "
                    + " fill_rate DOUBLE, "
                    + " sm_fill_rate DOUBLE, "
                    + " is_paas_order BOOLEAN,"
                    + " is_defer_picking BOOLEAN, "
                    + " is_unicorn_order BOOLEAN, "
                    + " surge_type STRING,"
                    + " instore_eta DOUBLE, "
                    + " outstore_eta DOUBLE, "
                    + " reached_doorstep_timestamp BIGINT,  "
                    + " surge_amount DOUBLE, "
                    + " session_id STRING, "
                    + " handling_charge DOUBLE,  "
                    + " convenience_charge DOUBLE, "
                    + " night_charge DOUBLE, "
                    + " small_cart_charge DOUBLE, "
                    + " l0_category STRING, "
                    + " l0_category_id INT, "
                    + " l1_category STRING, "
                    + " l1_category_id INT, "
                    + " l2_category STRING, "
                    + " l2_category_id INT, "
                    + " brand_name STRING, "
                    + " product_name STRING, "
                    + " product_type STRING "
                    + " ) "
                    + " WITH (    "
                    + "   'connector' = 'kafka',   "
                    + "   'topic' = '%s',    "
                    + "   'properties.bootstrap.servers' = '%s',    "
                    + "   'key.format' =  'json',    "
                    + "   'value.format' = 'json',   "
                    + "   'key.fields' = 'cart_id',  "
                    + "   'sink.partitioner' = 'default'  "
                    + " )  ";
}
