package com.grofers.observability.factorderitemdetails.enrichment.api;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.grofers.observability.factorderitemdetails.enrichment.exceptions.CriticalEnrichmentException;
import com.grofers.observability.factorderitemdetails.enrichment.models.ProductRequest;
import com.grofers.observability.factorderitemdetails.enrichment.models.ProductResponse;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.Closeable;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.concurrent.CompletableFuture;

/**
 * Product Information (PI) Service API client for fetching product details.
 *
 * <p>This client handles HTTP communication with the PI service to retrieve product information
 * including brand names, product names, product types, and category hierarchies.
 *
 * <p>Features: - Automatic retry logic with exponential backoff - Configurable timeouts and
 * connection settings - Proper resource management and connection cleanup - Fail-fast error
 * handling for data quality
 *
 * <p>API Details: - Endpoint: /api/v1/products - Method: POST - Request format: {"ids":
 * [product_id1, product_id2, ...]} - Response format: {"products": [{"id": ..., "brand": ...,
 * "type": ..., "category_hierarchy": ...}]}
 */
public class PiServiceClient implements Closeable {
    private static final Logger LOG = LoggerFactory.getLogger(PiServiceClient.class);

    private final PiServiceConfig config;
    private final CloseableHttpClient httpClient;
    private final ObjectMapper objectMapper;
    private final RequestConfig requestConfig;

    public PiServiceClient(PiServiceConfig config) {
        this.config = config;
        this.objectMapper = new ObjectMapper();
        this.requestConfig =
                RequestConfig.custom()
                        .setConnectTimeout(config.getConnectionTimeout())
                        .setSocketTimeout(config.getSocketTimeout())
                        .build();
        this.httpClient = HttpClients.custom().setDefaultRequestConfig(requestConfig).build();

        LOG.info("PI Service client initialized with config: {}", config);
    }

    /**
     * Get product details from PI Service API
     *
     * @param productRequest Product request with product IDs
     * @return CompletableFuture with ProductResponse
     */
    public CompletableFuture<ProductResponse> getProductDetails(ProductRequest productRequest) {
        return CompletableFuture.supplyAsync(
                () -> {
                    String url = config.getBaseUrl() + "/api/v1/products";

                    try {
                        String requestBody = objectMapper.writeValueAsString(productRequest);
                        LOG.debug(
                                "Calling PI Service API for product details: {} with body: {}",
                                url,
                                requestBody);

                        HttpPost httpPost = new HttpPost(url);
                        httpPost.setHeader("Content-Type", "application/json");
                        if (config.getAuthorization() != null
                                && !config.getAuthorization().isEmpty()) {
                            httpPost.setHeader("Authorization", config.getAuthorization());
                        }
                        httpPost.setEntity(new StringEntity(requestBody, StandardCharsets.UTF_8));

                        return executeWithRetry(
                                () -> {
                                    HttpResponse response = httpClient.execute(httpPost);
                                    return handleProductResponse(response);
                                });

                    } catch (Exception e) {
                        LOG.error("Error calling PI Service API for product details", e);
                        return new ProductResponse(); // Return empty response as fallback
                    }
                });
    }

    private ProductResponse handleProductResponse(HttpResponse response) throws IOException {
        int statusCode = response.getStatusLine().getStatusCode();
        HttpEntity entity = response.getEntity();

        if (statusCode == 200 && entity != null) {
            String responseBody = EntityUtils.toString(entity, StandardCharsets.UTF_8);
            LOG.debug("PI Service API product response: {}", responseBody);
            return objectMapper.readValue(responseBody, ProductResponse.class);
        } else {
            String errorBody =
                    entity != null
                            ? EntityUtils.toString(entity, StandardCharsets.UTF_8)
                            : "No response body";
            throw new CriticalEnrichmentException(
                    String.format(
                            "PI Service API returned non-200 status for products: %d, body: %s",
                            statusCode, errorBody));
        }
    }

    private <T> T executeWithRetry(ApiCall<T> apiCall) throws Exception {
        Exception lastException = null;

        for (int attempt = 1; attempt <= config.getMaxRetries(); attempt++) {
            try {
                return apiCall.call();
            } catch (Exception e) {
                lastException = e;
                LOG.warn("PI Service API call attempt {} failed: {}", attempt, e.getMessage());

                if (attempt < config.getMaxRetries()) {
                    try {
                        Thread.sleep(config.getRetryDelay() * attempt); // Exponential backoff
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        throw new RuntimeException("Interrupted during retry delay", ie);
                    }
                }
            }
        }

        throw new RuntimeException("All PI Service API call attempts failed", lastException);
    }

    @FunctionalInterface
    private interface ApiCall<T> {
        T call() throws Exception;
    }

    @Override
    public void close() throws IOException {
        if (httpClient != null) {
            httpClient.close();
            LOG.info("PI Service API client closed");
        }
    }
}
