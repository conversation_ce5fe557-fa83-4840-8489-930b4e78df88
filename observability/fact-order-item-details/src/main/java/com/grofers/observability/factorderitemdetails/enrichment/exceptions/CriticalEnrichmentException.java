package com.grofers.observability.factorderitemdetails.enrichment.exceptions;

/**
 * Critical enrichment exception that should fail the job immediately Used for failures that
 * compromise data quality and cannot be recovered from
 */
public class CriticalEnrichmentException extends RuntimeException {

    public CriticalEnrichmentException(String message) {
        super(message);
    }

    public CriticalEnrichmentException(String message, Throwable cause) {
        super(message, cause);
    }
}
