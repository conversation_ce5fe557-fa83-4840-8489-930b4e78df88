package com.grofers.observability.factorderitemdetails.enrichment.models;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.ArrayList;
import java.util.List;

/** Product API request model for enrichment: */
public class ProductRequest {
    @JsonProperty("ids")
    private List<Integer> productIds;

    // Default constructor
    public ProductRequest() {
        this.productIds = new ArrayList<>();
    }

    // Constructor with product IDs
    public ProductRequest(List<Integer> productIds) {
        this.productIds = productIds != null ? productIds : new ArrayList<>();
    }

    // Constructor with single product ID
    public ProductRequest(Integer productId) {
        this.productIds = new ArrayList<>();
        if (productId != null) {
            this.productIds.add(productId);
        }
    }

    // Getters and Setters
    public List<Integer> getProductIds() {
        return productIds;
    }

    public void setProductIds(List<Integer> productIds) {
        this.productIds = productIds;
    }

    @Override
    public String toString() {
        return "ProductRequest{" + "productIds=" + productIds + '}';
    }
}
