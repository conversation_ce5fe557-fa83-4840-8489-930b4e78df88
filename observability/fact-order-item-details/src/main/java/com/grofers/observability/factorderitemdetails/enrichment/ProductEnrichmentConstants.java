package com.grofers.observability.factorderitemdetails.enrichment;

/**
 * Constants used throughout the product enrichment system.
 *
 * <p>This class contains all configuration constants for caching, timeouts, default values, and
 * other settings used by the product enrichment functionality.
 */
public class ProductEnrichmentConstants {

    // Cache TTL constants (in seconds)
    public static final int ONE_DAY_IN_SECONDS = 24 * 60 * 60; // 86400 seconds
    public static final int TWO_DAY_IN_SECONDS = 2 * 24 * 60 * 60; // 172800 seconds

    // Cache key prefixes
    public static final String PRODUCT_CACHE_PREFIX = "product_category_mappings:";
    public static final String CATEGORY_MAPPING_CACHE_KEY = "category_child_parent_mapping";

    // Category types
    public static final String SUBCATEGORY = "subcategory";

    // Default values
    public static final String DEFAULT_BRAND = "#-NA";
    public static final String EMPTY_STRING = "";

    private ProductEnrichmentConstants() {
        // Utility class - prevent instantiation
    }
}
