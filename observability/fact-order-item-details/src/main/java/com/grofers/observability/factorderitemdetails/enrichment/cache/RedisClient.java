package com.grofers.observability.factorderitemdetails.enrichment.cache;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisPool;
import redis.clients.jedis.JedisPoolConfig;
import redis.clients.jedis.exceptions.JedisException;

import java.io.Closeable;
import java.util.Optional;

/**
 * Redis client for caching product and category data Based on Go reference:
 * data-order-lifecycle-streams/pkg/redis/client.go
 */
public class RedisClient implements Closeable {
    private static final Logger LOG = LoggerFactory.getLogger(RedisClient.class);

    private final JedisPool jedisPool;
    private final RedisConfig config;

    public RedisClient(RedisConfig config) {
        this.config = config;
        this.jedisPool = createJedisPool(config);
        LOG.info("Redis client initialized with config: {}", config);
    }

    private JedisPool createJedisPool(RedisConfig config) {
        JedisPoolConfig poolConfig = new JedisPoolConfig();
        poolConfig.setMaxTotal(config.getMaxTotal());
        poolConfig.setMaxIdle(config.getMaxIdle());
        poolConfig.setMinIdle(config.getMinIdle());
        poolConfig.setTestOnBorrow(true);
        poolConfig.setTestOnReturn(true);
        poolConfig.setTestWhileIdle(true);
        poolConfig.setBlockWhenExhausted(true);

        return new JedisPool(
                poolConfig,
                config.getHost(),
                config.getPort(),
                config.getConnectionTimeout(),
                config.getSocketTimeout(),
                null, // password
                config.getDatabase(),
                null // clientName
                );
    }

    /**
     * Get value from Redis cache
     *
     * @param key Cache key
     * @return Optional containing the value if found, empty otherwise
     */
    public Optional<String> get(String key) {
        try (Jedis jedis = jedisPool.getResource()) {
            String value = jedis.get(key);
            if (value != null) {
                LOG.debug("KEY_FOUND_IN_CACHE: {}", key);
                return Optional.of(value);
            } else {
                LOG.debug("KEY_NOT_FOUND_IN_CACHE: {}", key);
                return Optional.empty();
            }
        } catch (JedisException e) {
            LOG.error("Error getting value from Redis for key: {}", key, e);
            return Optional.empty();
        }
    }

    /**
     * Set value in Redis cache with TTL
     *
     * @param key Cache key
     * @param value Value to cache
     * @param ttlSeconds TTL in seconds
     * @return true if successful, false otherwise
     */
    public boolean set(String key, String value, int ttlSeconds) {
        try (Jedis jedis = jedisPool.getResource()) {
            String result = jedis.setex(key, ttlSeconds, value);
            boolean success = "OK".equals(result);
            if (success) {
                LOG.debug("Added VALUE for KEY: {} in cache with TTL: {} seconds", key, ttlSeconds);
            } else {
                LOG.warn("Failed to set value for key: {}", key);
            }
            return success;
        } catch (JedisException e) {
            LOG.error("Error setting value in Redis for key: {}", key, e);
            return false;
        }
    }

    @Override
    public void close() {
        if (jedisPool != null && !jedisPool.isClosed()) {
            jedisPool.close();
            LOG.info("Redis client closed");
        }
    }
}
