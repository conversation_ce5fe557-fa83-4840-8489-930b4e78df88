package com.grofers.observability.factorderitemdetails.enrichment.api;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.grofers.observability.factorderitemdetails.enrichment.exceptions.CriticalEnrichmentException;
import com.grofers.observability.factorderitemdetails.enrichment.models.CategoryChildParentMapping;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.Closeable;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.concurrent.CompletableFuture;

/**
 * CMS API client for fetching category mappings only Based on Go reference:
 * data-order-lifecycle-streams/internal/services/cms/service.go
 */
public class CmsApiClient implements Closeable {
    private static final Logger LOG = LoggerFactory.getLogger(CmsApiClient.class);

    private final CmsApiConfig config;
    private final CloseableHttpClient httpClient;
    private final ObjectMapper objectMapper;
    private final RequestConfig requestConfig;

    public CmsApiClient(CmsApiConfig config) {
        this.config = config;
        this.objectMapper = new ObjectMapper();
        this.requestConfig =
                RequestConfig.custom()
                        .setConnectTimeout(config.getConnectionTimeout())
                        .setSocketTimeout(config.getSocketTimeout())
                        .build();
        this.httpClient = HttpClients.custom().setDefaultRequestConfig(requestConfig).build();

        LOG.info("CMS API client initialized with config: {}", config);
    }

    /**
     * Get category child-parent mapping from CMS API
     *
     * @return CompletableFuture with CategoryChildParentMapping
     */
    public CompletableFuture<CategoryChildParentMapping> getCategoryMapping() {
        return CompletableFuture.supplyAsync(
                () -> {
                    String url = config.getBaseUrl() + "/api/services/filters/categories";

                    try {
                        LOG.debug("Calling CMS API for category mapping: {}", url);

                        HttpGet httpGet = new HttpGet(url);
                        if (config.getAuthorization() != null
                                && !config.getAuthorization().isEmpty()) {
                            httpGet.setHeader("Authorization", config.getAuthorization());
                        }

                        return executeWithRetry(
                                () -> {
                                    HttpResponse response = httpClient.execute(httpGet);
                                    return handleCategoryResponse(response);
                                });

                    } catch (Exception e) {
                        LOG.error("Error calling CMS API for category mapping", e);
                        return new CategoryChildParentMapping(); // Return empty response as
                        // fallback
                    }
                });
    }

    private CategoryChildParentMapping handleCategoryResponse(HttpResponse response)
            throws IOException {
        int statusCode = response.getStatusLine().getStatusCode();
        HttpEntity entity = response.getEntity();

        if (statusCode == 200 && entity != null) {
            String responseBody = EntityUtils.toString(entity, StandardCharsets.UTF_8);
            LOG.debug("CMS API category response: {}", responseBody);
            return objectMapper.readValue(responseBody, CategoryChildParentMapping.class);
        } else {
            String errorBody =
                    entity != null
                            ? EntityUtils.toString(entity, StandardCharsets.UTF_8)
                            : "No response body";
            throw new CriticalEnrichmentException(
                    String.format(
                            "CMS API returned non-200 status for categories: %d, body: %s",
                            statusCode, errorBody));
        }
    }

    private <T> T executeWithRetry(ApiCall<T> apiCall) throws Exception {
        Exception lastException = null;

        for (int attempt = 1; attempt <= config.getMaxRetries(); attempt++) {
            try {
                return apiCall.call();
            } catch (Exception e) {
                lastException = e;
                LOG.warn("API call attempt {} failed: {}", attempt, e.getMessage());

                if (attempt < config.getMaxRetries()) {
                    try {
                        Thread.sleep(config.getRetryDelay() * attempt); // Exponential backoff
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        throw new RuntimeException("Interrupted during retry delay", ie);
                    }
                }
            }
        }

        throw new RuntimeException("All API call attempts failed", lastException);
    }

    @FunctionalInterface
    private interface ApiCall<T> {
        T call() throws Exception;
    }

    @Override
    public void close() throws IOException {
        if (httpClient != null) {
            httpClient.close();
            LOG.info("CMS API client closed");
        }
    }
}
