package com.grofers.observability.factorderitemdetails.enrichment.api;

/**
 * Configuration class for Product Information (PI) Service API client.
 *
 * <p>This configuration class encapsulates all settings required for connecting to and
 * communicating with the PI service, including connection timeouts, retry policies, and
 * authentication details.
 *
 * <p>Default values are optimized for production use with fail-fast behavior: - Connection timeout:
 * 5 seconds - Socket timeout: 5 seconds - Max retries: 3 attempts - Retry delay: 1 second between
 * attempts
 */
public class PiServiceConfig {
    private String baseUrl;
    private String authorization;
    private int connectionTimeout;
    private int socketTimeout;
    private int maxRetries;
    private int retryDelay;

    // Default constructor with sensible defaults
    public PiServiceConfig() {
        this.connectionTimeout = 5000; // 5 seconds
        this.socketTimeout = 5000; // 5 seconds (matching original CMS API config)
        this.maxRetries = 3;
        this.retryDelay = 1000; // 1 second
    }

    // Constructor with required fields
    public PiServiceConfig(String baseUrl, String authorization) {
        this();
        this.baseUrl = baseUrl;
        this.authorization = authorization;
    }

    // Constructor with all fields
    public PiServiceConfig(
            String baseUrl,
            String authorization,
            int connectionTimeout,
            int socketTimeout,
            int maxRetries,
            int retryDelay) {
        this.baseUrl = baseUrl;
        this.authorization = authorization;
        this.connectionTimeout = connectionTimeout;
        this.socketTimeout = socketTimeout;
        this.maxRetries = maxRetries;
        this.retryDelay = retryDelay;
    }

    // Getters and Setters
    public String getBaseUrl() {
        return baseUrl;
    }

    public void setBaseUrl(String baseUrl) {
        this.baseUrl = baseUrl;
    }

    public String getAuthorization() {
        return authorization;
    }

    public void setAuthorization(String authorization) {
        this.authorization = authorization;
    }

    public int getConnectionTimeout() {
        return connectionTimeout;
    }

    public void setConnectionTimeout(int connectionTimeout) {
        this.connectionTimeout = connectionTimeout;
    }

    public int getSocketTimeout() {
        return socketTimeout;
    }

    public void setSocketTimeout(int socketTimeout) {
        this.socketTimeout = socketTimeout;
    }

    public int getMaxRetries() {
        return maxRetries;
    }

    public void setMaxRetries(int maxRetries) {
        this.maxRetries = maxRetries;
    }

    public int getRetryDelay() {
        return retryDelay;
    }

    public void setRetryDelay(int retryDelay) {
        this.retryDelay = retryDelay;
    }

    @Override
    public String toString() {
        // Mask sensitive URL and authorization for security
        String maskedUrl =
                baseUrl != null && baseUrl.length() > 6
                        ? baseUrl.substring(0, 3) + "***" + baseUrl.substring(baseUrl.length() - 3)
                        : "***MASKED***";

        return "PiServiceConfig{"
                + "baseUrl='"
                + maskedUrl
                + '\''
                + ", authorization='***'"
                + // Hide authorization for security
                ", connectionTimeout="
                + connectionTimeout
                + ", socketTimeout="
                + socketTimeout
                + ", maxRetries="
                + maxRetries
                + ", retryDelay="
                + retryDelay
                + '}';
    }
}
