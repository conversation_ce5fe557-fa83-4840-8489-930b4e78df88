package com.grofers.observability.factorderitemdetails.enrichment.models;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.ArrayList;
import java.util.List;

/** Product API response model for enrichment: */
@JsonIgnoreProperties(ignoreUnknown = true)
public class ProductResponse {
    @JsonProperty("products")
    private List<Product> products;

    // Default constructor
    public ProductResponse() {
        this.products = new ArrayList<>();
    }

    // Constructor with products
    public ProductResponse(List<Product> products) {
        this.products = products != null ? products : new ArrayList<>();
    }

    // Getters and Setters
    public List<Product> getProducts() {
        return products;
    }

    public void setProducts(List<Product> products) {
        this.products = products;
    }

    @Override
    public String toString() {
        return "ProductResponse{" + "products=" + products + '}';
    }

    /** Inner Product class */
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Product {
        @JsonProperty("id")
        private Integer id;

        @JsonProperty("name")
        private String name;

        @JsonProperty("brand")
        private String brand;

        @JsonProperty("type")
        private ProductType ptype;

        @JsonProperty("category_hierarchy")
        private List<CategoryHierarchy> categoryHierarchyOfProduct;

        // Default constructor
        public Product() {
            this.categoryHierarchyOfProduct = new ArrayList<>();
        }

        // Getters and Setters
        public Integer getId() {
            return id;
        }

        public void setId(Integer id) {
            this.id = id;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getBrand() {
            return brand;
        }

        public void setBrand(String brand) {
            this.brand = brand;
        }

        public ProductType getPtype() {
            return ptype;
        }

        public void setPtype(ProductType ptype) {
            this.ptype = ptype;
        }

        public List<CategoryHierarchy> getCategoryHierarchyOfProduct() {
            return categoryHierarchyOfProduct;
        }

        public void setCategoryHierarchyOfProduct(
                List<CategoryHierarchy> categoryHierarchyOfProduct) {
            this.categoryHierarchyOfProduct = categoryHierarchyOfProduct;
        }

        @Override
        public String toString() {
            return "Product{"
                    + "id="
                    + id
                    + ", name='"
                    + name
                    + '\''
                    + ", brand='"
                    + brand
                    + '\''
                    + ", ptype="
                    + ptype
                    + ", categoryHierarchyOfProduct="
                    + categoryHierarchyOfProduct
                    + '}';
        }
    }

    /** Product Type class */
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class ProductType {
        @JsonProperty("display_name")
        private String name;

        // Default constructor
        public ProductType() {}

        // Constructor
        public ProductType(String name) {
            this.name = name;
        }

        // Getters and Setters
        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        @Override
        public String toString() {
            return "ProductType{" + "name='" + name + '\'' + '}';
        }
    }

    /** Category Hierarchy class */
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class CategoryHierarchy {
        @JsonProperty("id")
        private Integer id;

        @JsonProperty("type")
        private String type;

        @JsonProperty("is_primary")
        private Boolean primary;

        @JsonProperty("is_leaf")
        private Boolean leaf;

        // Default constructor
        public CategoryHierarchy() {}

        // Getters and Setters
        public Integer getId() {
            return id;
        }

        public void setId(Integer id) {
            this.id = id;
        }

        public String getType() {
            return type;
        }

        public void setType(String type) {
            this.type = type;
        }

        public Boolean getPrimary() {
            return primary;
        }

        public void setPrimary(Boolean primary) {
            this.primary = primary;
        }

        public Boolean getLeaf() {
            return leaf;
        }

        public void setLeaf(Boolean leaf) {
            this.leaf = leaf;
        }

        @Override
        public String toString() {
            return "CategoryHierarchy{"
                    + "id="
                    + id
                    + ", type='"
                    + type
                    + '\''
                    + ", primary="
                    + primary
                    + ", leaf="
                    + leaf
                    + '}';
        }
    }
}
