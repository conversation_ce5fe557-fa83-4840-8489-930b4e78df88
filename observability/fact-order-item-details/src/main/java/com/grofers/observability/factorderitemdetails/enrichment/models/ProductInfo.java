package com.grofers.observability.factorderitemdetails.enrichment.models;

import com.fasterxml.jackson.annotation.JsonProperty;

/** Product information model for enrichment: */
public class ProductInfo {
    @JsonProperty("l0_category")
    private String l0Category;

    @JsonProperty("l0_category_id")
    private Integer l0CategoryId;

    @JsonProperty("l1_category")
    private String l1Category;

    @JsonProperty("l1_category_id")
    private Integer l1CategoryId;

    @JsonProperty("l2_category")
    private String l2Category;

    @JsonProperty("l2_category_id")
    private Integer l2CategoryId;

    @JsonProperty("brand")
    private String brand;

    @JsonProperty("ptype")
    private String productType;

    @JsonProperty("product_name")
    private String productName;

    // Default constructor
    public ProductInfo() {
        this.l0Category = "";
        this.l0CategoryId = null;
        this.l1Category = "";
        this.l1CategoryId = null;
        this.l2Category = "";
        this.l2CategoryId = null;
        this.brand = "#-NA";
        this.productType = "";
        this.productName = "";
    }

    // Constructor with all fields
    public ProductInfo(
            String l0Category,
            Integer l0CategoryId,
            String l1Category,
            Integer l1CategoryId,
            String l2Category,
            Integer l2CategoryId,
            String brand,
            String productType,
            String productName) {
        this.l0Category = l0Category != null ? l0Category : "";
        this.l0CategoryId = l0CategoryId;
        this.l1Category = l1Category != null ? l1Category : "";
        this.l1CategoryId = l1CategoryId;
        this.l2Category = l2Category != null ? l2Category : "";
        this.l2CategoryId = l2CategoryId;
        this.brand = brand != null ? brand : "#-NA";
        this.productType = productType != null ? productType : "";
        this.productName = productName != null ? productName : "";
    }

    // Getters and Setters
    public String getL0Category() {
        return l0Category;
    }

    public void setL0Category(String l0Category) {
        this.l0Category = l0Category;
    }

    public Integer getL0CategoryId() {
        return l0CategoryId;
    }

    public void setL0CategoryId(Integer l0CategoryId) {
        this.l0CategoryId = l0CategoryId;
    }

    public String getL1Category() {
        return l1Category;
    }

    public void setL1Category(String l1Category) {
        this.l1Category = l1Category;
    }

    public Integer getL1CategoryId() {
        return l1CategoryId;
    }

    public void setL1CategoryId(Integer l1CategoryId) {
        this.l1CategoryId = l1CategoryId;
    }

    public String getL2Category() {
        return l2Category;
    }

    public void setL2Category(String l2Category) {
        this.l2Category = l2Category;
    }

    public Integer getL2CategoryId() {
        return l2CategoryId;
    }

    public void setL2CategoryId(Integer l2CategoryId) {
        this.l2CategoryId = l2CategoryId;
    }

    public String getBrand() {
        return brand;
    }

    public void setBrand(String brand) {
        this.brand = brand;
    }

    public String getProductType() {
        return productType;
    }

    public void setProductType(String productType) {
        this.productType = productType;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    @Override
    public String toString() {
        return "ProductInfo{"
                + "l0Category='"
                + l0Category
                + '\''
                + ", l0CategoryId="
                + l0CategoryId
                + ", l1Category='"
                + l1Category
                + '\''
                + ", l1CategoryId="
                + l1CategoryId
                + ", l2Category='"
                + l2Category
                + '\''
                + ", l2CategoryId="
                + l2CategoryId
                + ", brand='"
                + brand
                + '\''
                + ", productType='"
                + productType
                + '\''
                + ", productName='"
                + productName
                + '\''
                + '}';
    }
}
