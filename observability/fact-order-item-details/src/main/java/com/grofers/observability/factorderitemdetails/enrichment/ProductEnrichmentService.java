package com.grofers.observability.factorderitemdetails.enrichment;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.grofers.observability.factorderitemdetails.enrichment.api.CmsApiClient;
import com.grofers.observability.factorderitemdetails.enrichment.api.PiServiceClient;
import com.grofers.observability.factorderitemdetails.enrichment.cache.RedisClient;
import com.grofers.observability.factorderitemdetails.enrichment.exceptions.CriticalEnrichmentException;
import com.grofers.observability.factorderitemdetails.enrichment.models.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;

import static com.grofers.observability.factorderitemdetails.enrichment.ProductEnrichmentConstants.*;

/**
 * Service for product enrichment with tiered caching.
 *
 * <p>Provides product enrichment functionality with a tiered caching strategy: Redis cache → API
 * calls → local caching. Implements fail-fast error handling to ensure data quality.
 */
public class ProductEnrichmentService {
    private static final Logger LOG = LoggerFactory.getLogger(ProductEnrichmentService.class);

    private final PiServiceClient piServiceClient;
    private final CmsApiClient cmsApiClient;
    private final RedisClient redisClient;
    private final ObjectMapper objectMapper;

    public ProductEnrichmentService(
            PiServiceClient piServiceClient, CmsApiClient cmsApiClient, RedisClient redisClient) {
        this.piServiceClient = piServiceClient;
        this.cmsApiClient = cmsApiClient;
        this.redisClient = redisClient;
        this.objectMapper = new ObjectMapper();
    }

    /**
     * Get product details with tiered caching
     *
     * @param productId Product ID to enrich
     * @return CompletableFuture with ProductInfo
     */
    public CompletableFuture<ProductInfo> getProductDetails(Integer productId) {
        if (productId == null) {
            return CompletableFuture.completedFuture(new ProductInfo());
        }

        String productCacheKey = PRODUCT_CACHE_PREFIX + productId;

        // Try Redis cache first - fail fast on Redis connection issues
        try {
            Optional<String> cachedProductInfo = redisClient.get(productCacheKey);
            if (cachedProductInfo.isPresent()) {
                try {
                    ProductInfo productInfo =
                            objectMapper.readValue(cachedProductInfo.get(), ProductInfo.class);
                    LOG.debug("Returning cached product info for product ID: {}", productId);
                    return CompletableFuture.completedFuture(productInfo);
                } catch (Exception e) {
                    LOG.warn(
                            "Failed to deserialize cached product info for product ID: {} -"
                                    + " continuing with API fetch",
                            productId,
                            e);
                }
            }
        } catch (Exception e) {
            // Redis connection failures are critical - fail fast
            throw new CriticalEnrichmentException(
                    String.format("Redis connection failed for product ID: %d", productId), e);
        }

        // Cache miss - fetch from API
        return fetchAndCacheProductDetails(productId);
    }

    private CompletableFuture<ProductInfo> fetchAndCacheProductDetails(Integer productId) {
        return getCategoryMapping()
                .thenCompose(
                        categoryMapping -> {
                            ProductRequest request = new ProductRequest(productId);
                            return piServiceClient
                                    .getProductDetails(request)
                                    .thenApply(
                                            productResponse -> {
                                                try {
                                                    ProductInfo productInfo =
                                                            mapProductResponse(
                                                                    productResponse,
                                                                    categoryMapping);
                                                    cacheProductInfo(productId, productInfo);
                                                    return productInfo;
                                                } catch (Exception e) {
                                                    throw new CriticalEnrichmentException(
                                                            String.format(
                                                                    "Error mapping product"
                                                                        + " response for product"
                                                                        + " ID: %d",
                                                                    productId),
                                                            e);
                                                }
                                            });
                        })
                .exceptionally(
                        throwable -> {
                            throw new CriticalEnrichmentException(
                                    String.format(
                                            "Error fetching product details for product ID: %d",
                                            productId),
                                    throwable);
                        });
    }

    private CompletableFuture<Map<Integer, Category>> getCategoryMapping() {
        try {
            Optional<String> cachedMapping = redisClient.get(CATEGORY_MAPPING_CACHE_KEY);

            if (cachedMapping.isPresent()) {
                try {
                    Map<Integer, Category> mapping =
                            objectMapper.readValue(
                                    cachedMapping.get(),
                                    objectMapper
                                            .getTypeFactory()
                                            .constructMapType(
                                                    Map.class, Integer.class, Category.class));
                    LOG.debug("Returning cached category mapping");
                    return CompletableFuture.completedFuture(mapping);
                } catch (Exception e) {
                    LOG.warn(
                            "Failed to deserialize cached category mapping - continuing with"
                                    + " API fetch",
                            e);
                }
            }
        } catch (Exception e) {
            // Redis connection failures are critical
            throw new CriticalEnrichmentException(
                    "Redis connection failed while fetching category mapping", e);
        }

        // Cache miss - fetch from API
        return cmsApiClient
                .getCategoryMapping()
                .thenApply(this::buildCategoryMapping)
                .thenApply(
                        mapping -> {
                            cacheCategoryMapping(mapping);
                            return mapping;
                        })
                .exceptionally(
                        throwable -> {
                            throw new CriticalEnrichmentException(
                                    "Error fetching category mapping from API", throwable);
                        });
    }

    private Map<Integer, Category> buildCategoryMapping(
            CategoryChildParentMapping categoryMapping) {
        Map<Integer, Category> idToCategoryMapping = new HashMap<>();

        if (categoryMapping.getCategories() != null) {
            for (Category category : categoryMapping.getCategories()) {
                if (category.getId() != null) {
                    idToCategoryMapping.put(category.getId(), category);
                }
            }
        }

        LOG.debug("Built category mapping with {} categories", idToCategoryMapping.size());
        return idToCategoryMapping;
    }

    private ProductInfo mapProductResponse(
            ProductResponse productResponse, Map<Integer, Category> categoryMapping) {
        if (productResponse.getProducts() == null || productResponse.getProducts().isEmpty()) {
            throw new CriticalEnrichmentException("productResponse.Products is empty");
        }

        ProductResponse.Product product = productResponse.getProducts().get(0);

        if (product.getCategoryHierarchyOfProduct() == null
                || product.getCategoryHierarchyOfProduct().isEmpty()) {
            throw new CriticalEnrichmentException(
                    "productResponse.Products.CategoryHierarchy is empty");
        }

        if (categoryMapping == null) {
            throw new CriticalEnrichmentException("parent child category mapping is null");
        }
        ProductInfo productInfo = new ProductInfo();

        // Set basic product info
        productInfo.setProductName(product.getName() != null ? product.getName() : EMPTY_STRING);
        productInfo.setBrand(product.getBrand() != null ? product.getBrand() : DEFAULT_BRAND);

        if (product.getPtype() != null && product.getPtype().getName() != null) {
            productInfo.setProductType(product.getPtype().getName());
        }

        // Map category hierarchy
        if (product.getCategoryHierarchyOfProduct() != null && !categoryMapping.isEmpty()) {
            mapCategoryHierarchy(
                    product.getCategoryHierarchyOfProduct(), categoryMapping, productInfo);
        }

        return productInfo;
    }

    private void mapCategoryHierarchy(
            List<ProductResponse.CategoryHierarchy> categoryHierarchy,
            Map<Integer, Category> categoryMapping,
            ProductInfo productInfo) {

        for (ProductResponse.CategoryHierarchy hierarchy : categoryHierarchy) {
            if (SUBCATEGORY.equals(hierarchy.getType())
                    && Boolean.TRUE.equals(hierarchy.getPrimary())
                    && Boolean.TRUE.equals(hierarchy.getLeaf())) {

                // Check if this category exists in our mapping
                Category category = categoryMapping.get(hierarchy.getId());
                if (category == null) {
                    LOG.warn("Category ID {} not found in category mapping!", hierarchy.getId());
                    continue;
                }

                CategoryLevel level =
                        countParentCategoryIterations(hierarchy.getId(), categoryMapping);

                if (level.getIterations() == 1) {
                    Category l1Category = categoryMapping.get(hierarchy.getId());
                    if (l1Category != null) {
                        productInfo.setL1Category(l1Category.getName());
                        productInfo.setL1CategoryId(l1Category.getId());
                    }
                } else if (level.getIterations() == 2) {
                    Category l2Category = categoryMapping.get(hierarchy.getId());
                    if (l2Category != null) {
                        productInfo.setL2Category(l2Category.getName());
                        productInfo.setL2CategoryId(l2Category.getId());
                    }
                }

                // Set L0 category from the traversal
                if (level.getL0CategoryName() != null) {
                    productInfo.setL0Category(level.getL0CategoryName());
                    productInfo.setL0CategoryId(level.getL0CategoryId());
                }
                if (level.getL1CategoryName() != null && productInfo.getL1Category().isEmpty()) {
                    productInfo.setL1Category(level.getL1CategoryName());
                    productInfo.setL1CategoryId(level.getL1CategoryId());
                }

                break; // Found the primary leaf subcategory
            }
        }
    }

    private CategoryLevel countParentCategoryIterations(
            Integer categoryId, Map<Integer, Category> categoryMapping) {
        CategoryLevel level = new CategoryLevel();
        int iterations = 0;
        Integer currentId = categoryId;

        while (currentId != null && iterations <= 3) { // Prevent infinite loops
            Category category = categoryMapping.get(currentId);
            if (category == null) {
                LOG.warn("Category not found in mapping: {}", currentId);
                break;
            }

            // Check if we've reached the root category
            if (category.getId().equals(category.getParentId())) {
                level.setL0CategoryName(category.getName());
                level.setL0CategoryId(category.getId());
                break;
            }

            iterations++;
            if (iterations == 2) {
                level.setL1CategoryName(category.getName());
                level.setL1CategoryId(category.getId());
            }

            if (iterations > 2) {
                // Reset previously set values to ensure clean state (like OLS)
                level.setL1CategoryName(null);
                level.setL1CategoryId(null);

                // Return error like OLS instead of continuing with partial data
                throw new CriticalEnrichmentException(
                        String.format(
                                "Category hierarchy too deep for category ID: %d. "
                                        + "Maximum depth of 2 exceeded with %d iterations.",
                                categoryId, iterations));
            }

            currentId = category.getParentId();
        }

        level.setIterations(iterations);
        return level;
    }

    private void cacheProductInfo(Integer productId, ProductInfo productInfo) {
        try {
            String productInfoJson = objectMapper.writeValueAsString(productInfo);
            String cacheKey = PRODUCT_CACHE_PREFIX + productId;
            boolean success = redisClient.set(cacheKey, productInfoJson, ONE_DAY_IN_SECONDS);
            if (!success) {
                LOG.warn("Failed to cache product info for product ID: {}", productId);
            }
        } catch (Exception e) {
            LOG.error("Error caching product info for product ID: {}", productId, e);
        }
    }

    private void cacheCategoryMapping(Map<Integer, Category> categoryMapping) {
        try {
            String mappingJson = objectMapper.writeValueAsString(categoryMapping);
            boolean success =
                    redisClient.set(CATEGORY_MAPPING_CACHE_KEY, mappingJson, TWO_DAY_IN_SECONDS);
            if (!success) {
                LOG.warn("Failed to cache category mapping");
            }
        } catch (Exception e) {
            LOG.error("Error caching category mapping", e);
        }
    }

    /** Helper class to hold category level information during traversal */
    private static class CategoryLevel {
        private int iterations;
        private String l0CategoryName;
        private Integer l0CategoryId;
        private String l1CategoryName;
        private Integer l1CategoryId;

        // Getters and setters
        public int getIterations() {
            return iterations;
        }

        public void setIterations(int iterations) {
            this.iterations = iterations;
        }

        public String getL0CategoryName() {
            return l0CategoryName;
        }

        public void setL0CategoryName(String l0CategoryName) {
            this.l0CategoryName = l0CategoryName;
        }

        public Integer getL0CategoryId() {
            return l0CategoryId;
        }

        public void setL0CategoryId(Integer l0CategoryId) {
            this.l0CategoryId = l0CategoryId;
        }

        public String getL1CategoryName() {
            return l1CategoryName;
        }

        public void setL1CategoryName(String l1CategoryName) {
            this.l1CategoryName = l1CategoryName;
        }

        public Integer getL1CategoryId() {
            return l1CategoryId;
        }

        public void setL1CategoryId(Integer l1CategoryId) {
            this.l1CategoryId = l1CategoryId;
        }
    }
}
