package com.grofers.observability.factorderitemdetails.enrichment.cache;

/** Redis configuration model for connection and pool settings. */
public class RedisConfig {
    private String host;
    private int port;
    private int database;
    private int connectionTimeout;
    private int socketTimeout;
    private int maxTotal;
    private int maxIdle;
    private int minIdle;

    // Default constructor with sensible defaults
    public RedisConfig() {
        this.host = "localhost";
        this.port = 6379;
        this.database = 0;
        this.connectionTimeout = 2000; // 2 seconds
        this.socketTimeout = 2000; // 2 seconds
        this.maxTotal = 20;
        this.maxIdle = 10;
        this.minIdle = 5;
    }

    // Constructor with host and port
    public RedisConfig(String host, int port) {
        this();
        this.host = host;
        this.port = port;
    }

    // Constructor with all fields
    public RedisConfig(
            String host,
            int port,
            int database,
            int connectionTimeout,
            int socketTimeout,
            int maxTotal,
            int maxIdle,
            int minIdle) {
        this.host = host;
        this.port = port;
        this.database = database;
        this.connectionTimeout = connectionTimeout;
        this.socketTimeout = socketTimeout;
        this.maxTotal = maxTotal;
        this.maxIdle = maxIdle;
        this.minIdle = minIdle;
    }

    // Getters and Setters
    public String getHost() {
        return host;
    }

    public void setHost(String host) {
        this.host = host;
    }

    public int getPort() {
        return port;
    }

    public void setPort(int port) {
        this.port = port;
    }

    public int getDatabase() {
        return database;
    }

    public void setDatabase(int database) {
        this.database = database;
    }

    public int getConnectionTimeout() {
        return connectionTimeout;
    }

    public void setConnectionTimeout(int connectionTimeout) {
        this.connectionTimeout = connectionTimeout;
    }

    public int getSocketTimeout() {
        return socketTimeout;
    }

    public void setSocketTimeout(int socketTimeout) {
        this.socketTimeout = socketTimeout;
    }

    public int getMaxTotal() {
        return maxTotal;
    }

    public void setMaxTotal(int maxTotal) {
        this.maxTotal = maxTotal;
    }

    public int getMaxIdle() {
        return maxIdle;
    }

    public void setMaxIdle(int maxIdle) {
        this.maxIdle = maxIdle;
    }

    public int getMinIdle() {
        return minIdle;
    }

    public void setMinIdle(int minIdle) {
        this.minIdle = minIdle;
    }

    @Override
    public String toString() {
        // Mask sensitive host information for security
        String maskedHost =
                host != null && host.length() > 6
                        ? host.substring(0, 3) + "***" + host.substring(host.length() - 3)
                        : "***MASKED***";

        return "RedisConfig{"
                + "host='"
                + maskedHost
                + '\''
                + ", port="
                + port
                + ", database="
                + database
                + ", connectionTimeout="
                + connectionTimeout
                + ", socketTimeout="
                + socketTimeout
                + ", maxTotal="
                + maxTotal
                + ", maxIdle="
                + maxIdle
                + ", minIdle="
                + minIdle
                + '}';
    }
}
