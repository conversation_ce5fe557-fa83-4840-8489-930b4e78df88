package com.grofers.observability.factorderitemdetails.enrichment;

import org.apache.flink.table.annotation.DataTypeHint;
import org.apache.flink.table.annotation.FunctionHint;
import org.apache.flink.table.functions.TableFunction;
import org.apache.flink.types.Row;

import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.grofers.observability.factorderitemdetails.enrichment.api.CmsApiClient;
import com.grofers.observability.factorderitemdetails.enrichment.api.CmsApiConfig;
import com.grofers.observability.factorderitemdetails.enrichment.api.PiServiceClient;
import com.grofers.observability.factorderitemdetails.enrichment.api.PiServiceConfig;
import com.grofers.observability.factorderitemdetails.enrichment.cache.RedisClient;
import com.grofers.observability.factorderitemdetails.enrichment.cache.RedisConfig;
import com.grofers.observability.factorderitemdetails.enrichment.exceptions.CriticalEnrichmentException;
import com.grofers.observability.factorderitemdetails.enrichment.models.ProductInfo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.concurrent.TimeUnit;

/** Table function for product enrichment that can be used with LATERAL TABLE() in SQL queries */
@FunctionHint(
        output =
                @DataTypeHint(
                        "ROW<l0_category STRING, l0_category_id INT, l1_category STRING,"
                                + " l1_category_id INT, l2_category STRING, l2_category_id INT,"
                                + " brand_name STRING, product_name STRING, product_type STRING>"))
public class ProductEnrichmentTableFunction extends TableFunction<Row> {
    private static final Logger LOG = LoggerFactory.getLogger(ProductEnrichmentTableFunction.class);

    private transient ProductEnrichmentService enrichmentService;
    private transient RedisClient redisClient;
    private transient CmsApiClient cmsApiClient;
    private transient PiServiceClient piServiceClient;

    private transient boolean initialized = false;

    // Local in-memory cache with 50K entries and 1-day TTL (matching Redis TTL)
    private transient Cache<Integer, ProductInfo> localCache;

    /**
     * Enrich product with category and brand information
     *
     * @param productId Product ID to enrich
     */
    public void eval(Integer productId) {
        if (!initialized) {
            initialize();
        }

        // Fail fast for null product IDs - this indicates data quality issues upstream
        if (productId == null) {
            throw new CriticalEnrichmentException(
                    "Product ID is null - indicates upstream data quality issue. "
                            + "Failing job to prevent processing incomplete data.");
        }

        try {

            // 1. Try local cache first (fastest - ~0.1ms)
            ProductInfo cachedInfo = localCache.getIfPresent(productId);
            if (cachedInfo != null) {
                LOG.debug("Local cache hit for product ID: {}", productId);
                collect(createEnrichmentRow(cachedInfo));
                return;
            }

            // 2. Local cache miss - fetch from Redis + API (~1-500ms)
            LOG.debug("Local cache miss for product ID: {}, fetching from Redis/API", productId);
            ProductInfo productInfo = getProductInfoSync(productId);

            // 3. Cache the result locally for future requests
            localCache.put(productId, productInfo);

            collect(createEnrichmentRow(productInfo));

        } catch (CriticalEnrichmentException e) {
            // Critical failures should fail the job immediately
            LOG.error(
                    "Critical enrichment failure for product ID: {} - failing job to preserve data"
                            + " quality",
                    productId,
                    e);
            throw e;

        } catch (Exception e) {
            // All other failures are also critical for data quality
            LOG.error(
                    "Enrichment failure for product ID: {} - failing job to preserve data quality",
                    productId,
                    e);
            throw new CriticalEnrichmentException(
                    String.format(
                            "Failed to enrich product ID %d - stopping job to preserve data"
                                    + " quality",
                            productId),
                    e);
        }
    }

    private void initialize() {
        try {
            LOG.info("Starting ProductEnrichmentTableFunction initialization...");

            // Determine environment and get secrets using same pattern as reference code
            String environment = System.getProperty("environment", "stage"); // Default to stage
            String envPrefix = environment.equalsIgnoreCase("prod") ? "PROD" : "STAGE";

            // Try both environment variables and system properties (injected by Vault)
            String redisHost = getConfigValue("REDIS_HOST_" + envPrefix, "localhost");
            String redisPort = getConfigValue("REDIS_PORT_" + envPrefix, "6379");
            String redisDb = getConfigValue("REDIS_DB_" + envPrefix, "0");
            String cmsUrl =
                    getConfigValue("CMS_API_URL_" + envPrefix, "https://cms-api.stage.blinkit.com");
            String cmsAuth =
                    getConfigValue("CMS_API_AUTHORIZATION_" + envPrefix, "Bearer test-token");
            String piUrl =
                    getConfigValue(
                            "PI_SERVICE_URL_" + envPrefix, "https://pi-service.stage.blinkit.com");
            String piAuth =
                    getConfigValue("PI_SERVICE_AUTHORIZATION_" + envPrefix, "Bearer test-token");

            LOG.debug("Environment: {}, EnvPrefix: {}", environment, envPrefix);

            // Parse Redis host (handle host:port format from Vault)
            String actualRedisHost = redisHost;
            int actualRedisPort = Integer.parseInt(redisPort);

            // If Redis host contains port (like "host:6379"), extract them
            if (redisHost.contains(":")) {
                String[] parts = redisHost.split(":");
                actualRedisHost = parts[0];
                if (parts.length > 1) {
                    actualRedisPort = Integer.parseInt(parts[1]);
                }
            }

            RedisConfig redisConfig = new RedisConfig(actualRedisHost, actualRedisPort);
            redisConfig.setDatabase(Integer.parseInt(redisDb));
            redisClient = new RedisClient(redisConfig);

            // Initialize CMS API client
            CmsApiConfig cmsConfig = new CmsApiConfig(cmsUrl, cmsAuth);
            cmsApiClient = new CmsApiClient(cmsConfig);

            // Initialize PI Service client
            PiServiceConfig piConfig = new PiServiceConfig(piUrl, piAuth);
            piServiceClient = new PiServiceClient(piConfig);

            // Initialize enrichment service with both clients
            enrichmentService =
                    new ProductEnrichmentService(piServiceClient, cmsApiClient, redisClient);

            // Initialize local cache with 50K entries and 1-day TTL (matching Redis TTL)
            localCache =
                    Caffeine.newBuilder()
                            .maximumSize(50_000)
                            .expireAfterWrite(1, TimeUnit.DAYS)
                            .build();

            initialized = true;
            LOG.info(
                    "ProductEnrichmentTableFunction initialized successfully with local cache (50K"
                            + " entries, 1-day TTL)");
        } catch (Exception e) {
            LOG.error("Failed to initialize ProductEnrichmentTableFunction", e);
            initialized = false;
        }
    }

    private ProductInfo getProductInfoSync(Integer productId) {
        int maxRetries = 3;
        int baseTimeoutSeconds = 20; // Increased base timeout

        for (int attempt = 1; attempt <= maxRetries; attempt++) {
            try {
                // Progressive timeout: 20s, 30s, 40s
                int timeoutSeconds = baseTimeoutSeconds + (attempt - 1) * 10;

                LOG.debug(
                        "Attempting enrichment for product ID: {} (attempt {}/{}, timeout: {}s)",
                        productId,
                        attempt,
                        maxRetries,
                        timeoutSeconds);

                // Try to get from enrichment service with progressive timeout
                ProductInfo result =
                        enrichmentService
                                .getProductDetails(productId)
                                .get(timeoutSeconds, java.util.concurrent.TimeUnit.SECONDS);

                if (attempt > 1) {
                    LOG.info(
                            "Successfully enriched product ID: {} on attempt {} after {}s timeout",
                            productId,
                            attempt,
                            baseTimeoutSeconds + (attempt - 1) * 10);
                }
                return result;

            } catch (java.util.concurrent.TimeoutException e) {
                if (attempt == maxRetries) {
                    // Final attempt failed - this is a critical data quality issue
                    LOG.error(
                            "CRITICAL TIMEOUT: Product ID {} failed after {} attempts with"
                                + " progressive timeouts (20s, 30s, 40s). This indicates serious"
                                + " API performance issues. Failing job to preserve data quality.",
                            productId,
                            maxRetries);
                    throw new CriticalEnrichmentException(
                            String.format(
                                    "Timeout getting product info for ID: %d after %d attempts"
                                            + " with progressive timeouts",
                                    productId, maxRetries),
                            e);
                }

                LOG.warn(
                        "Timeout for product ID: {} on attempt {}/{} ({}s timeout), retrying with"
                                + " longer timeout...",
                        productId,
                        attempt,
                        maxRetries,
                        baseTimeoutSeconds + (attempt - 1) * 10);

                // Brief pause before retry
                try {
                    Thread.sleep(1000 * attempt); // Progressive delay: 1s, 2s
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt();
                    throw new CriticalEnrichmentException(
                            String.format("Interrupted while retrying product ID: %d", productId),
                            ie);
                }

            } catch (Exception e) {
                // Non-timeout errors are still critical - fail fast
                LOG.error("Critical error for product ID: {} on attempt {}", productId, attempt, e);
                throw new CriticalEnrichmentException(
                        String.format("Failed to get product info for ID: %d", productId), e);
            }
        }

        // Should never reach here
        throw new CriticalEnrichmentException(
                String.format("Unexpected error: exhausted retries for product ID: %d", productId));
    }

    /**
     * Gets configuration value from environment variables or system properties Tries environment
     * variable first, then system property, then default
     */
    private String getConfigValue(String key, String defaultValue) {
        // Try environment variable first
        String envValue = System.getenv(key);
        if (envValue != null) {
            LOG.debug("Found {} in environment variables", key);
            return envValue;
        }

        // Try system property
        String propValue = System.getProperty(key);
        if (propValue != null) {
            LOG.debug("Found {} in system properties", key);
            return propValue;
        }

        LOG.debug("Using default value for {}", key);
        return defaultValue;
    }

    private Row createEnrichmentRow(ProductInfo productInfo) {
        Row row = new Row(9);
        row.setField(0, productInfo.getL0Category());
        row.setField(1, productInfo.getL0CategoryId());
        row.setField(2, productInfo.getL1Category());
        row.setField(3, productInfo.getL1CategoryId());
        row.setField(4, productInfo.getL2Category());
        row.setField(5, productInfo.getL2CategoryId());
        row.setField(6, productInfo.getBrand());
        row.setField(7, productInfo.getProductName());
        row.setField(8, productInfo.getProductType());
        return row;
    }

    @Override
    public void close() throws Exception {
        super.close();

        if (redisClient != null) {
            redisClient.close();
        }

        if (cmsApiClient != null) {
            cmsApiClient.close();
        }

        if (piServiceClient != null) {
            piServiceClient.close();
        }

        LOG.info("ProductEnrichmentTableFunction closed");
    }
}
