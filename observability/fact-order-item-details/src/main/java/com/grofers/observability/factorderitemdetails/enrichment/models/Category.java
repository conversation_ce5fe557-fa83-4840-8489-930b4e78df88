package com.grofers.observability.factorderitemdetails.enrichment.models;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

/** Category model for enrichment: */
@JsonIgnoreProperties(ignoreUnknown = true)
public class Category {
    @JsonProperty("id")
    private Integer id;

    @JsonProperty("name")
    private String name;

    @JsonProperty("type")
    private String type;

    @JsonProperty("parent_category_id")
    private Integer parentId;

    @JsonProperty("allow_primary")
    private Boolean primary;

    @JsonProperty("level")
    private Integer level;

    @JsonProperty("is_leaf")
    private Boolean leaf;

    // Default constructor
    public Category() {}

    // Constructor with all fields
    public Category(
            Integer id,
            String name,
            String type,
            Integer parentId,
            Boolean primary,
            Integer level,
            Boolean leaf) {
        this.id = id;
        this.name = name;
        this.type = type;
        this.parentId = parentId;
        this.primary = primary;
        this.level = level;
        this.leaf = leaf;
    }

    // Getters and Setters
    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public Integer getParentId() {
        return parentId;
    }

    public void setParentId(Integer parentId) {
        this.parentId = parentId;
    }

    public Boolean getPrimary() {
        return primary;
    }

    public void setPrimary(Boolean primary) {
        this.primary = primary;
    }

    public Integer getLevel() {
        return level;
    }

    public void setLevel(Integer level) {
        this.level = level;
    }

    public Boolean getLeaf() {
        return leaf;
    }

    public void setLeaf(Boolean leaf) {
        this.leaf = leaf;
    }

    @Override
    public String toString() {
        return "Category{"
                + "id="
                + id
                + ", name='"
                + name
                + '\''
                + ", type='"
                + type
                + '\''
                + ", parentId="
                + parentId
                + ", primary="
                + primary
                + ", level="
                + level
                + ", leaf="
                + leaf
                + '}';
    }
}
