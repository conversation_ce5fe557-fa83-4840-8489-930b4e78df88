ORDER_LIFECYCLE_EVENTS_SOURCE_BROKERS = ****************************************.groupblinkit-agent.kafka.discoveryv2.prod-y.ap-southeast-1.warpstream.com:9092
ORDER_LIFECYCLE_EVENTS_SOURCE_CONSUMER_GROUP_ID = blinkit.test.data.flink-fact-order-item-details
ORDER_LIFECYCLE_EVENTS_SOURCE_TOPIC = blinkit.order.lifecycle-events
ORDER_ITEM_FACT_SINK_BROKERS = b-1.testdatacommon.vkkz3t.c5.kafka.ap-southeast-1.amazonaws.com:9092,b-2.testdatacommon.vkkz3t.c5.kafka.ap-southeast-1.amazonaws.com:9092
ORDER_ITEM_FACT_SINK_TOPIC = observability.test.metrics.fact-order-item-details
STATE_BACKEND_LOCATION = s3a://grofers-test-dse-singapore/flink-streams/observability/fact-order-item-details/checkpoints/
