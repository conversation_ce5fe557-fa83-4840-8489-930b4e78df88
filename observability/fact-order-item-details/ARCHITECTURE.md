# Product Enrichment Architecture

## Overview

This document describes the architecture for product enrichment in the Flink job `fact-order-item-details`. The enrichment adds product category hierarchy, brand information, and product details to order item data.

## Architecture Diagram

```mermaid
graph TB
	subgraph "Flink Job"
		A[Order Item Stream] --> B[ProductEnrichmentTableFunction]
		B --> C[Local Cache Check]
		C -->|Cache Hit ~0.1ms| D[Return Cached Data]
		C -->|Cache Miss| E[ProductEnrichmentService]
		E --> F[Redis Cache Check]
		F -->|Cache Hit ~1-5ms| G[Cache Locally & Return]
		F -->|Cache Miss| H[Fetch from APIs]
		H --> I[CMS API Client]
		H --> J[Category Mapping API]
		I --> K[Product Details API]
		J --> L[Category Hierarchy API]
		K --> M[Merge & Process Data]
		L --> M
		M --> N[Cache in Redis & Local]
		N --> O[Return Enriched Data]
		D --> P[Enriched Order Item]
		G --> P
		O --> P
	end

	subgraph "External Services"
		Q[CMS API]
		R[Redis Cache]
	end

	subgraph "Security"
		S[Vault Secrets]
		S --> T[Redis Credentials]
		S --> U[CMS API Tokens]
	end

	subgraph "Local Cache"
		V[Caffeine Cache<br/>50K entries<br/>1-day TTL]
	end

	I --> Q
	J --> Q
	F --> R
	N --> R
	C --> V
	G --> V
	N --> V
	T --> R
	U --> Q

	style A fill:#e1f5fe
	style P fill:#c8e6c9
	style Q fill:#fff3e0
	style R fill:#fce4ec
	style S fill:#f3e5f5
	style V fill:#e8f5e8
```

## Components

### 1. ProductEnrichmentTableFunction
- **Purpose**: Main Flink table function for product enrichment
- **Input**: Product ID from order item stream
- **Output**: Enriched row with category hierarchy, brand, and product details
- **Features**:
- Fail-fast error handling for data quality
- Synchronous processing with timeout
- Environment-based configuration

### 2. ProductEnrichmentService
- **Purpose**: Core enrichment logic and orchestration
- **Responsibilities**:
- Redis cache management
- API orchestration
- Data mapping and transformation
- Error handling and fallbacks

### 3. CmsApiClient
- **Purpose**: HTTP client for CMS API interactions
- **Features**:
- Retry logic with exponential backoff
- Connection pooling
- Timeout configuration
- Secure authentication

### 4. Redis Cache Layer
- **Purpose**: High-performance caching for product data
- **Cache Keys**:
- `product_category_mappings:{productId}` - Individual product data
- `category_child_parent_mapping` - Category hierarchy mapping
- **TTL**: 1 day for products, 2 days for category mapping

## Data Flow

### 1. Local Cache Hit Path (Fastest - ~0.1ms)
```
Order Item → ProductEnrichmentTableFunction → Local Cache → Return Cached Data
```

### 2. Redis Cache Hit Path (Fast - ~1-5ms)
```
Order Item → ProductEnrichmentTableFunction → Local Cache Miss → Redis Cache → Cache Locally → Return Data
```

### 3. API Fetch Path (Slow - ~100-500ms)
```
Order Item → ProductEnrichmentTableFunction → Local Cache Miss → Redis Cache Miss → ProductEnrichmentService
	↓
Parallel API Calls:
	├── CMS Product Details API
	└── CMS Category Hierarchy API
	↓
Data Mapping & Category Traversal → Cache in Redis & Local → Return Enriched Data
```

## Security Integration

### Vault Integration
- **Pattern**: Containerized environment variable injection
- **Format**: `-Dcontainerized.master.env.SECRET_NAME=value`
- **Secrets**:
- `REDIS_HOST_{ENV}` - Redis connection details
- `REDIS_DB_{ENV}` - Redis database number
- `CMS_API_URL_{ENV}` - CMS API endpoint
- `CMS_API_AUTHORIZATION_{ENV}` - API authentication token

### Environment Support
- **Stage**: Uses `_STAGE` suffix for secrets
- **Production**: Uses `_PROD` suffix for secrets
- **Auto-detection**: Based on `environment` system property

## Output Schema

The enrichment function returns a row with the following fields:

| Field | Type | Description |
|-------|------|-------------|
| `l0_category` | STRING | Top-level category name |
| `l0_category_id` | INT | Top-level category ID |
| `l1_category` | STRING | Second-level category name |
| `l1_category_id` | INT | Second-level category ID |
| `l2_category` | STRING | Third-level category name |
| `l2_category_id` | INT | Third-level category ID |
| `brand_name` | STRING | Product brand |
| `product_name` | STRING | Product name |
| `product_type` | STRING | Product type/classification |

## Performance Characteristics

### Tiered Cache Performance
- **Local Cache Hit Ratio**: ~80-90% (for repeated product IDs within task)
- **Local Cache Response Time**: ~0.1ms (in-memory)
- **Redis Cache Hit Ratio**: ~90% (overall product data)
- **Redis Cache Response Time**: ~1-5ms (network call)
- **Cache TTL**: 1 day (synchronized between local and Redis)

### Local Cache Configuration
- **Maximum Size**: 50,000 entries per task
- **Memory Usage**: ~25MB per task (estimated)
- **Eviction Policy**: LRU (Least Recently Used)
- **TTL**: 1 day (matches Redis TTL)

### API Performance
- **Connection Timeout**: 5 seconds
- **Socket Timeout**: 5 seconds
- **Retry Policy**: 3 attempts with exponential backoff
- **Error Handling**: Fail-fast on all API failures

### Throughput
- **Local Cache Hits**: >50,000 RPS per task
- **Redis Cache Hits**: >10,000 RPS per task
- **API Requests**: ~500 RPS (with retries)

## Error Handling

### Fail-Fast Strategy
- **Philosophy**: Prioritize data quality over availability
- **Behavior**: Job fails immediately on enrichment failures
- **Rationale**: Prevent processing incomplete/corrupted data

### Critical Failures (Job Termination)
- **Null Product IDs**: Indicates upstream data quality issues
- **Redis Connection Failures**: Cache layer unavailable
- **API Failures**: Source data unavailable (PI service or CMS API)
- **Timeout Exceptions**: Service degradation beyond acceptable limits

### Exception Hierarchy
- **CriticalEnrichmentException**: Fails job immediately (extends RuntimeException)

### Logging
- **Error Logging**: Full stack traces with context
- **Critical Logging**: Job termination reasons
- **Debug Logging**: Cache operations and API responses

## Monitoring & Observability

### Key Metrics
- Cache hit/miss ratios
- API response times
- Enrichment success/failure rates

### Health Checks
- Redis connectivity
- API availability (PI service and CMS API)

## Future Enhancements

1. **Async Processing**: Migrate to async table function for better throughput
2. **Local Caching**: Add in-memory cache layer for frequently accessed data
3. **Metrics Integration**: Add custom Flink metrics for monitoring
4. **Batch Optimization**: Batch multiple product lookups in single API call
