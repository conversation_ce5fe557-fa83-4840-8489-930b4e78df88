/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.grofers.observability.factorderdetailsv2;

import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.contrib.streaming.state.EmbeddedRocksDBStateBackend;
import org.apache.flink.streaming.api.CheckpointingMode;
import org.apache.flink.streaming.api.environment.CheckpointConfig;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.table.annotation.DataTypeHint;
import org.apache.flink.table.api.StatementSet;
import org.apache.flink.table.api.bridge.java.StreamTableEnvironment;
import org.apache.flink.table.functions.ScalarFunction;
import org.apache.flink.types.Row;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.grofers.observability.factorderdetailsv2.configs.JobConfigManager;

import java.io.IOException;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeFormatterBuilder;
import java.time.temporal.ChronoField;
import java.util.*;

import static com.grofers.observability.factorderdetailsv2.Queries.*;
import static com.grofers.observability.factorderdetailsv2.configs.JobConfig.*;

/**
 * Order Metrics Job
 *
 * <p>For a tutorial how to write a Flink streaming application, check the tutorials and examples on
 * the <a href="https://flink.apache.org/docs/stable/">Flink Website</a>.
 *
 * <p>To package your application into a JAR file for execution, run 'mvn clean package' on the
 * command line.
 *
 * <p>If you change the name of the main class (with the public static void main(String[] args))
 * method, change the respective entry in the POM.xml file (simply search for 'mainClass').
 */
public class FactOrderDetails {
    public static class SumOfItemQuantityFunction extends ScalarFunction {
        public Integer eval(
                @DataTypeHint(
                                "ARRAY<ROW<`procured_quantity` INT, `quantity` INT,"
                                    + " `cancellations` ARRAY<ROW<`reason` STRING, `quantity`"
                                    + " INT>>, `categories` ARRAY<STRING>, `product_meta`"
                                    + " ROW<`attributes` ARRAY<ROW<`id` INT, `name` STRING>>>>>")
                        Row[] arr,
                String key) {
            if (arr != null) {
                int sum = 0;
                for (Row i : arr) {
                    if (i.getFieldAs(key) != null) {
                        sum += (int) i.getFieldAs(key);
                    }
                }
                return sum;
            }
            return null;
        }
    }

    public static class GetFillRateFunction extends ScalarFunction {
        private static final List<String> PNA_CANCELLATION_REASONS =
                new ArrayList<String>() {
                    {
                        add("ITEM_CANCEL_PROCUREMENT_UPDATE_MISSING");
                    }
                };

        public Double eval(
                @DataTypeHint(
                                "ARRAY<ROW<`procured_quantity` INT, `quantity` INT,"
                                    + " `cancellations` ARRAY<ROW<`reason` STRING, `quantity`"
                                    + " INT>>, `categories` ARRAY<STRING>, `product_meta`"
                                    + " ROW<`attributes` ARRAY<ROW<`id` INT, `name` STRING>>>>>")
                        Row[] arr,
                boolean includePNAItems) {

            if (arr != null) {
                int quantity = 0, procured_quantity = 0;
                double sm_fill_rate = 100.0;

                for (Row i : arr) {
                    // Aggregate quantity and procured_quantity
                    if (i.getField("quantity") != null && i.getField("procured_quantity") != null) {
                        quantity += (int) i.getField("quantity");
                        procured_quantity += (int) i.getField("procured_quantity");
                    }

                    // Aggregate cancelled quantity for each reason
                    Row[] cancellations = i.getFieldAs("cancellations");
                    if (cancellations != null) {
                        for (Row cancellation : cancellations) {
                            String cancellationReason = cancellation.getFieldAs("reason");
                            Integer cancelledQuantity = cancellation.getFieldAs("quantity");
                            if (cancelledQuantity != null
                                    && PNA_CANCELLATION_REASONS.contains(cancellationReason)) {
                                sm_fill_rate = 0.0;
                                break;
                                // if even a single item is marked as PNA(Product not available)
                                // then make SM fill rate as 0 and don't check further cancellation
                                // reasons
                            }
                        }
                    }
                }
                if (quantity == 0) return null;

                double fill_rate = ((double) procured_quantity / (double) quantity) * 100;

                return includePNAItems ? fill_rate : sm_fill_rate;
            }
            return null;
        }
    }

    public static class GetTypeOfOrderFunction extends ScalarFunction {
        public String eval(
                @DataTypeHint("ARRAY< ROW <`id` INT, `type` VARCHAR(63) >>") Row[] arr,
                Integer key) {
            if (arr != null) {
                for (Row i : arr) {
                    if ((int) i.getFieldAs("id") == key) {
                        return i.getFieldAs("type").toString();
                    }
                }
            }
            return null;
        }
    }

    public static class GetTimeStampFromOrderEventsFunction extends ScalarFunction {
        public Long eval(
                @DataTypeHint(
                                "ARRAY< ROW< `event_state_change_to` STRING, `event_type` STRING,"
                                    + " `timestamp` STRING, `extra` ROW< `meta` ROW<"
                                    + " `batched_order_ids` ARRAY<STRING>, `picking_end_time`"
                                    + " STRING >, `partner_emp_id` STRING, `picker_imei` STRING,"
                                    + " `picker_name` STRING, `timestamp` STRING,"
                                    + " `assignment_queued_ts` STRING, `pharmacy_details`"
                                    + " ROW<`doctor_details` ROW<`doctor_name` STRING,"
                                    + " `doctor_gender` STRING>, `reasons` STRING, `source`"
                                    + " STRING>, `reason_code` STRING, `trip_id` STRING >>>")
                        Row[] arr,
                String eventValue) {
            if (arr != null) {
                String timestamp = null;
                for (Row i : arr) {
                    if (i.getFieldAs("event_type").equals(eventValue)
                            || (i.getFieldAs("event_type").equals("state_change")
                                    && i.getFieldAs("event_state_change_to").equals(eventValue))) {
                        if (timestamp == null
                                || i.getFieldAs("timestamp").toString().compareTo(timestamp) > 0) {
                            timestamp = i.getFieldAs("timestamp").toString();
                        }
                    }
                }
                return getEpochFromTimestamp(timestamp);
            }
            return null;
        }
    }

    public static class GetIfBatchedOrderFromOrderEventsFunction extends ScalarFunction {
        public boolean eval(
                @DataTypeHint(
                                "ARRAY< ROW< `event_state_change_to` STRING, `event_type` STRING,"
                                    + " `timestamp` STRING, `extra` ROW< `meta` ROW<"
                                    + " `batched_order_ids` ARRAY<STRING>, `picking_end_time`"
                                    + " STRING >, `partner_emp_id` STRING, `picker_imei` STRING,"
                                    + " `picker_name` STRING, `timestamp` STRING,"
                                    + " `assignment_queued_ts` STRING, `pharmacy_details`"
                                    + " ROW<`doctor_details` ROW<`doctor_name` STRING,"
                                    + " `doctor_gender` STRING>, `reasons` STRING, `source`"
                                    + " STRING>, `reason_code` STRING, `trip_id` STRING >>>")
                        Row[] arr) {
            if (arr != null) {
                for (Row row : arr) {
                    if (row != null) {
                        if (row.getField("event_state_change_to") != null
                                && row.getField("event_state_change_to")
                                        .toString()
                                        .equals("ENROUTE")
                                && row.getField("extra") != null) {
                            Row extra = (Row) row.getField("extra");
                            if (extra != null && extra.getField("meta") != null) {
                                Row meta = (Row) extra.getField("meta");
                                if (meta != null && meta.getField("batched_order_ids") != null) {
                                    List<String> batchedOrderIds =
                                            Arrays.asList(
                                                    (String[]) meta.getField("batched_order_ids"));
                                    if (batchedOrderIds != null && batchedOrderIds.size() > 0) {
                                        return true;
                                    }
                                }
                            }
                        }
                    }
                }
            }
            return false;
        }
    }

    public static class GetIfTagExistsOrderFunction extends ScalarFunction {
        public boolean eval(
                @DataTypeHint(
                                "ROW < `tags` ARRAY<STRING>, `slot_charge` DOUBLE,"
                                    + " `checkout_properties` ROW < `slot_charge` DOUBLE >,"
                                    + " `serviceability` ROW <`eta` INT, `serviceability_reason`"
                                    + " STRING, `surge_charge_v2` ROW < `source` STRING,"
                                    + " `surge_amount` DOUBLE >, `session_id` STRING, `components`"
                                    + " ARRAY< ROW<`name` STRING, `duration` DOUBLE> >, `batching`"
                                    + " ROW<`handshake_time` DOUBLE> >>")
                        Row row,
                String tag) {
            if (row != null) {
                if (row.getField("tags") != null) {
                    List<String> tags = Arrays.asList((String[]) row.getField("tags"));
                    if (tags != null && tags.contains(tag)) {
                        return true;
                    }
                }
            }
            return false;
        }
    }

    public static class GetPaasMetrics extends ScalarFunction {
        private static final ObjectMapper objectMapper = new ObjectMapper();

        public String eval(
                @DataTypeHint(
                                "ARRAY<ROW( `procured_quantity` INT, `quantity` INT,"
                                    + " `cancellations` ARRAY<ROW<`reason` STRING, `quantity`"
                                    + " INT>>, `categories` ARRAY<STRING>, `product_meta`"
                                    + " ROW<`attributes` ARRAY<ROW<`id` INT, `name` STRING>>> )>")
                        Row[] arr,
                String[] keys) {

            if (arr != null && keys != null) {
                Map<String, Object> resultMap = new HashMap<>();
                int passSku = 0;

                // Initialize result map with keys and zero values
                for (String key : keys) {
                    resultMap.put(key, 0);
                }

                for (Row row : arr) {
                    String[] categories = row.getFieldAs("categories");
                    if (categories != null) {
                        List<String> categoriesList = Arrays.asList(categories);
                        if (categoriesList.contains("Print as a service")) {
                            for (String key : keys) {
                                if (row.getField(key) != null) {
                                    int qty = (int) row.getFieldAs(key);
                                    resultMap.put(key, (Integer) resultMap.get(key) + qty);
                                }
                            }
                            passSku++;
                        }
                    }
                }

                // Add SKU count to result map
                resultMap.put("sku", passSku);

                // PAAS order event flags removed from paas_metrics
                // These will be handled at root level instead

                // Convert the result map to JSON string
                try {
                    return objectMapper.writeValueAsString(resultMap);
                } catch (JsonProcessingException e) {
                    e.printStackTrace();
                    return null;
                }
            }

            return null;
        }
    }

    public static class GetBatchedOrderIdsFunction extends ScalarFunction {
        public String[] eval(
                @DataTypeHint(
                                "ARRAY< ROW< `event_state_change_to` STRING, `event_type` STRING,"
                                    + " `timestamp` STRING, `extra` ROW< `meta` ROW<"
                                    + " `batched_order_ids` ARRAY<STRING>, `picking_end_time`"
                                    + " STRING >, `partner_emp_id` STRING, `picker_imei` STRING,"
                                    + " `picker_name` STRING, `timestamp` STRING,"
                                    + " `assignment_queued_ts` STRING, `pharmacy_details`"
                                    + " ROW<`doctor_details` ROW<`doctor_name` STRING,"
                                    + " `doctor_gender` STRING>, `reasons` STRING, `source`"
                                    + " STRING>, `reason_code` STRING, `trip_id` STRING >>>")
                        Row[] arr) {
            if (arr != null) {
                for (Row row : arr) {
                    if (row != null) {
                        // Only check events where event_state_change_to is "ENROUTE"
                        // as batched_order_ids is only present in ENROUTE events
                        String eventStateChangeTo =
                                row.getField("event_state_change_to") != null
                                        ? row.getField("event_state_change_to").toString()
                                        : null;

                        if ("ENROUTE".equals(eventStateChangeTo) && row.getField("extra") != null) {
                            Row extra = (Row) row.getField("extra");
                            if (extra != null && extra.getField("meta") != null) {
                                Row meta = (Row) extra.getField("meta");
                                if (meta != null && meta.getField("batched_order_ids") != null) {
                                    String[] batchedOrderIds =
                                            (String[]) meta.getField("batched_order_ids");
                                    if (batchedOrderIds != null && batchedOrderIds.length > 0) {
                                        return batchedOrderIds;
                                    }
                                }
                            }
                        }
                    }
                }
            }
            return null;
        }
    }

    public static class GetTaskDuration extends ScalarFunction {
        public Double eval(
                @DataTypeHint(
                                "ROW <`eta` INT, `serviceability_reason` STRING, `surge_charge_v2`"
                                        + " ROW < `source` STRING, `surge_amount` DOUBLE >,"
                                        + " `session_id` STRING, `components` ARRAY< ROW<`name`"
                                        + " STRING, `duration` DOUBLE> >, `batching`"
                                        + " ROW<`handshake_time` DOUBLE>>")
                        Row row,
                List<String> componentNames) {

            if (row != null && row.getField("components") != null) {
                Row[] components = (Row[]) row.getField("components");
                double sumDuration = 0.0;

                for (Row component : components) {
                    String name = (String) component.getField("name");
                    if (name != null && componentNames.contains(name)) {
                        Double duration = (Double) component.getField("duration");
                        if (duration != null) {
                            sumDuration += duration;
                        }
                    }
                }
                return sumDuration;
            }
            return null;
        }
    }

    public static class GetPartnerEmpIdFromOrderEventsFunction extends ScalarFunction {
        public String eval(
                @DataTypeHint(
                                "ARRAY< ROW< `event_state_change_to` STRING, `event_type` STRING,"
                                    + " `timestamp` STRING, `extra` ROW< `meta` ROW<"
                                    + " `batched_order_ids` ARRAY<STRING>, `picking_end_time`"
                                    + " STRING >, `partner_emp_id` STRING, `picker_imei` STRING,"
                                    + " `picker_name` STRING, `timestamp` STRING,"
                                    + " `assignment_queued_ts` STRING, `pharmacy_details`"
                                    + " ROW<`doctor_details` ROW<`doctor_name` STRING,"
                                    + " `doctor_gender` STRING>, `reasons` STRING, `source`"
                                    + " STRING>, `reason_code` STRING, `trip_id` STRING >>>")
                        Row[] arr) {
            if (arr != null) {
                String partnerEmpId = null, timestamp = null;
                for (Row row : arr) {
                    if (row != null) {
                        if (row.getField("event_type")
                                        .toString()
                                        .equals("delivery_partner_reassigned")
                                || row.getField("event_type")
                                        .toString()
                                        .equals("delivery_partner_assigned")) {
                            if (timestamp == null
                                    || row.getFieldAs("timestamp").toString().compareTo(timestamp)
                                            > 0) {
                                timestamp = row.getFieldAs("timestamp").toString();
                                Row extra = (Row) row.getField("extra");
                                if (extra != null && extra.getField("partner_emp_id") != null) {
                                    partnerEmpId = extra.getField("partner_emp_id").toString();
                                }
                            }
                        }
                    }
                }
                return partnerEmpId;
            }
            return null;
        }
    }

    public static class GetPickerDetailsFromOrderEventsFunction extends ScalarFunction {
        public String eval(
                @DataTypeHint(
                                "ARRAY< ROW< `event_state_change_to` STRING, `event_type` STRING,"
                                    + " `timestamp` STRING, `extra` ROW< `meta` ROW<"
                                    + " `batched_order_ids` ARRAY<STRING>, `picking_end_time`"
                                    + " STRING >, `partner_emp_id` STRING, `picker_imei` STRING,"
                                    + " `picker_name` STRING, `timestamp` STRING,"
                                    + " `assignment_queued_ts` STRING, `pharmacy_details`"
                                    + " ROW<`doctor_details` ROW<`doctor_name` STRING,"
                                    + " `doctor_gender` STRING>, `reasons` STRING, `source`"
                                    + " STRING>, `reason_code` STRING, `trip_id` STRING >>>")
                        Row[] arr,
                String pickerField,
                String event_type,
                boolean is_timestamp) {
            if (arr != null) {
                String pickerFieldValue = null, timestamp = null;
                for (Row row : arr) {
                    if (row != null) {
                        if (event_type.equals(row.getFieldAs("event_type").toString())
                                || ("state_change".equals(row.getFieldAs("event_type"))
                                        && event_type.equals(
                                                row.getFieldAs("event_state_change_to")))) {
                            if (timestamp == null
                                    || row.getFieldAs("timestamp").toString().compareTo(timestamp)
                                            > 0) {
                                timestamp = row.getFieldAs("timestamp").toString();
                                Row extra = (Row) row.getField("extra");
                                if (extra != null) {
                                    try {
                                        Object field = extra.getField(pickerField);
                                        pickerFieldValue = extra.getFieldAs(pickerField).toString();
                                    } catch (IllegalArgumentException e) {
                                        Row meta = (Row) extra.getField("meta");
                                        if (meta != null && meta.getField(pickerField) != null) {
                                            pickerFieldValue =
                                                    meta.getFieldAs(pickerField).toString();
                                        }
                                    }
                                    timestamp = is_timestamp ? pickerFieldValue : timestamp;
                                }
                            }
                        }
                    }
                }
                return (is_timestamp && pickerFieldValue != null)
                        ? getEpochFromTimestamp(pickerFieldValue).toString()
                        : pickerFieldValue;
            }
            return null;
        }
    }

    public static class ExtractAdditionalChargeAmount extends ScalarFunction {
        public double eval(
                @DataTypeHint(
                                "ARRAY<ROW< `id` INT, `name` STRING, `amount` DOUBLE, `charge_id`"
                                        + " INT >>")
                        Row[] arr,
                int chargeId) {

            if (arr != null) {
                for (Row row : arr) {
                    if (row != null) {
                        if (row.getField("charge_id") != null
                                && (int) row.getField("charge_id") == chargeId
                                && row.getField("amount") != null) {
                            return (Double) row.getField("amount");
                        }
                    }
                }
            }
            return 0.0;
        }
    }

    public static class GetIfEventExistsOrderFunction extends ScalarFunction {
        public boolean eval(
                @DataTypeHint(
                                "ARRAY< ROW< `event_state_change_to` STRING, `event_type` STRING,"
                                    + " `timestamp` STRING, `extra` ROW< `meta` ROW<"
                                    + " `batched_order_ids` ARRAY<STRING>, `picking_end_time`"
                                    + " STRING >, `partner_emp_id` STRING, `picker_imei` STRING,"
                                    + " `picker_name` STRING, `timestamp` STRING,"
                                    + " `assignment_queued_ts` STRING, `pharmacy_details`"
                                    + " ROW<`doctor_details` ROW<`doctor_name` STRING,"
                                    + " `doctor_gender` STRING>, `reasons` STRING, `source`"
                                    + " STRING>, `reason_code` STRING, `trip_id` STRING >>>")
                        Row[] arr,
                String eventType) {
            if (arr != null) {
                for (Row row : arr) {
                    if (row != null && row.getField("event_type") != null) {
                        if (row.getField("event_type").toString().equals(eventType)) {
                            return true;
                        }
                    }
                }
            }
            return false;
        }
    }

    public static class GetTripIdFromOrderEventsFunction extends ScalarFunction {
        public String eval(
                @DataTypeHint(
                                "ARRAY< ROW< `event_state_change_to` STRING, `event_type` STRING,"
                                    + " `timestamp` STRING, `extra` ROW< `meta` ROW<"
                                    + " `batched_order_ids` ARRAY<STRING>, `picking_end_time`"
                                    + " STRING >, `partner_emp_id` STRING, `picker_imei` STRING,"
                                    + " `picker_name` STRING, `timestamp` STRING,"
                                    + " `assignment_queued_ts` STRING, `pharmacy_details`"
                                    + " ROW<`doctor_details` ROW<`doctor_name` STRING,"
                                    + " `doctor_gender` STRING>, `reasons` STRING, `source`"
                                    + " STRING>, `reason_code` STRING, `trip_id` STRING >>>")
                        Row[] arr) {
            if (arr != null) {
                String tripId = null, latestTimestamp = null;
                for (Row row : arr) {
                    if (row != null && row.getField("event_type") != null) {
                        String eventType = row.getField("event_type").toString();
                        if (eventType.equals("delivery_partner_assigned")
                                || eventType.equals("delivery_partner_reassigned")) {
                            String timestamp = row.getFieldAs("timestamp");
                            if (timestamp != null
                                    && (latestTimestamp == null
                                            || timestamp.compareTo(latestTimestamp) > 0)) {
                                latestTimestamp = timestamp;
                                Row extra = (Row) row.getField("extra");
                                if (extra != null) {
                                    try {
                                        // Try to get trip_id directly from extra
                                        Object tripIdField = extra.getField("trip_id");
                                        if (tripIdField != null) {
                                            tripId = tripIdField.toString();
                                        }
                                    } catch (IllegalArgumentException e) {
                                        // trip_id might be in a different location or not present
                                        tripId = null;
                                    }
                                }
                            }
                        }
                    }
                }
                return tripId;
            }
            return null;
        }
    }

    public static class GetComponentDuration extends ScalarFunction {
        public Double eval(
                @DataTypeHint("ARRAY< ROW<`name` STRING, `duration` DOUBLE> >") Row[] components,
                String componentName) {

            if (components != null) {
                for (Row component : components) {
                    if (component != null && component.getField("name") != null) {
                        String name = (String) component.getField("name");
                        if (name != null && name.equals(componentName)) {
                            Double duration = (Double) component.getField("duration");
                            return duration;
                        }
                    }
                }
            }
            return null;
        }
    }

    public static class GetPharmaEventTimestamp extends ScalarFunction {
        public Long eval(
                @DataTypeHint(
                                "ARRAY< ROW< `event_state_change_to` STRING, `event_type` STRING,"
                                    + " `timestamp` STRING, `extra` ROW< `meta` ROW<"
                                    + " `batched_order_ids` ARRAY<STRING>, `picking_end_time`"
                                    + " STRING >, `partner_emp_id` STRING, `picker_imei` STRING,"
                                    + " `picker_name` STRING, `timestamp` STRING,"
                                    + " `assignment_queued_ts` STRING, `pharmacy_details`"
                                    + " ROW<`doctor_details` ROW<`doctor_name` STRING,"
                                    + " `doctor_gender` STRING>, `reasons` STRING, `source`"
                                    + " STRING>, `reason_code` STRING, `trip_id` STRING >>>")
                        Row[] orderEvents,
                String eventName) {
            return getEventTimestamp(orderEvents, eventName, null, null);
        }

        public Long eval(
                @DataTypeHint(
                                "ARRAY< ROW< `event_state_change_to` STRING, `event_type` STRING,"
                                    + " `timestamp` STRING, `extra` ROW< `meta` ROW<"
                                    + " `batched_order_ids` ARRAY<STRING>, `picking_end_time`"
                                    + " STRING >, `partner_emp_id` STRING, `picker_imei` STRING,"
                                    + " `picker_name` STRING, `timestamp` STRING,"
                                    + " `assignment_queued_ts` STRING, `pharmacy_details`"
                                    + " ROW<`doctor_details` ROW<`doctor_name` STRING,"
                                    + " `doctor_gender` STRING>, `reasons` STRING, `source`"
                                    + " STRING>, `reason_code` STRING, `trip_id` STRING >>>")
                        Row[] orderEvents,
                String eventName,
                String approvedByField,
                String approvedByValue) {
            return getEventTimestamp(orderEvents, eventName, approvedByField, approvedByValue);
        }

        private Long getEventTimestamp(
                Row[] orderEvents,
                String eventName,
                String approvedByField,
                String approvedByValue) {
            if (orderEvents != null) {
                for (Row event : orderEvents) {
                    if (event != null && event.getField("event_type") != null) {
                        String eventType = event.getField("event_type").toString();
                        if (eventType.equals(eventName)) {
                            // Check additional condition if specified
                            if (approvedByField != null && approvedByValue != null) {
                                Row extra = (Row) event.getField("extra");
                                if (extra != null && extra.getField(approvedByField) != null) {
                                    String actualValue = extra.getField(approvedByField).toString();
                                    if (!actualValue.equals(approvedByValue)) {
                                        continue; // Skip this event
                                    }
                                }
                            }

                            String timestamp = event.getFieldAs("timestamp");
                            if (timestamp != null) {
                                return getEpochFromTimestamp(timestamp);
                            }
                        }
                    }
                }
            }
            return null;
        }
    }

    public static class GetPharmaRejectionReason extends ScalarFunction {
        public String eval(
                @DataTypeHint(
                                "ARRAY< ROW< `event_state_change_to` STRING, `event_type` STRING,"
                                    + " `timestamp` STRING, `extra` ROW< `meta` ROW<"
                                    + " `batched_order_ids` ARRAY<STRING>, `picking_end_time`"
                                    + " STRING >, `partner_emp_id` STRING, `picker_imei` STRING,"
                                    + " `picker_name` STRING, `timestamp` STRING,"
                                    + " `assignment_queued_ts` STRING, `pharmacy_details`"
                                    + " ROW<`doctor_details` ROW<`doctor_name` STRING,"
                                    + " `doctor_gender` STRING>, `reasons` STRING, `source`"
                                    + " STRING>, `reason_code` STRING, `trip_id` STRING >>>")
                        Row[] orderEvents,
                String eventName) {
            if (orderEvents != null) {
                for (Row event : orderEvents) {
                    if (event != null && event.getField("event_type") != null) {
                        String eventType = event.getField("event_type").toString();
                        if (eventType.equals(eventName)) {
                            Row extra = (Row) event.getField("extra");
                            if (extra != null && extra.getField("pharmacy_details") != null) {
                                Row pharmacyDetails = (Row) extra.getField("pharmacy_details");
                                if (pharmacyDetails != null
                                        && pharmacyDetails.getField("reasons") != null) {
                                    return pharmacyDetails.getField("reasons").toString();
                                }
                            }
                        }
                    }
                }
            }
            return null;
        }
    }

    public static class CheckPrescriptionUploaded extends ScalarFunction {
        public Boolean eval(
                @DataTypeHint(
                                "ROW<`datetime_created` BIGINT, `datetime_updated` BIGINT,"
                                    + " `current_state_name` VARCHAR(50), `device_id` STRING,"
                                    + " `additional_charges_data` ARRAY<ROW<`id` INT, `name`"
                                    + " STRING, `amount` DOUBLE, `charge_id` INT>>, `source`"
                                    + " STRING, `actual_merchant_id` INT, `cart_id` BIGINT,"
                                    + " `total_cost` DOUBLE, `delivery_cost` DOUBLE,"
                                    + " `offer_details` ROW<`total_discount` DOUBLE>, `net_cost`"
                                    + " DOUBLE, `procurement_amount` DOUBLE, `wallet_amount`"
                                    + " DOUBLE, `items` ARRAY<ROW<`procured_quantity` INT,"
                                    + " `quantity` INT, `cancellations` ARRAY<ROW<`reason` STRING,"
                                    + " `quantity` INT>>, `categories` ARRAY<STRING>,"
                                    + " `product_meta` ROW<`attributes` ARRAY<ROW<`id` INT, `name`"
                                    + " STRING>>>>>, `order_events`"
                                    + " ARRAY<ROW<`event_state_change_to` STRING, `event_type`"
                                    + " STRING, `timestamp` STRING, `extra` ROW<`meta`"
                                    + " ROW<`batched_order_ids` ARRAY<STRING>, `picking_end_time`"
                                    + " STRING>, `partner_emp_id` STRING, `picker_imei` STRING,"
                                    + " `picker_name` STRING, `timestamp` STRING,"
                                    + " `assignment_queued_ts` STRING, `pharmacy_details`"
                                    + " ROW<`doctor_details` ROW<`doctor_name` STRING,"
                                    + " `doctor_gender` STRING>, `reasons` STRING, `source`"
                                    + " STRING>, `reason_code` STRING, `trip_id` STRING>>>,"
                                    + " `slot_properties` ROW<`tags` ARRAY<STRING>, `slot_charge`"
                                    + " DOUBLE, `checkout_properties` ROW<`slot_charge` DOUBLE>,"
                                    + " `serviceability` ROW<`eta` INT, `serviceability_reason`"
                                    + " STRING, `surge_charge_v2` ROW<`source` STRING,"
                                    + " `surge_amount` DOUBLE>, `session_id` STRING, `components`"
                                    + " ARRAY<ROW<`name` STRING, `duration` DOUBLE>>, `batching`"
                                    + " ROW<`handshake_time` DOUBLE>>>,"
                                    + " `additional_charges_amount` DOUBLE, `city` STRING,"
                                    + " `checkout_merchant_name` STRING, `merchant` ROW<`city_id`"
                                    + " INT>, `customer` ROW<`id` INT>, `cart` ROW<`orders`"
                                    + " ARRAY<ROW<`id` INT, `type` VARCHAR(63)>>>,"
                                    + " `org_channel_id` VARCHAR(10), `cart_rank_details`"
                                    + " ROW<`cart_rank_last_6m` INT, `order_count_life_time` INT>,"
                                    + " `payment` ROW<`mode` STRING>, `meta` ROW<`location_hex`"
                                    + " STRING, `customer_karma` ROW<`label` STRING, `score`"
                                    + " DOUBLE, `source` STRING>, `is_indian_card_bin` BOOLEAN,"
                                    + " `user_pharmacy_preference_data`"
                                    + " ROW<`patient_contact_details` ROW<`name` STRING>,"
                                    + " `prescription_method_opted` STRING>,"
                                    + " `contains_pharmacy_prescription_required_items` BOOLEAN>>")
                        Row order) {
            // Check if prescription_method_opted == 'prescription_uploaded'
            if (order != null && order.getField("meta") != null) {
                Row meta = (Row) order.getField("meta");
                if (meta != null && meta.getField("user_pharmacy_preference_data") != null) {
                    Row userPharmacyData = (Row) meta.getField("user_pharmacy_preference_data");
                    if (userPharmacyData != null
                            && userPharmacyData.getField("prescription_method_opted") != null) {
                        String prescriptionMethod =
                                userPharmacyData.getField("prescription_method_opted").toString();
                        return "prescription_uploaded".equals(prescriptionMethod);
                    }
                }
            }
            // Default to false if prescription upload status cannot be determined
            return false;
        }
    }

    public static class CheckDoctorApprovalLeg extends ScalarFunction {
        public Boolean eval(Boolean prescriptionUploaded, Long pharmacistRejectedAt) {
            if (prescriptionUploaded != null && prescriptionUploaded) {
                return pharmacistRejectedAt != null;
            } else {
                return true; // Always has doctor approval leg if no prescription uploaded
            }
        }
    }

    public static class ClassifyPharmaProductCounts extends ScalarFunction {
        public String eval(
                @DataTypeHint(
                                "ARRAY<ROW( `procured_quantity` INT, `quantity` INT,"
                                    + " `cancellations` ARRAY<ROW<`reason` STRING, `quantity`"
                                    + " INT>>, `categories` ARRAY<STRING>, `product_meta`"
                                    + " ROW<`attributes` ARRAY<ROW<`id` INT, `name` STRING>>> )>")
                        Row[] items) {
            int rxCount = 0, otxCount = 0;

            if (items != null) {
                for (Row item : items) {
                    if (item != null) {
                        boolean isRx = hasProductAttribute(item, 7572);
                        boolean isOtx = hasProductAttribute(item, 6810);

                        if (isRx || isOtx) {
                            int quantity =
                                    item.getField("quantity") != null
                                            ? (Integer) item.getField("quantity")
                                            : 0;

                            // Prioritize RX over OTX if item has both attributes
                            if (isRx) {
                                rxCount += quantity;
                            } else if (isOtx) {
                                otxCount += quantity;
                            }
                        }
                    }
                }
            }

            return String.format("{\"rx_count\":%d,\"otx_count\":%d}", rxCount, otxCount);
        }

        private boolean hasProductAttribute(Row item, int attributeId) {
            if (item.getField("product_meta") != null) {
                Row productMeta = (Row) item.getField("product_meta");
                if (productMeta != null && productMeta.getField("attributes") != null) {
                    Row[] attributes = (Row[]) productMeta.getField("attributes");
                    for (Row attr : attributes) {
                        if (attr != null && attr.getField("id") != null) {
                            Integer id = (Integer) attr.getField("id");
                            if (id != null && id == attributeId) {
                                return true;
                            }
                        }
                    }
                }
            }
            return false;
        }
    }

    public static class GetDoctorRejectionTimestamp extends ScalarFunction {
        public Long eval(
                @DataTypeHint(
                                "ARRAY< ROW< `event_state_change_to` STRING, `event_type` STRING,"
                                    + " `timestamp` STRING, `extra` ROW< `meta` ROW<"
                                    + " `batched_order_ids` ARRAY<STRING>, `picking_end_time`"
                                    + " STRING >, `partner_emp_id` STRING, `picker_imei` STRING,"
                                    + " `picker_name` STRING, `timestamp` STRING,"
                                    + " `assignment_queued_ts` STRING, `pharmacy_details`"
                                    + " ROW<`doctor_details` ROW<`doctor_name` STRING,"
                                    + " `doctor_gender` STRING>, `reasons` STRING, `source`"
                                    + " STRING>, `reason_code` STRING, `trip_id` STRING >>>")
                        Row[] orderEvents) {
            if (orderEvents != null) {
                for (Row event : orderEvents) {
                    if (event != null && event.getField("event_type") != null) {
                        String eventType = event.getField("event_type").toString();
                        if (eventType.equals("order_cancellation")) {
                            // Check for reason_code in extra field
                            Row extra = (Row) event.getField("extra");
                            if (extra != null && extra.getField("reason_code") != null) {
                                String reasonCode = extra.getField("reason_code").toString();
                                if ("CANCEL_PHARMACY_DOCTOR_CUSTOMER_NOT_AVAILABLE"
                                        .equals(reasonCode)) {
                                    String timestamp = event.getFieldAs("timestamp");
                                    if (timestamp != null) {
                                        return getEpochFromTimestamp(timestamp);
                                    }
                                }
                            }
                        }
                    }
                }
            }
            return null;
        }
    }

    public static class CheckDoctorAssignmentFailed extends ScalarFunction {
        public Boolean eval(
                @DataTypeHint(
                                "ARRAY< ROW< `event_state_change_to` STRING, `event_type` STRING,"
                                    + " `timestamp` STRING, `extra` ROW< `meta` ROW<"
                                    + " `batched_order_ids` ARRAY<STRING>, `picking_end_time`"
                                    + " STRING >, `partner_emp_id` STRING, `picker_imei` STRING,"
                                    + " `picker_name` STRING, `timestamp` STRING,"
                                    + " `assignment_queued_ts` STRING, `pharmacy_details`"
                                    + " ROW<`doctor_details` ROW<`doctor_name` STRING,"
                                    + " `doctor_gender` STRING>, `reasons` STRING, `source`"
                                    + " STRING>, `reason_code` STRING, `trip_id` STRING >>>")
                        Row[] orderEvents) {
            if (orderEvents != null) {
                for (Row event : orderEvents) {
                    if (event != null && event.getField("event_type") != null) {
                        String eventType = event.getField("event_type").toString();
                        if (eventType.equals("order_cancelled")) {
                            Row extra = (Row) event.getField("extra");
                            if (extra != null && extra.getField("pharmacy_details") != null) {
                                Row pharmacyDetails = (Row) extra.getField("pharmacy_details");
                                if (pharmacyDetails != null
                                        && pharmacyDetails.getField("source") != null) {
                                    String source = pharmacyDetails.getField("source").toString();
                                    return "DOCTOR".equals(source);
                                }
                            }
                        }
                    }
                }
            }
            return false;
        }
    }

    private static Long getEpochFromTimestamp(String utcTimestamp) {
        if (utcTimestamp == null) return null;
        // Check if the utcTimestamp is in the form of an epoch
        try {
            long epochTimestamp = Long.parseLong(utcTimestamp);
            // If the parsing succeeds, it means the timestamp is already in epoch format
            return epochTimestamp;
        } catch (NumberFormatException e) {
            // If parsing fails, it means the timestamp is in string format
            // Define two formats to handle both cases
            DateTimeFormatter formatter =
                    new DateTimeFormatterBuilder()
                            .appendPattern("yyyy-MM-dd'T'HH:mm:ss")
                            .optionalStart() // Optional start for milliseconds
                            .appendFraction(ChronoField.MICRO_OF_SECOND, 0, 6, true)
                            .optionalEnd() // Optional end for milliseconds
                            .optionalStart() // Optional start for the timezone offset
                            .appendOffset("+HHMM", "Z") // Offset and 'Z' are optional
                            .optionalEnd() // Optional end for the timezone offset
                            .toFormatter();

            ZonedDateTime zonedDateTimeDate;
            try {
                // First try to parse with the formatter without milliseconds and 'Z'
                zonedDateTimeDate =
                        LocalDateTime.parse(utcTimestamp, formatter).atZone(ZoneId.of("UTC"));
                return zonedDateTimeDate.toInstant().toEpochMilli();
            } catch (java.time.format.DateTimeParseException ex) {
                return null;
            }
        }
    }

    public static void main(String[] args)
            throws JobConfigManager.InvalidEnvironmentException, IOException {
        final StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment();
        final StreamTableEnvironment tEnv = StreamTableEnvironment.create(env);
        tEnv.createTemporarySystemFunction(
                "SumOfItemQuantityFunction", SumOfItemQuantityFunction.class);
        tEnv.createTemporarySystemFunction("GetTypeOfOrderFunction", GetTypeOfOrderFunction.class);
        tEnv.createTemporarySystemFunction(
                "GetTimeStampFromOrderEventsFunction", GetTimeStampFromOrderEventsFunction.class);
        tEnv.createTemporarySystemFunction(
                "GetIfBatchedOrderFromOrderEventsFunction",
                GetIfBatchedOrderFromOrderEventsFunction.class);
        tEnv.createTemporarySystemFunction(
                "GetPartnerEmpIdFromOrderEventsFunction",
                GetPartnerEmpIdFromOrderEventsFunction.class);
        tEnv.createTemporarySystemFunction(
                "GetPickerDetailsFromOrderEventsFunction",
                GetPickerDetailsFromOrderEventsFunction.class);
        tEnv.createTemporarySystemFunction("GetFillRateFunction", GetFillRateFunction.class);
        tEnv.createTemporarySystemFunction(
                "GetIfTagExistsOrderFunction", GetIfTagExistsOrderFunction.class);
        tEnv.createTemporarySystemFunction(
                "ExtractAdditionalChargeAmount", ExtractAdditionalChargeAmount.class);
        tEnv.createTemporarySystemFunction("GetTaskDuration", GetTaskDuration.class);
        tEnv.createTemporarySystemFunction("GetComponentDuration", GetComponentDuration.class);
        tEnv.createTemporarySystemFunction(
                "GetBatchedOrderIdsFunction", GetBatchedOrderIdsFunction.class);
        tEnv.createTemporarySystemFunction("GetPaasMetrics", GetPaasMetrics.class);
        tEnv.createTemporarySystemFunction(
                "GetIfEventExistsOrderFunction", GetIfEventExistsOrderFunction.class);
        tEnv.createTemporarySystemFunction(
                "GetTripIdFromOrderEventsFunction", GetTripIdFromOrderEventsFunction.class);
        tEnv.createTemporarySystemFunction(
                "GetPharmaEventTimestamp", GetPharmaEventTimestamp.class);
        tEnv.createTemporarySystemFunction(
                "GetPharmaRejectionReason", GetPharmaRejectionReason.class);
        tEnv.createTemporarySystemFunction(
                "CheckPrescriptionUploaded", CheckPrescriptionUploaded.class);
        tEnv.createTemporarySystemFunction("CheckDoctorApprovalLeg", CheckDoctorApprovalLeg.class);
        tEnv.createTemporarySystemFunction(
                "ClassifyPharmaProductCounts", ClassifyPharmaProductCounts.class);
        tEnv.createTemporarySystemFunction(
                "GetDoctorRejectionTimestamp", GetDoctorRejectionTimestamp.class);
        tEnv.createTemporarySystemFunction(
                "CheckDoctorAssignmentFailed", CheckDoctorAssignmentFailed.class);
        Configuration configuration = tEnv.getConfig().getConfiguration();
        configuration.setString("table.exec.source.idle-timeout", "1200 s");

        // local-global aggregation depends on mini-batch is enabled
        configuration.setString("table.exec.mini-batch.enabled", "true");
        configuration.setString("table.exec.mini-batch.allow-latency", "1 s");
        configuration.setString("table.exec.mini-batch.size", "500");

        configuration.setString("table.optimizer.agg-phase-strategy", "TWO_PHASE");
        configuration.setString("table.optimizer.distinct-agg.split.enabled", "true");

        ParameterTool params = ParameterTool.fromArgs(args);
        String userEnv = params.getRequired("environment");

        // Get job configs
        JobConfigManager.setJobConfigsPath(userEnv);
        String jobPropertiesPath = JobConfigManager.getJobConfigsPath(userEnv);
        JobConfigManager.setJobConfigs(tEnv, jobPropertiesPath);
        JobConfigManager.getJobConfigs(tEnv);

        // Checkpoint Configs
        env.enableCheckpointing(600000);
        // set mode to exactly-once (default)
        env.getCheckpointConfig().setCheckpointingMode(CheckpointingMode.EXACTLY_ONCE);
        // make sure 900000 ms of progress happen between checkpoints
        env.getCheckpointConfig().setMinPauseBetweenCheckpoints(300000);
        // checkpoints have to complete within this minute, or are discarded
        env.getCheckpointConfig().setCheckpointTimeout(600000);
        // only two consecutive checkpoint failures are tolerated
        env.getCheckpointConfig().setTolerableCheckpointFailureNumber(2);
        // allow only one checkpoint to be in progress at the same time
        env.getCheckpointConfig().setMaxConcurrentCheckpoints(1);
        // enable externalized checkpoints which are retained after job cancellation
        env.getCheckpointConfig()
                .setExternalizedCheckpointCleanup(
                        CheckpointConfig.ExternalizedCheckpointCleanup.DELETE_ON_CANCELLATION);
        // enable unaligned checkpoints
        env.getCheckpointConfig().enableUnalignedCheckpoints();
        // sets a RocksDB checkpoint storage where checkpoint snapshots will be incrementally
        // written
        env.setStateBackend(new EmbeddedRocksDBStateBackend(true));
        env.getCheckpointConfig().setCheckpointStorage(STATE_BACKEND_LOCATION);

        String omsOrderCreateTable =
                String.format(
                        BLINKIT_ORDER_LIFECYCLE_EVENTS,
                        ORDER_LIFECYCLE_EVENTS_SOURCE_TOPIC,
                        ORDER_LIFECYCLE_EVENTS_SOURCE_BROKERS,
                        ORDER_LIFECYCLE_EVENTS_SOURCE_CONSUMER_GROUP_ID);
        tEnv.executeSql(omsOrderCreateTable);

        String omsHPOrderCreateTable =
                String.format(
                        HYPERPURE_ORDER_LIFECYCLE_EVENTS,
                        HYPERPURE_ORDER_LIFECYCLE_EVENTS_SOURCE_TOPIC,
                        ORDER_LIFECYCLE_EVENTS_SOURCE_BROKERS,
                        ORDER_LIFECYCLE_EVENTS_SOURCE_CONSUMER_GROUP_ID);
        tEnv.executeSql(omsHPOrderCreateTable);

        String orderMetricsCreateTable =
                String.format(
                        ORDER_FACT_CREATE_TABLE, ORDER_FACT_SINK_TOPIC, ORDER_FACT_SINK_BROKERS);
        tEnv.executeSql(orderMetricsCreateTable);

        String pharmaMetricsCreateTable =
                String.format(
                        PHARMA_FACT_CREATE_TABLE, PHARMA_FACT_SINK_TOPIC, PHARMA_FACT_SINK_BROKERS);
        tEnv.executeSql(pharmaMetricsCreateTable);

        // Use StatementSet to execute multiple INSERT statements in single execution
        StatementSet statementSet = tEnv.createStatementSet();
        statementSet.addInsertSql(ORDER_FACT_METRIC);
        statementSet.addInsertSql(PHARMA_FACT_METRIC);
        statementSet.execute();
    }
}
