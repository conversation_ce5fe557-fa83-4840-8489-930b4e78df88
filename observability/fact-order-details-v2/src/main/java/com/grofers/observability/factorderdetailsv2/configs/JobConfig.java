package com.grofers.observability.factorderdetailsv2.configs;

public class JobConfig {
    public static String ORDER_LIFECYCLE_EVENTS_SOURCE_BROKERS;
    public static String ORDER_LIFECYCLE_EVENTS_SOURCE_CONSUMER_GROUP_ID;
    public static String ORDER_LIFECYCLE_EVENTS_SOURCE_TOPIC;
    public static String HYPERPURE_ORDER_LIFECYCLE_EVENTS_SOURCE_TOPIC;
    public static String ORDER_FACT_SINK_BROKERS;
    public static String ORDER_FACT_SINK_TOPIC;
    public static String PHARMA_FACT_SINK_BROKERS;
    public static String PHARMA_FACT_SINK_TOPIC;
    public static String STATE_BACKEND_LOCATION;

    public JobConfig(
            String ORDER_LIFECYCLE_EVENTS_SOURCE_BROKERS,
            String ORDER_LIFECYCLE_EVENTS_SOURCE_CONSUMER_GROUP_ID,
            String ORDER_LIFECYCLE_EVENTS_SOURCE_TOPIC,
            String HYPERPURE_ORDER_LIFECYCLE_EVENTS_SOURCE_TOPIC,
            String ORDER_FACT_SINK_BROKERS,
            String ORDER_FACT_SINK_TOPIC,
            String PHARMA_FACT_SINK_BROKERS,
            String PHARMA_FACT_SINK_TOPIC,
            String STATE_BACKEND_LOCATION) {
        JobConfig.ORDER_LIFECYCLE_EVENTS_SOURCE_BROKERS = ORDER_LIFECYCLE_EVENTS_SOURCE_BROKERS;
        JobConfig.ORDER_LIFECYCLE_EVENTS_SOURCE_CONSUMER_GROUP_ID =
                ORDER_LIFECYCLE_EVENTS_SOURCE_CONSUMER_GROUP_ID;
        JobConfig.ORDER_LIFECYCLE_EVENTS_SOURCE_TOPIC = ORDER_LIFECYCLE_EVENTS_SOURCE_TOPIC;
        JobConfig.HYPERPURE_ORDER_LIFECYCLE_EVENTS_SOURCE_TOPIC =
                HYPERPURE_ORDER_LIFECYCLE_EVENTS_SOURCE_TOPIC;
        JobConfig.ORDER_FACT_SINK_BROKERS = ORDER_FACT_SINK_BROKERS;
        JobConfig.ORDER_FACT_SINK_TOPIC = ORDER_FACT_SINK_TOPIC;
        JobConfig.PHARMA_FACT_SINK_BROKERS = PHARMA_FACT_SINK_BROKERS;
        JobConfig.PHARMA_FACT_SINK_TOPIC = PHARMA_FACT_SINK_TOPIC;
        JobConfig.STATE_BACKEND_LOCATION = STATE_BACKEND_LOCATION;
    }
}
