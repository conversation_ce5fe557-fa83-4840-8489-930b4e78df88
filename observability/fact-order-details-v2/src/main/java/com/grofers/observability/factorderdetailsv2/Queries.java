package com.grofers.observability.factorderdetailsv2;

public class Queries {
    public static final String BLINKIT_ORDER_LIFECYCLE_EVENTS =
            "CREATE TABLE IF NOT EXISTS order_lifecycle_events ( order_id INT,  `order`"
                + " ROW(datetime_created BIGINT, datetime_updated BIGINT, current_state_name"
                + " VARCHAR(50), device_id STRING,additional_charges_data ARRAY<ROW< `id` INT,"
                + " `name` STRING, `amount` DOUBLE, `charge_id` INT >>,  source STRING,"
                + " actual_merchant_id INT, cart_id BIGINT, total_cost DOUBLE, delivery_cost"
                + " DOUBLE, offer_details ROW(`total_discount` DOUBLE), net_cost DOUBLE,"
                + " procurement_amount DOUBLE, wallet_amount DOUBLE, items ARRAY<ROW ("
                + " `procured_quantity` INT, `quantity` INT, `cancellations` ARRAY<ROW<`reason`"
                + " STRING, `quantity` INT>>, `categories` ARRAY<STRING>, `product_meta`"
                + " ROW<`attributes` ARRAY<ROW<`id` INT, `name` STRING>>> ) >,  order_events"
                + " ARRAY< ROW(`event_state_change_to` STRING, `event_type` STRING, `timestamp`"
                + " STRING, `extra` ROW(`meta` ROW(`batched_order_ids` ARRAY<STRING>,"
                + " `picking_end_time` STRING), `partner_emp_id` STRING, `picker_imei` STRING,"
                + " `picker_name` STRING, `timestamp` STRING, `assignment_queued_ts` STRING,"
                + " `pharmacy_details` ROW(`doctor_details` ROW(`doctor_name` STRING,"
                + " `doctor_gender` STRING), `reasons` STRING, `source` STRING), `reason_code`"
                + " STRING, `trip_id` STRING))>, `slot_properties` ROW(`tags` ARRAY<STRING>, `slot_charge` DOUBLE,"
                + " `checkout_properties` ROW(`slot_charge` DOUBLE), `serviceability` ROW(`eta`"
                + " INT, `serviceability_reason` STRING, `surge_charge_v2` ROW(`source` STRING,"
                + " `surge_amount` DOUBLE ), `session_id` STRING, `components` ARRAY< ROW(`name`"
                + " STRING, `duration` DOUBLE) >, `batching` ROW(`handshake_time` DOUBLE))),"
                + " additional_charges_amount DOUBLE, `city` STRING, checkout_merchant_name"
                + " STRING, merchant ROW(`city_id` INT), customer ROW( `id` INT), cart"
                + " ROW(`orders` ARRAY< ROW (`id` INT, `type` VARCHAR(63) )>), org_channel_id"
                + " VARCHAR(10), cart_rank_details ROW(`cart_rank_last_6m` INT,"
                + " `order_count_life_time` INT), payment ROW(`mode` STRING), meta"
                + " ROW(`location_hex` STRING, `customer_karma` ROW(`label` STRING, `score`"
                + " DOUBLE, `source` STRING), `is_indian_card_bin` BOOLEAN,"
                + " `user_pharmacy_preference_data` ROW(`patient_contact_details` ROW(`name`"
                + " STRING), `prescription_method_opted` STRING),"
                + " `contains_pharmacy_prescription_required_items` BOOLEAN)), reason_code"
                + " VARCHAR(50),flink_event_time_epoch AS"
                + " TO_TIMESTAMP_LTZ(`order`.datetime_updated, 0),WATERMARK FOR"
                + " flink_event_time_epoch AS flink_event_time_epoch - INTERVAL '60' seconds) WITH"
                + " (  'connector' = 'kafka', 'format' = 'json', 'topic' = '%s',"
                + " 'properties.bootstrap.servers' = '%s', 'properties.group.id' = '%s',"
                + " 'properties.auto.offset.reset' = 'latest',  'scan.startup.mode' ="
                + " 'group-offsets'  )";

    public static final String HYPERPURE_ORDER_LIFECYCLE_EVENTS =
            "CREATE TABLE IF NOT EXISTS hyperpure_order_lifecycle_events ( payload ROW( order_id"
                + " INT, `order` ROW( datetime_created BIGINT, datetime_updated BIGINT,"
                + " current_state_name VARCHAR(50), device_id STRING, additional_charges_data"
                + " ARRAY<ROW< `id` INT, `name` STRING, `amount` DOUBLE, `charge_id` INT >>,"
                + " source STRING, actual_merchant_id INT, cart_id BIGINT, total_cost DOUBLE,"
                + " delivery_cost DOUBLE, offer_details ROW(`total_discount` DOUBLE), net_cost"
                + " DOUBLE, procurement_amount DOUBLE, wallet_amount DOUBLE, items ARRAY<ROW ("
                + " `procured_quantity` INT, `quantity` INT, `cancellations` ARRAY<ROW< `reason`"
                + " STRING, `quantity` INT >>, `categories` ARRAY<STRING>, `product_meta`"
                + " ROW<`attributes` ARRAY<ROW<`id` INT, `name` STRING>>> )>, order_events"
                + " ARRAY<ROW( `event_state_change_to` STRING, `event_type` STRING, `timestamp`"
                + " STRING, `extra` ROW( `meta` ROW( `batched_order_ids` ARRAY<STRING>,"
                + " `picking_end_time` STRING ), `partner_emp_id` STRING, `picker_imei` STRING,"
                + " `picker_name` STRING, `timestamp` STRING, `assignment_queued_ts` STRING,"
                + " `pharmacy_details` ROW(`doctor_details` ROW(`doctor_name` STRING,"
                + " `doctor_gender` STRING), `reasons` STRING, `source` STRING), `reason_code`"
                + " STRING, `trip_id` STRING) )>, `slot_properties` ROW( `tags` ARRAY<STRING>, `slot_charge` DOUBLE,"
                + " `checkout_properties` ROW( `slot_charge` DOUBLE ), `serviceability` ROW( `eta`"
                + " INT, `serviceability_reason` STRING, `surge_charge_v2` ROW( `source` STRING,"
                + " `surge_amount` DOUBLE ), `session_id` STRING, `components` ARRAY<ROW( `name`"
                + " STRING, `duration` DOUBLE )>, `batching` ROW(`handshake_time` DOUBLE) ) ),"
                + " additional_charges_amount DOUBLE, `city` STRING, checkout_merchant_name"
                + " STRING, merchant ROW( `city_id` INT ), customer ROW( `id` INT ), cart ROW("
                + " `orders` ARRAY<ROW( `id` INT, `type` VARCHAR(63) )> ), org_channel_id"
                + " VARCHAR(10), cart_rank_details ROW(`cart_rank_last_6m` INT,"
                + " `order_count_life_time` INT), payment ROW(`mode` STRING), meta"
                + " ROW(`location_hex` STRING, `customer_karma` ROW(`label` STRING, `score`"
                + " DOUBLE, `source` STRING), `is_indian_card_bin` BOOLEAN,"
                + " `user_pharmacy_preference_data` ROW(`patient_contact_details` ROW(`name`"
                + " STRING), `prescription_method_opted` STRING),"
                + " `contains_pharmacy_prescription_required_items` BOOLEAN)), reason_code"
                + " VARCHAR(50)), flink_event_time_epoch AS"
                + " TO_TIMESTAMP_LTZ(payload.`order`.datetime_updated, 0), WATERMARK FOR"
                + " flink_event_time_epoch AS flink_event_time_epoch - INTERVAL '60' seconds )"
                + " WITH ( 'connector' = 'kafka', 'format' = 'json', 'topic' = '%s',"
                + " 'properties.bootstrap.servers' = '%s', 'properties.group.id' = '%s',"
                + " 'properties.auto.offset.reset' = 'latest',  'scan.startup.mode' ="
                + " 'group-offsets' )";

    public static final String ORDER_FACT_METRIC =
            " INSERT INTO fact_order_details          SELECT o.order_id,"
                + " o.`order`.datetime_created*1000 AS insert_timestamp_epoch,       "
                + " o.`order`.datetime_updated*1000 AS update_timestamp_epoch,"
                + " o.`order`.`customer`.id, o.`order`.current_state_name,       "
                + " o.`order`.total_cost,        o.`order`.delivery_cost,"
                + " o.`order`.`offer_details`.`total_discount` as discount,       "
                + " o.`order`.net_cost, o.`order`.procurement_amount,       "
                + " GetTypeOfOrderFunction(o.`order`.cart.orders, o.order_id) AS TYPE,       "
                + " o.`order`.cart_id,        o.`order`.actual_merchant_id AS merchant_id,       "
                + " o.`order`.wallet_amount, o.`order`.additional_charges_amount,       "
                + " o.`order`.merchant.city_id AS city_id, o.`order`.city AS city_name,       "
                + " o.`order`.checkout_merchant_name AS merchant_name,"
                + " CARDINALITY(o.`order`.items) AS item_count,"
                + " SumOfItemQuantityFunction(o.`order`.items, 'quantity') AS"
                + " total_items_quantity, SumOfItemQuantityFunction(o.`order`.items,"
                + " 'procured_quantity') AS total_procured_items_quantity,"
                + " GetPaasMetrics(o.`order`.items, ARRAY['quantity']) AS paas_metrics, "
                + " o.`order`.slot_properties.slot_charge AS slot_charge,"
                + " o.`order`.slot_properties.checkout_properties.slot_charge AS"
                + " checkout_slot_charge,        o.device_id AS device_id, o.source AS SOURCE,    "
                + "    o.`order`.org_channel_id, o.reason_code,"
                + " GetPickerDetailsFromOrderEventsFunction(o.`order`.order_events,"
                + " 'picker_imei','retail_picker_assignment', false) AS picker_employee_id,"
                + " GetPickerDetailsFromOrderEventsFunction(o.`order`.order_events, 'picker_name',"
                + " 'retail_picker_assignment', false) AS picker_name,"
                + " CAST(GetPickerDetailsFromOrderEventsFunction(o.`order`.order_events,"
                + " 'timestamp', 'picking_started', true) AS BIGINT) AS picking_start_time,"
                + " CAST(GetPickerDetailsFromOrderEventsFunction(o.`order`.order_events,"
                + " 'picking_end_time', 'BILLED', true) AS BIGINT) AS pick_completion_time,"
                + " CAST(GetPickerDetailsFromOrderEventsFunction(o.`order`.order_events,"
                + " 'assignment_queued_ts', 'retail_picklist_ready_to_assign', true) AS BIGINT) AS"
                + " picker_assignment_queued_time,"
                + " o.`order`.slot_properties.serviceability.serviceability_reason AS"
                + " serviceability_reason, o.`order`.slot_properties.serviceability.eta AS"
                + " eta_shown, GetTimeStampFromOrderEventsFunction(o.`order`.order_events,"
                + " 'BILLED') AS order_billed_timestamp,"
                + " GetTimeStampFromOrderEventsFunction(o.`order`.order_events,"
                + " 'delivery_partner_assigned') AS rider_assigned_timestamp,"
                + " GetTimeStampFromOrderEventsFunction(o.`order`.order_events, 'DELIVERED') AS"
                + " delivery_timestamp,"
                + " GetTimeStampFromOrderEventsFunction(o.`order`.order_events,"
                + " 'retail_picker_assignment') AS picker_assigned_timestamp,"
                + " GetTimeStampFromOrderEventsFunction(o.`order`.order_events, 'ENROUTE') AS"
                + " order_enroute_timestamp,"
                + " GetTimeStampFromOrderEventsFunction(o.`order`.order_events, 'CREATED') AS"
                + " checkout_timestamp,"
                + " GetTimeStampFromOrderEventsFunction(o.`order`.order_events, 'APPROVED') AS"
                + " approved_timestamp,"
                + " GetPartnerEmpIdFromOrderEventsFunction(o.`order`.order_events) AS"
                + " delivery_fe_id,"
                + " GetIfBatchedOrderFromOrderEventsFunction(o.`order`.order_events) AS"
                + " is_batched_order, GetFillRateFunction(o.`order`.items, TRUE) AS fill_rate,    "
                + "    GetFillRateFunction(o.`order`.items, FALSE) AS sm_fill_rate,"
                + " GetIfTagExistsOrderFunction(o.`order`.slot_properties, 'paas') AS"
                + " is_paas_order, GetIfEventExistsOrderFunction(o.`order`.order_events,"
                + " 'print_order_created') AS is_paas_order_created,"
                + " GetIfEventExistsOrderFunction(o.`order`.order_events, 'print_order_completed')"
                + " AS is_paas_order_completed,"
                + " GetIfTagExistsOrderFunction(o.`order`.slot_properties, 'defer_picking') AS"
                + " is_defer_picking, GetIfTagExistsOrderFunction(o.`order`.slot_properties,"
                + " 'unicorn') AS is_unicorn_order,"
                + " o.`order`.slot_properties.serviceability.surge_charge_v2.source AS surge_type,"
                + " GetTaskDuration(o.`order`.`slot_properties`.serviceability, ARRAY['picking',"
                + " 'billing', 'picker_eta']) AS instore_eta,"
                + " GetTaskDuration(o.`order`.`slot_properties`.serviceability, ARRAY['travel',"
                + " 'buffer', 'field_executive_eta']) AS outstore_eta,"
                + " GetTimeStampFromOrderEventsFunction(o.`order`.order_events,"
                + " 'reached_doorstep') AS reached_doorstep_timestamp,"
                + " o.`order`.slot_properties.serviceability.surge_charge_v2.surge_amount AS"
                + " surge_amount,        session_id AS session_id,"
                + " ExtractAdditionalChargeAmount(o.additional_charges_data, 3) AS"
                + " handling_charge, ExtractAdditionalChargeAmount(o.additional_charges_data, 4)"
                + " AS convenience_charge,       "
                + " ExtractAdditionalChargeAmount(o.additional_charges_data, 5) AS night_charge,"
                + " ExtractAdditionalChargeAmount(o.additional_charges_data, 7) AS"
                + " small_cart_charge, o.`order`.cart_rank_details.cart_rank_last_6m AS"
                + " cart_rank_last_6m, o.`order`.cart_rank_details.order_count_life_time  AS"
                + " order_count_life_time, o.`order`.payment.`mode` AS payment_mode,"
                + " o.`order`.slot_properties.serviceability.batching.handshake_time AS"
                + " handshake_time, GetIfTagExistsOrderFunction(o.`order`.slot_properties,"
                + " 'LARGE_ORDER_FLEET_ONLY') AS is_large_fleet_order,"
                + " GetTripIdFromOrderEventsFunction(o.`order`.order_events) AS order_trip_id,"
                + " GetComponentDuration(o.`order`.slot_properties.serviceability.components,"
                + " 'picker_eta') AS est_picker_eta,"
                + " GetComponentDuration(o.`order`.slot_properties.serviceability.components,"
                + " 'picking') AS est_picking_eta,"
                + " GetComponentDuration(o.`order`.slot_properties.serviceability.components,"
                + " 'billing') AS est_billing_eta,"
                + " GetComponentDuration(o.`order`.slot_properties.serviceability.components,"
                + " 'buffer') AS est_buffer_eta,"
                + " GetComponentDuration(o.`order`.slot_properties.serviceability.components,"
                + " 'store_handshake') AS est_store_handshake_time,"
                + " GetComponentDuration(o.`order`.slot_properties.serviceability.components,"
                + " 'field_executive_eta') AS est_fe_eta,"
                + " GetComponentDuration(o.`order`.slot_properties.serviceability.components,"
                + " 'travel') AS est_travel_eta,"
                + " GetBatchedOrderIdsFunction(o.`order`.order_events) AS batched_order_ids, CASE"
                + " WHEN (CAST(JSON_VALUE(ClassifyPharmaProductCounts(o.`order`.items),"
                + " '$.rx_count') AS INT) > 0 OR"
                + " CAST(JSON_VALUE(ClassifyPharmaProductCounts(o.`order`.items), '$.otx_count')"
                + " AS INT) > 0) THEN true ELSE false END AS is_pharma_order  FROM"
                + " order_lifecycle_events AS o WHERE"
                + " (GetTypeOfOrderFunction(o.`order`.cart.orders, o.order_id) IS NULL OR"
                + " GetTypeOfOrderFunction(o.`order`.cart.orders, o.order_id) IN"
                + " ('RetailForwardOrder'))   AND o.`order`.city NOT IN ('Not in service area',  "
                + " 'test1207898732')   AND o.`order`.city NOT LIKE '%B2B%' UNION ALL          "
                + " SELECT hp.payload.order_id, hp.payload.`order`.datetime_created*1000 AS"
                + " insert_timestamp_epoch,        hp.payload.`order`.datetime_updated*1000 AS"
                + " update_timestamp_epoch,        hp.payload.`order`.`customer`.id,       "
                + " hp.payload.`order`.current_state_name,        hp.payload.`order`.total_cost,  "
                + "      hp.payload.`order`.delivery_cost,       "
                + " hp.payload.`order`.`offer_details`.`total_discount` as discount,"
                + " hp.payload.`order`.net_cost, hp.payload.`order`.procurement_amount,       "
                + " GetTypeOfOrderFunction(hp.payload.`order`.cart.orders, hp.payload.order_id) AS"
                + " TYPE,        hp.payload.`order`.cart_id,       "
                + " hp.payload.`order`.actual_merchant_id AS merchant_id,       "
                + " hp.payload.`order`.wallet_amount,       "
                + " hp.payload.`order`.additional_charges_amount,       "
                + " hp.payload.`order`.merchant.city_id AS city_id,        hp.payload.`order`.city"
                + " AS city_name,        hp.payload.`order`.checkout_merchant_name AS"
                + " merchant_name,        CARDINALITY(hp.payload.`order`.items) AS item_count,"
                + " SumOfItemQuantityFunction(hp.payload.`order`.items, 'quantity') AS"
                + " total_items_quantity, SumOfItemQuantityFunction(hp.payload.`order`.items,"
                + " 'procured_quantity') AS total_procured_items_quantity,       "
                + " GetPaasMetrics(hp.payload.`order`.items, ARRAY['quantity']) AS paas_metrics, "
                + " hp.payload.`order`.slot_properties.slot_charge AS slot_charge,"
                + " hp.payload.`order`.slot_properties.checkout_properties.slot_charge AS"
                + " checkout_slot_charge,        hp.payload.device_id AS device_id,       "
                + " hp.payload.source AS SOURCE,        hp.payload.`order`.org_channel_id,       "
                + " hp.payload.reason_code,"
                + " GetPickerDetailsFromOrderEventsFunction(hp.payload.`order`.order_events,"
                + " 'picker_imei', 'retail_picker_assignment', false) AS picker_employee_id,"
                + " GetPickerDetailsFromOrderEventsFunction(hp.payload.`order`.order_events,"
                + " 'picker_name', 'retail_picker_assignment', false) AS picker_name,"
                + " CAST(GetPickerDetailsFromOrderEventsFunction(hp.payload.`order`.order_events,"
                + " 'timestamp', 'picking_started', true) AS BIGINT) AS picking_start_time,"
                + " CAST(GetPickerDetailsFromOrderEventsFunction(hp.payload.`order`.order_events,"
                + " 'picking_end_time', 'BILLED', true) AS BIGINT) AS pick_completion_time,"
                + " CAST(GetPickerDetailsFromOrderEventsFunction(hp.payload.`order`.order_events,"
                + " 'assignment_queued_ts', 'retail_picklist_ready_to_assign', true) AS BIGINT) AS"
                + " picker_assignment_queued_time,"
                + " hp.payload.`order`.slot_properties.serviceability.serviceability_reason AS"
                + " serviceability_reason,       "
                + " hp.payload.`order`.slot_properties.serviceability.eta AS eta_shown,"
                + " GetTimeStampFromOrderEventsFunction(hp.payload.`order`.order_events, 'BILLED')"
                + " AS order_billed_timestamp,"
                + " GetTimeStampFromOrderEventsFunction(hp.payload.`order`.order_events,"
                + " 'delivery_partner_assigned') AS rider_assigned_timestamp,"
                + " GetTimeStampFromOrderEventsFunction(hp.payload.`order`.order_events,"
                + " 'DELIVERED') AS delivery_timestamp,"
                + " GetTimeStampFromOrderEventsFunction(hp.payload.`order`.order_events,"
                + " 'retail_picker_assignment') AS picker_assigned_timestamp,"
                + " GetTimeStampFromOrderEventsFunction(hp.payload.`order`.order_events,"
                + " 'ENROUTE') AS order_enroute_timestamp,"
                + " GetTimeStampFromOrderEventsFunction(hp.payload.`order`.order_events,"
                + " 'CREATED') AS checkout_timestamp,"
                + " GetTimeStampFromOrderEventsFunction(hp.payload.`order`.order_events,"
                + " 'APPROVED') AS approved_timestamp,"
                + " GetPartnerEmpIdFromOrderEventsFunction(hp.payload.`order`.order_events) AS"
                + " delivery_fe_id,"
                + " GetIfBatchedOrderFromOrderEventsFunction(hp.payload.`order`.order_events) AS"
                + " is_batched_order,        GetFillRateFunction(hp.payload.`order`.items, TRUE)"
                + " AS fill_rate,        GetFillRateFunction(hp.payload.`order`.items, FALSE) AS"
                + " sm_fill_rate, GetIfTagExistsOrderFunction(hp.payload.`order`.slot_properties,"
                + " 'paas') AS is_paas_order,"
                + " GetIfEventExistsOrderFunction(hp.payload.`order`.order_events,"
                + " 'print_order_created') AS is_paas_order_created,"
                + " GetIfEventExistsOrderFunction(hp.payload.`order`.order_events,"
                + " 'print_order_completed') AS is_paas_order_completed,"
                + " GetIfTagExistsOrderFunction(hp.payload.`order`.slot_properties,"
                + " 'defer_picking') AS is_defer_picking,"
                + " GetIfTagExistsOrderFunction(hp.payload.`order`.slot_properties, 'unicorn') AS"
                + " is_unicorn_order,"
                + " hp.payload.`order`.slot_properties.serviceability.surge_charge_v2.source AS"
                + " surge_type,"
                + " GetTaskDuration(hp.payload.`order`.`slot_properties`.serviceability,"
                + " ARRAY['picking', 'billing', 'picker_eta']) AS instore_eta,"
                + " GetTaskDuration(hp.payload.`order`.`slot_properties`.serviceability,"
                + " ARRAY['travel', 'buffer', 'field_executive_eta']) AS outstore_eta,"
                + " GetTimeStampFromOrderEventsFunction(hp.payload.`order`.order_events,"
                + " 'reached_doorstep') AS reached_doorstep_timestamp,"
                + " hp.payload.`order`.slot_properties.serviceability.surge_charge_v2.surge_amount"
                + " AS surge_amount,        session_id AS session_id,"
                + " ExtractAdditionalChargeAmount(hp.payload.additional_charges_data, 3) AS"
                + " handling_charge,"
                + " ExtractAdditionalChargeAmount(hp.payload.additional_charges_data, 4) AS"
                + " convenience_charge,       "
                + " ExtractAdditionalChargeAmount(hp.payload.additional_charges_data, 5) AS"
                + " night_charge,"
                + " ExtractAdditionalChargeAmount(hp.payload.additional_charges_data, 7) AS"
                + " small_cart_charge, hp.payload.`order`.cart_rank_details.cart_rank_last_6m AS"
                + " cart_rank_last_6m, hp.payload.`order`.cart_rank_details.order_count_life_time "
                + " AS order_count_life_time, hp.payload.`order`.payment.`mode` AS payment_mode,"
                + " hp.payload.`order`.slot_properties.serviceability.batching.handshake_time AS"
                + " handshake_time,"
                + " GetIfTagExistsOrderFunction(hp.payload.`order`.slot_properties,"
                + " 'LARGE_ORDER_FLEET_ONLY') AS is_large_fleet_order,"
                + " GetTripIdFromOrderEventsFunction(hp.payload.`order`.order_events) AS"
                + " order_trip_id,"
                + " GetComponentDuration(hp.payload.`order`.slot_properties.serviceability.components,"
                + " 'picker_eta') AS est_picker_eta,"
                + " GetComponentDuration(hp.payload.`order`.slot_properties.serviceability.components,"
                + " 'picking') AS est_picking_eta,"
                + " GetComponentDuration(hp.payload.`order`.slot_properties.serviceability.components,"
                + " 'billing') AS est_billing_eta,"
                + " GetComponentDuration(hp.payload.`order`.slot_properties.serviceability.components,"
                + " 'buffer') AS est_buffer_eta,"
                + " GetComponentDuration(hp.payload.`order`.slot_properties.serviceability.components,"
                + " 'store_handshake') AS est_store_handshake_time,"
                + " GetComponentDuration(hp.payload.`order`.slot_properties.serviceability.components,"
                + " 'field_executive_eta') AS est_fe_eta,"
                + " GetComponentDuration(hp.payload.`order`.slot_properties.serviceability.components,"
                + " 'travel') AS est_travel_eta,"
                + " GetBatchedOrderIdsFunction(hp.payload.`order`.order_events) AS"
                + " batched_order_ids, CASE WHEN"
                + " (CAST(JSON_VALUE(ClassifyPharmaProductCounts(hp.payload.`order`.items),"
                + " '$.rx_count') AS INT) > 0 OR"
                + " CAST(JSON_VALUE(ClassifyPharmaProductCounts(hp.payload.`order`.items),"
                + " '$.otx_count') AS INT) > 0) THEN true ELSE false END AS is_pharma_order  FROM"
                + " hyperpure_order_lifecycle_events AS hp ";

    public static final String ORDER_FACT_CREATE_TABLE =
            "CREATE TABLE IF NOT EXISTS fact_order_details (     id INT,     insert_timestamp"
                + " BIGINT,     update_timestamp BIGINT,     customer_id INT,     current_status"
                + " VARCHAR(50),     total_cost DOUBLE,     delivery_cost DOUBLE,     discount"
                + " DOUBLE,     net_cost DOUBLE,     procurement_amount DOUBLE,     type"
                + " VARCHAR(63),     cart_id BIGINT,     merchant_id BIGINT,     wallet_amount"
                + " DOUBLE,     additional_charges_amount DOUBLE,     city_id INT,     city_name"
                + " STRING,     merchant_name STRING,     item_count BIGINT,    "
                + " total_items_quantity BIGINT,     total_procured_items_quantity BIGINT,     "
                + " paas_metrics STRING,   slot_charge DOUBLE,     checkout_slot_charge DOUBLE,   "
                + " device_id STRING, source STRING,     org_channel_id VARCHAR(10),    "
                + " reason_code VARCHAR(50), picker_employee_id STRING, picker_name STRING,"
                + " picking_start_time BIGINT, pick_completion_time BIGINT,"
                + " picker_assignment_queued_time BIGINT, serviceability_reason STRING,    "
                + " eta_shown BIGINT,     order_billed_timestamp BIGINT,    "
                + " rider_assigned_timestamp BIGINT,     delivery_timestamp BIGINT,    "
                + " picker_assigned_timestamp BIGINT,     order_enroute_timestamp BIGINT,    "
                + " checkout_timestamp BIGINT,     approved_timestamp BIGINT,      delivery_fe_id"
                + " STRING, is_batched_order BOOLEAN, fill_rate DOUBLE, sm_fill_rate DOUBLE,"
                + " is_paas_order BOOLEAN, is_paas_order_created BOOLEAN, is_paas_order_completed"
                + " BOOLEAN, is_defer_picking BOOLEAN, is_unicorn_order BOOLEAN, surge_type"
                + " STRING, instore_eta DOUBLE, outstore_eta DOUBLE, reached_doorstep_timestamp"
                + " BIGINT, surge_amount DOUBLE, session_id STRING, handling_charge DOUBLE,"
                + " convenience_charge DOUBLE, night_charge DOUBLE, small_cart_charge DOUBLE,"
                + " cart_rank_last_6m INT, order_count_life_time INT, payment_mode STRING,"
                + " handshake_time DOUBLE, is_large_fleet_order BOOLEAN, order_trip_id STRING,"
                + " est_picker_eta DOUBLE, est_picking_eta DOUBLE, est_billing_eta DOUBLE,"
                + " est_buffer_eta DOUBLE, est_store_handshake_time DOUBLE, est_fe_eta DOUBLE,"
                + " est_travel_eta DOUBLE, batched_order_ids ARRAY<STRING>, is_pharma_order"
                + " BOOLEAN) WITH (    'connector' = 'kafka',    'topic' = '%s',   "
                + " 'properties.bootstrap.servers' = '%s',    'key.format' = 'json',   "
                + " 'value.format' = 'json', 'key.fields' = 'cart_id', 'sink.partitioner' ="
                + " 'default' )";

    public static final String PHARMA_FACT_CREATE_TABLE =
            "CREATE TABLE IF NOT EXISTS fact_pharma_order_details ( id INT, insert_timestamp"
                + " BIGINT, update_timestamp BIGINT, customer_id INT, current_status VARCHAR(50),"
                + " cart_id BIGINT, merchant_id BIGINT, city_id INT, city_name STRING,"
                + " merchant_name STRING, device_id STRING, source STRING, order_billed_timestamp"
                + " BIGINT, rider_assigned_timestamp BIGINT, delivery_timestamp BIGINT,"
                + " picker_assigned_timestamp BIGINT, order_enroute_timestamp BIGINT,"
                + " checkout_timestamp BIGINT, approved_timestamp BIGINT,"
                + " pharmacy_order_prescription_uploaded BOOLEAN,"
                + " pharmacy_order_pharmacist_assigned_at BIGINT,"
                + " pharmacy_order_pharmacist_approved_at BIGINT,"
                + " pharmacy_order_pharmacist_rejected_at BIGINT,"
                + " pharmacy_order_pharmacist_acceptance_time BIGINT,"
                + " pharmacy_order_pharmacist_resolution_time BIGINT,"
                + " pharmacy_order_pharmacist_rejection_reason STRING,"
                + " pharmacy_order_has_doctor_approval_leg BOOLEAN,"
                + " pharmacy_order_doctor_assigned_at BIGINT, pharmacy_order_doctor_approved_at"
                + " BIGINT, pharmacy_order_doctor_rejected_at BIGINT,"
                + " pharmacy_order_doctor_acceptance_time BIGINT,"
                + " pharmacy_order_doctor_resolution_time_from_assignment BIGINT,"
                + " pharmacy_order_doctor_resolution_time_from_rejection BIGINT,"
                + " pharmacy_order_doctor_rejection_reason STRING,"
                + " pharmacy_order_doctor_consultation_on_hold BOOLEAN,"
                + " pharmacy_order_doctor_assignment_failed BOOLEAN, rx_item_count INT,"
                + " otx_item_count INT) WITH ( 'connector' = 'kafka', 'topic' = '%s',"
                + " 'properties.bootstrap.servers' = '%s', 'key.format' = 'json', 'value.format' ="
                + " 'json', 'key.fields' = 'cart_id', 'sink.partitioner' = 'default' )";

    public static final String PHARMA_FACT_METRIC =
            " INSERT INTO fact_pharma_order_details SELECT base.order_id, base.order_placed_at AS"
                + " insert_timestamp_epoch, base.update_timestamp_epoch, base.customer_id,"
                + " base.current_status, base.cart_id, base.merchant_id, base.city_id,"
                + " base.city_name, base.merchant_name, base.device_id, base.source,"
                + " base.order_billed_timestamp, base.rider_assigned_timestamp,"
                + " base.delivery_timestamp, base.picker_assigned_timestamp,"
                + " base.order_enroute_timestamp, base.checkout_timestamp,"
                + " base.approved_timestamp, base.prescription_uploaded AS"
                + " pharmacy_order_prescription_uploaded, base.pharmacist_assigned_at AS"
                + " pharmacy_order_pharmacist_assigned_at, base.pharmacist_approved_at AS"
                + " pharmacy_order_pharmacist_approved_at, base.pharmacist_rejected_at AS"
                + " pharmacy_order_pharmacist_rejected_at, (base.pharmacist_assigned_at -"
                + " base.order_placed_at) AS pharmacy_order_pharmacist_acceptance_time, CASE WHEN"
                + " base.pharmacist_approved_at IS NOT NULL THEN (base.pharmacist_approved_at -"
                + " base.pharmacist_assigned_at) ELSE (base.pharmacist_rejected_at -"
                + " base.pharmacist_assigned_at) END AS pharmacy_order_pharmacist_resolution_time,"
                + " base.pharmacist_rejection_reason AS"
                + " pharmacy_order_pharmacist_rejection_reason,"
                + " CheckDoctorApprovalLeg(base.prescription_uploaded,"
                + " base.pharmacist_rejected_at) AS pharmacy_order_has_doctor_approval_leg,"
                + " base.doctor_assigned_at AS pharmacy_order_doctor_assigned_at,"
                + " base.doctor_approved_at AS pharmacy_order_doctor_approved_at,"
                + " base.doctor_rejected_at AS pharmacy_order_doctor_rejected_at, CASE WHEN"
                + " base.prescription_uploaded = true THEN (base.doctor_assigned_at -"
                + " base.pharmacist_rejected_at) ELSE (base.doctor_assigned_at -"
                + " base.order_placed_at) END AS pharmacy_order_doctor_acceptance_time, CASE WHEN"
                + " base.doctor_approved_at IS NOT NULL THEN (base.doctor_approved_at -"
                + " base.doctor_assigned_at) ELSE (base.doctor_rejected_at -"
                + " base.doctor_assigned_at) END AS"
                + " pharmacy_order_doctor_resolution_time_from_assignment, CASE WHEN"
                + " base.prescription_uploaded = true AND base.pharmacist_rejected_at IS NOT NULL"
                + " THEN CASE WHEN base.doctor_approved_at IS NOT NULL THEN"
                + " (base.doctor_approved_at - base.pharmacist_rejected_at) ELSE"
                + " (base.doctor_rejected_at - base.pharmacist_rejected_at) END ELSE CASE WHEN"
                + " base.doctor_approved_at IS NOT NULL THEN (base.doctor_approved_at -"
                + " base.order_placed_at) ELSE (base.doctor_rejected_at - base.order_placed_at)"
                + " END END AS pharmacy_order_doctor_resolution_time_from_rejection,"
                + " base.doctor_rejection_reason AS pharmacy_order_doctor_rejection_reason,"
                + " base.doctor_consultation_on_hold AS"
                + " pharmacy_order_doctor_consultation_on_hold, base.doctor_assignment_failed AS"
                + " pharmacy_order_doctor_assignment_failed,"
                + " CAST(JSON_VALUE(base.product_classification, '$.rx_count') AS INT) AS"
                + " rx_item_count, CAST(JSON_VALUE(base.product_classification, '$.otx_count') AS"
                + " INT) AS otx_item_count FROM ( SELECT o.order_id,"
                + " o.`order`.datetime_created*1000 AS order_placed_at,"
                + " o.`order`.datetime_updated*1000 AS update_timestamp_epoch,"
                + " o.`order`.customer.id AS customer_id, o.`order`.current_state_name AS"
                + " current_status, o.`order`.cart_id, o.`order`.actual_merchant_id AS"
                + " merchant_id, o.`order`.merchant.city_id AS city_id, o.`order`.city AS"
                + " city_name, o.`order`.checkout_merchant_name AS merchant_name,"
                + " o.`order`.device_id, o.`order`.source,"
                + " GetTimeStampFromOrderEventsFunction(o.`order`.order_events, 'BILLED') AS"
                + " order_billed_timestamp,"
                + " GetTimeStampFromOrderEventsFunction(o.`order`.order_events,"
                + " 'delivery_partner_assigned') AS rider_assigned_timestamp,"
                + " GetTimeStampFromOrderEventsFunction(o.`order`.order_events, 'DELIVERED') AS"
                + " delivery_timestamp,"
                + " GetTimeStampFromOrderEventsFunction(o.`order`.order_events,"
                + " 'retail_picker_assignment') AS picker_assigned_timestamp,"
                + " GetTimeStampFromOrderEventsFunction(o.`order`.order_events, 'ENROUTE') AS"
                + " order_enroute_timestamp,"
                + " GetTimeStampFromOrderEventsFunction(o.`order`.order_events, 'CREATED') AS"
                + " checkout_timestamp,"
                + " GetTimeStampFromOrderEventsFunction(o.`order`.order_events, 'APPROVED') AS"
                + " approved_timestamp, CheckPrescriptionUploaded(o.`order`) AS"
                + " prescription_uploaded, GetPharmaEventTimestamp(o.`order`.order_events,"
                + " 'pharmacist_assigned') AS pharmacist_assigned_at,"
                + " GetPharmaEventTimestamp(o.`order`.order_events, 'pharmacist_order_reviewed',"
                + " 'approved_by', 'PHARMACIST') AS pharmacist_approved_at,"
                + " GetPharmaEventTimestamp(o.`order`.order_events,"
                + " 'pharmacist_verification_failed') AS pharmacist_rejected_at,"
                + " GetPharmaEventTimestamp(o.`order`.order_events, 'doctor_assigned') AS"
                + " doctor_assigned_at, GetPharmaEventTimestamp(o.`order`.order_events,"
                + " 'pharmacy_order_reviewed', 'approved_by', 'DOCTOR') AS doctor_approved_at,"
                + " GetDoctorRejectionTimestamp(o.`order`.order_events) AS doctor_rejected_at,"
                + " GetPharmaRejectionReason(o.`order`.order_events,"
                + " 'pharmacist_verification_failed') AS pharmacist_rejection_reason,"
                + " GetPharmaRejectionReason(o.`order`.order_events, 'order_cancellation') AS"
                + " doctor_rejection_reason, GetIfEventExistsOrderFunction(o.`order`.order_events,"
                + " 'doctor_consultation_on_hold') AS doctor_consultation_on_hold,"
                + " CheckDoctorAssignmentFailed(o.`order`.order_events) AS"
                + " doctor_assignment_failed, ClassifyPharmaProductCounts(o.`order`.items) AS"
                + " product_classification FROM order_lifecycle_events AS o WHERE"
                + " (GetTypeOfOrderFunction(o.`order`.cart.orders, o.order_id) IS NULL OR"
                + " GetTypeOfOrderFunction(o.`order`.cart.orders, o.order_id) IN"
                + " ('RetailForwardOrder')) AND o.`order`.city NOT IN ('Not in service area',"
                + " 'test1207898732') AND o.`order`.city NOT LIKE '%B2B%' AND"
                + " (CAST(JSON_VALUE(ClassifyPharmaProductCounts(o.`order`.items), '$.rx_count')"
                + " AS INT) > 0 OR CAST(JSON_VALUE(ClassifyPharmaProductCounts(o.`order`.items),"
                + " '$.otx_count') AS INT) > 0) ) AS base WHERE"
                + " CheckDoctorApprovalLeg(base.prescription_uploaded,"
                + " base.pharmacist_rejected_at) = true";
}
