ORDER_LIFECYCLE_EVENTS_SOURCE_BROKERS = ****************************************.groupblinkit-agent.kafka.discoveryv2.prod-y.ap-southeast-1.warpstream.com:9092
ORDER_LIFECYCLE_EVENTS_SOURCE_CONSUMER_GROUP_ID = blinkit.data.flink-fact-order-details-v3
ORDER_LIFECYCLE_EVENTS_SOURCE_TOPIC = blinkit.order.lifecycle-events
HYPERPURE_ORDER_LIFECYCLE_EVENTS_SOURCE_TOPIC = hyperpure.order.lifecycle_events
ORDER_FACT_SINK_BROKERS = b-1.prod-data-corestr.uynfnv.c4.kafka.ap-southeast-1.amazonaws.com:9092,b-2.prod-data-corestr.uynfnv.c4.kafka.ap-southeast-1.amazonaws.com:9092,b-3.prod-data-corestr.uynfnv.c4.kafka.ap-southeast-1.amazonaws.com:9092
ORDER_FACT_SINK_TOPIC = observability.metrics.fact-order-details-v4
PHARMA_FACT_SINK_BROKERS = b-1.prod-data-corestr.uynfnv.c4.kafka.ap-southeast-1.amazonaws.com:9092,b-2.prod-data-corestr.uynfnv.c4.kafka.ap-southeast-1.amazonaws.com:9092,b-3.prod-data-corestr.uynfnv.c4.kafka.ap-southeast-1.amazonaws.com:9092
PHARMA_FACT_SINK_TOPIC = observability.metrics.fact-pharma-order-details
STATE_BACKEND_LOCATION = s3a://prod-data-flink-states/flink-streams/observability/fact-order-details-v2/checkpoints/
