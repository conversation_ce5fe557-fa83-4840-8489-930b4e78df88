name: Fact Order Details
image: 442534439095.dkr.ecr.ap-southeast-1.amazonaws.com/data/flink-streams/observability/fact-order-details-v2
flink_config:
  taskmanager.memory.process.size: 10240m
  jobmanager.memory.process.size: 2048m
  taskmanager.memory.managed.size: 0
  taskmanager.numberOfTaskSlots: 3
  parallelism.default: 5
  process.working-dir: fact-order-details-v2/process
  state.backend.local-recovery: true
  taskmanager.resource-id: TaskManager_FactOrderDetailsV2
deployment_config:
  kubernetes.rest-service.exposed.type: NodePort
  metrics.reporters: prom
  metrics.reporter.prom.factory.class: org.apache.flink.metrics.prometheus.PrometheusReporterFactory
allow_non_restored_state: False # Set to True when changes are made to Operator
codeartifact_access: True
