name: Inventory Sold Metrics
image: 442534439095.dkr.ecr.ap-southeast-1.amazonaws.com/data/flink-streams/observability/inventory-sold-metric
flink_config:
  taskmanager.memory.process.size: 8192m
  jobmanager.memory.process.size: 2048m
  taskmanager.numberOfTaskSlots: 2
  parallelism.default: 2
deployment_config:
  kubernetes.rest-service.exposed.type: NodePort
  metrics.reporters: prom
  metrics.reporter.prom.class: org.apache.flink.metrics.prometheus.PrometheusReporter
allow_non_restored_state: False # Set to True when changes are made to Operator
codeartifact_access: True
