ORDER_LIFECYCLE_EVENTS_SOURCE_BROKERS = ****************************************.groupblinkit-agent.kafka.discoveryv2.prod-y.ap-southeast-1.warpstream.com:9092
ORDER_LIFECYCLE_APPROVED_EVENTS_SOURCE_CONSUMER_GROUP_ID = blinkit.data.test.flink-inventory-sold-metrics-approved-warpstream-test
ORDER_LIFECYCLE_CANCELLED_EVENTS_SOURCE_CONSUMER_GROUP_ID = blinkit.data.test.flink-inventory-sold-metrics-cancelled-warpstream-test
ORDER_LIFECYCLE_EVENTS_SOURCE_TOPIC = blinkit.order.lifecycle-events
INVENTORY_SOLD_SINK_BROKERS = b-2.testdatacommon.vkkz3t.c5.kafka.ap-southeast-1.amazonaws.com:9092,b-1.testdatacommon.vkkz3t.c5.kafka.ap-southeast-1.amazonaws.com:9092
INVENTORY_SOLD_SINK_TOPIC = testing.v1.observability.metrics.inventory-sold-metrics
STATE_BACKEND_LOCATION = s3a://grofers-test-dse-singapore/flink-streams/observability/inventory-sold-metrics/checkpoints/
