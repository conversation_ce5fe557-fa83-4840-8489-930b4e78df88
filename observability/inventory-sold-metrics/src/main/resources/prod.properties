ORDER_LIFECYCLE_EVENTS_SOURCE_BROKERS = ****************************************.groupblinkit-agent.kafka.discoveryv2.prod-y.ap-southeast-1.warpstream.com:9092
ORDER_LIFECYCLE_APPROVED_EVENTS_SOURCE_CONSUMER_GROUP_ID = blinkit.data.flink-inventory-sold-metrics-approved-consumer_group-v1
ORDER_LIFECYCLE_CANCELLED_EVENTS_SOURCE_CONSUMER_GROUP_ID = blinkit.data.flink-inventory-sold-metrics-cancelled-consumer_group-v1
ORDER_LIFECYCLE_EVENTS_SOURCE_TOPIC = blinkit.order.lifecycle-events
INVENTORY_SOLD_SINK_BROKERS = b-1.prod-data-corestr.uynfnv.c4.kafka.ap-southeast-1.amazonaws.com:9092,b-2.prod-data-corestr.uynfnv.c4.kafka.ap-southeast-1.amazonaws.com:9092,b-3.prod-data-corestr.uynfnv.c4.kafka.ap-southeast-1.amazonaws.com:9092
INVENTORY_SOLD_SINK_TOPIC = observability.metrics.inventory-sold-metrics-v1
STATE_BACKEND_LOCATION = s3a://prod-data-flink-states/flink-streams/observability/inventory-sold-metrics/checkpoints/
