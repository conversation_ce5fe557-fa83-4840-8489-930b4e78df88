package com.grofers.observability.inventorysoldmetrics;

public class Queries {
    public static final String ORDER_LIFECYCLE_EVENTS_APPROVED =
            "CREATE TABLE IF NOT EXISTS order_lifecycle_approved_events (order_id INT, `order`"
                + " ROW(datetime_created BIGINT, datetime_updated BIGINT, current_state_name"
                + " VARCHAR(50), cart_id BIGINT, items ARRAY<ROW (`product_id` INT, `quantity`"
                + " INT, `cancelled_quantity` INT, price FLOAT, product_meta ROW( type ROW(`name`"
                + " STRING)) ) >, `city` STRING, merchant ROW(`id` INT, `city_id` INT), cart"
                + " ROW(`orders` ARRAY< ROW (`id` INT, `type` VARCHAR(63) )>)),"
                + " flink_event_time_epoch AS TO_TIMESTAMP_LTZ(`order`.datetime_created,"
                + " 0),WATERMARK FOR flink_event_time_epoch AS flink_event_time_epoch - INTERVAL"
                + " '60' seconds) WITH (  'connector' = 'kafka', 'format' = 'json', 'topic' ="
                + " '%s', 'properties.bootstrap.servers' = '%s', 'properties.group.id' = '%s',"
                + " 'scan.startup.mode' = 'earliest-offset'   );";

    public static final String ORDER_LIFECYCLE_EVENTS_CANCELLED =
            "CREATE TABLE IF NOT EXISTS order_lifecycle_cancelled_events (order_id INT, `order`"
                + " ROW(datetime_created BIGINT, datetime_updated BIGINT, current_state_name"
                + " VARCHAR(50), cart_id BIGINT  ,items ARRAY<ROW (`product_id` INT, `quantity`"
                + " INT, `cancelled_quantity` INT, price FLOAT, product_meta ROW( type ROW(`name`"
                + " STRING)) ) >, `city` STRING, merchant ROW(`id` INT, `city_id` INT), cart"
                + " ROW(`orders` ARRAY< ROW (`id` INT, `type` VARCHAR(63) )>)),"
                + " flink_event_time_epoch AS TO_TIMESTAMP_LTZ(`order`.datetime_updated,"
                + " 0),WATERMARK FOR flink_event_time_epoch AS flink_event_time_epoch - INTERVAL"
                + " '5' seconds) WITH (  'connector' = 'kafka', 'format' = 'json', 'topic' = '%s',"
                + " 'properties.bootstrap.servers' = '%s', 'properties.group.id' = '%s',"
                + " 'scan.startup.mode' = 'earliest-offset'   );";

    public static final String INVENTORY_SOLD_CREATE_TABLE =
            "CREATE TABLE IF NOT EXISTS inventory_sold_sink ( `merchant_id` INT, `city` STRING,"
                + " `product_id` INT,  `product_type` STRING, `gmv` DOUBLE, `current_state_name`"
                + " VARCHAR(50), `window_start` BIGINT, `window_end` BIGINT, `placed_quantity`"
                + " INT, num_carts BIGINT, `cancelled_quantity` INT,  PRIMARY"
                + " KEY(merchant_id,product_id,current_state_name,window_start) NOT ENFORCED)  "
                + " WITH ( 'connector' = 'upsert-kafka', 'key.format' = 'json',  'value.format' ="
                + " 'json',  'topic' = '%s', 'properties.bootstrap.servers' = '%s');";
}
