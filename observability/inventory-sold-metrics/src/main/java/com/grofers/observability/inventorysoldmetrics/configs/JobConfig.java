package com.grofers.observability.inventorysoldmetrics.configs;

public class JobConfig {
    public static String ORDER_LIFECYCLE_EVENTS_SOURCE_BROKERS;
    public static String ORDER_LIFECYCLE_APPROVED_EVENTS_SOURCE_CONSUMER_GROUP_ID;
    public static String ORDER_LIFECYCLE_CANCELLED_EVENTS_SOURCE_CONSUMER_GROUP_ID;
    public static String ORDER_LIFECYCLE_EVENTS_SOURCE_TOPIC;
    public static String INVENTORY_SOLD_SINK_BROKERS;
    public static String INVENTORY_SOLD_SINK_TOPIC;
    public static String STATE_BACKEND_LOCATION;

    public JobConfig(
            String ORDER_LIFECYCLE_EVENTS_SOURCE_BROKERS,
            String ORDER_LIFECYCLE_APPROVED_EVENTS_SOURCE_CONSUMER_GROUP_ID,
            String ORDER_LIFECYCLE_CANCELLED_EVENTS_SOURCE_CONSUMER_GROUP_ID,
            String ORDER_LIFECYCLE_EVENTS_SOURCE_TOPIC,
            String INVENTORY_SOLD_SINK_BROKERS,
            String INVENTORY_SOLD_SINK_TOPIC,
            String STATE_BACKEND_LOCATION) {
        JobConfig.ORDER_LIFECYCLE_EVENTS_SOURCE_BROKERS = ORDER_LIFECYCLE_EVENTS_SOURCE_BROKERS;
        JobConfig.ORDER_LIFECYCLE_APPROVED_EVENTS_SOURCE_CONSUMER_GROUP_ID =
                ORDER_LIFECYCLE_APPROVED_EVENTS_SOURCE_CONSUMER_GROUP_ID;
        JobConfig.ORDER_LIFECYCLE_CANCELLED_EVENTS_SOURCE_CONSUMER_GROUP_ID =
                ORDER_LIFECYCLE_CANCELLED_EVENTS_SOURCE_CONSUMER_GROUP_ID;
        JobConfig.ORDER_LIFECYCLE_EVENTS_SOURCE_TOPIC = ORDER_LIFECYCLE_EVENTS_SOURCE_TOPIC;
        JobConfig.INVENTORY_SOLD_SINK_BROKERS = INVENTORY_SOLD_SINK_BROKERS;
        JobConfig.INVENTORY_SOLD_SINK_TOPIC = INVENTORY_SOLD_SINK_TOPIC;
        JobConfig.STATE_BACKEND_LOCATION = STATE_BACKEND_LOCATION;
    }
}
