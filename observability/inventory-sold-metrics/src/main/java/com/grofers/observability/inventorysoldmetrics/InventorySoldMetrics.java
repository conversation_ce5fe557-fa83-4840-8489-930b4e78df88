/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.grofers.observability.inventorysoldmetrics;

import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.contrib.streaming.state.EmbeddedRocksDBStateBackend;
import org.apache.flink.streaming.api.CheckpointingMode;
import org.apache.flink.streaming.api.environment.CheckpointConfig;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.table.annotation.DataTypeHint;
import org.apache.flink.table.api.StatementSet;
import org.apache.flink.table.api.Table;
import org.apache.flink.table.api.bridge.java.StreamTableEnvironment;
import org.apache.flink.table.functions.ScalarFunction;
import org.apache.flink.types.Row;

import com.grofers.observability.inventorysoldmetrics.configs.JobConfigManager;

import static com.grofers.observability.inventorysoldmetrics.Queries.*;
import static com.grofers.observability.inventorysoldmetrics.configs.JobConfig.*;

/**
 * Order Metrics Job
 *
 * <p>For a tutorial how to write a Flink streaming application, check the tutorials and examples on
 * the <a href="https://flink.apache.org/docs/stable/">Flink Website</a>.
 *
 * <p>To package your application into a JAR file for execution, run 'mvn clean package' on the
 * command line.
 *
 * <p>If you change the name of the main class (with the public static void main(String[] args))
 * method, change the respective entry in the POM.xml file (simply search for 'mainClass').
 */
public class InventorySoldMetrics {
    public static class GetTypeOfOrderFunction extends ScalarFunction {
        public String eval(
                @DataTypeHint("ARRAY< ROW <`id` INT, `type` VARCHAR(63) >>") Row[] arr,
                Integer key) {
            if (arr != null) {
                for (Row i : arr) {
                    if ((int) i.getFieldAs("id") == key) {
                        return i.getFieldAs("type").toString();
                    }
                }
            }
            return null;
        }
    }

    public static void main(String[] args) throws Exception {
        final StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment();
        final StreamTableEnvironment tEnv = StreamTableEnvironment.create(env);
        tEnv.createTemporarySystemFunction("GetTypeOfOrderFunction", GetTypeOfOrderFunction.class);
        Configuration configuration = tEnv.getConfig().getConfiguration();
        configuration.setString("table.exec.source.idle-timeout", "1200 s");
        configuration.setString("table.exec.state.ttl", "10800 s");

        // local-global aggregation depends on mini-batch is enabled
        configuration.setString("table.exec.mini-batch.enabled", "true");
        configuration.setString("table.exec.mini-batch.allow-latency", "10 s");
        configuration.setString("table.exec.mini-batch.size", "100");

        configuration.setString("table.optimizer.agg-phase-strategy", "TWO_PHASE");
        configuration.setString("table.optimizer.distinct-agg.split.enabled", "true");

        ParameterTool params = ParameterTool.fromArgs(args);
        String userEnv = params.getRequired("environment");

        // Get job configs
        JobConfigManager.setJobConfigsPath(userEnv);
        String jobPropertiesPath = JobConfigManager.getJobConfigsPath(userEnv);
        JobConfigManager.setJobConfigs(tEnv, jobPropertiesPath);
        JobConfigManager.getJobConfigs(tEnv);

        // Checkpoint Configs
        env.enableCheckpointing(900000);
        // set mode to exactly-once (default)
        env.getCheckpointConfig().setCheckpointingMode(CheckpointingMode.EXACTLY_ONCE);
        // make sure 900000 ms of progress happen between checkpoints
        env.getCheckpointConfig().setMinPauseBetweenCheckpoints(900000);
        // checkpoints have to complete within this minute, or are discarded
        env.getCheckpointConfig().setCheckpointTimeout(840000);
        // only two consecutive checkpoint failures are tolerated
        env.getCheckpointConfig().setTolerableCheckpointFailureNumber(3);
        // allow only one checkpoint to be in progress at the same time
        env.getCheckpointConfig().setMaxConcurrentCheckpoints(1);
        // enable externalized checkpoints which are retained after job cancellation
        env.getCheckpointConfig()
                .setExternalizedCheckpointCleanup(
                        CheckpointConfig.ExternalizedCheckpointCleanup.RETAIN_ON_CANCELLATION);
        // enable unaligned checkpoints
        env.getCheckpointConfig().enableUnalignedCheckpoints();
        // sets a RocksDB checkpoint storage where checkpoint snapshots will be written
        env.setStateBackend(new EmbeddedRocksDBStateBackend(true));
        env.getCheckpointConfig().setCheckpointStorage(STATE_BACKEND_LOCATION);

        String omsOrderApproveCreateTable =
                String.format(
                        ORDER_LIFECYCLE_EVENTS_APPROVED,
                        ORDER_LIFECYCLE_EVENTS_SOURCE_TOPIC,
                        ORDER_LIFECYCLE_EVENTS_SOURCE_BROKERS,
                        ORDER_LIFECYCLE_APPROVED_EVENTS_SOURCE_CONSUMER_GROUP_ID);
        tEnv.executeSql(omsOrderApproveCreateTable);

        String omsOrderCancelCreateTable =
                String.format(
                        ORDER_LIFECYCLE_EVENTS_CANCELLED,
                        ORDER_LIFECYCLE_EVENTS_SOURCE_TOPIC,
                        ORDER_LIFECYCLE_EVENTS_SOURCE_BROKERS,
                        ORDER_LIFECYCLE_CANCELLED_EVENTS_SOURCE_CONSUMER_GROUP_ID);
        tEnv.executeSql(omsOrderCancelCreateTable);

        String inventorySoldCreateTable =
                String.format(
                        INVENTORY_SOLD_CREATE_TABLE,
                        INVENTORY_SOLD_SINK_TOPIC,
                        INVENTORY_SOLD_SINK_BROKERS);
        tEnv.executeSql(inventorySoldCreateTable);

        // Aggregated Approved Order items without Duplicates
        Table approvedOrderItems =
                tEnv.sqlQuery(
                        "WITH `item_level_order_events` AS (select `order_id`,"
                            + " o.`order`.merchant.id as merchant_id, o.`order`.city as city,"
                            + " flink_event_time_epoch, o.`order`.cart_id AS cart_id,"
                            + " o.`order`.current_state_name as current_state_name,  ROW_NUMBER()"
                            + " OVER (PARTITION BY order_id, unnested_items.product_id,"
                            + " o.`order`.current_state_name, flink_event_time_epoch ORDER BY"
                            + " flink_event_time_epoch) as `row_num`, unnested_items.product_id,"
                            + " (case when o.`order`.current_state_name = 'APPROVED' then"
                            + " unnested_items.quantity else 0 end) as quantity,"
                            + " unnested_items.cancelled_quantity as cancelled_quantity,"
                            + " unnested_items.price, unnested_items.product_meta.type.name as"
                            + " product_type FROM order_lifecycle_approved_events o CROSS JOIN"
                            + " UNNEST(o.items) AS unnested_items (product_id, quantity,"
                            + " cancelled_quantity, price, product_meta) WHERE"
                            + " (GetTypeOfOrderFunction(o.`order`.cart.orders, o.order_id) is null"
                            + "  or GetTypeOfOrderFunction(o.`order`.cart.orders, o.order_id) in"
                            + " ('RetailForwardOrder')) and o.`order`.current_state_name in"
                            + " ('APPROVED') and o.`order`.city not in ('Not in service"
                            + " area','Hapur','test1207898732') and o.`order`.city not like"
                            + " '%B2B%') SELECT merchant_id, city, product_id, product_type,"
                            + " sum(quantity) * price as gmv, current_state_name,"
                            + " UNIX_TIMESTAMP(CAST(window_start AS STRING)) * 1000 as"
                            + " window_start, UNIX_TIMESTAMP(CAST(window_end AS STRING)) * 1000 as"
                            + " window_end, sum(quantity) as placed_quantity, count(distinct"
                            + " cart_id) as num_carts, 0 as cancelled_quantity FROM"
                            + " TABLE(CUMULATE(TABLE item_level_order_events,"
                            + " DESCRIPTOR(flink_event_time_epoch), INTERVAL '2' MINUTES, INTERVAL"
                            + " '60' MINUTES)) WHERE row_num = 1 GROUP BY merchant_id, city,"
                            + " product_id, product_type, price, window_start, window_end,"
                            + " current_state_name");

        // Aggregated Cancelled Order items without Duplicates
        Table cancelledOrderItems =
                tEnv.sqlQuery(
                        "WITH `item_level_order_events` AS (select `order_id`,"
                            + " o.`order`.merchant.id as merchant_id, o.`order`.city as city,"
                            + " flink_event_time_epoch, o.`order`.cart_id AS cart_id,"
                            + " o.`order`.current_state_name as current_state_name,  ROW_NUMBER()"
                            + " OVER (PARTITION BY order_id, unnested_items.product_id,"
                            + " o.`order`.current_state_name, flink_event_time_epoch ORDER BY"
                            + " flink_event_time_epoch) as `row_num`, unnested_items.product_id,"
                            + " unnested_items.quantity as quantity,"
                            + " unnested_items.cancelled_quantity as cancelled_quantity,"
                            + " unnested_items.price, unnested_items.product_meta.type.name as"
                            + " product_type FROM order_lifecycle_cancelled_events o CROSS JOIN"
                            + " UNNEST(o.items) AS unnested_items (product_id, quantity,"
                            + " cancelled_quantity, price, product_meta) WHERE"
                            + " (GetTypeOfOrderFunction(o.`order`.cart.orders, o.order_id) is null"
                            + "  or GetTypeOfOrderFunction(o.`order`.cart.orders, o.order_id) in"
                            + " ('RetailForwardOrder')) and o.`order`.current_state_name in"
                            + " ('CANCELLED') and o.`order`.city not in ('Not in service"
                            + " area','Hapur','test1207898732') and o.`order`.city not like"
                            + " '%B2B%') SELECT merchant_id, city, product_id, product_type,"
                            + " sum(cancelled_quantity) * price as gmv, current_state_name,"
                            + " UNIX_TIMESTAMP(CAST(window_start AS STRING)) * 1000 as"
                            + " window_start, UNIX_TIMESTAMP(CAST(window_end AS STRING)) * 1000 as"
                            + " window_end, 0 as placed_quantity, count(distinct cart_id) as"
                            + " num_carts, sum(cancelled_quantity) as cancelled_quantity  FROM"
                            + " TABLE(CUMULATE(TABLE item_level_order_events,"
                            + " DESCRIPTOR(flink_event_time_epoch), INTERVAL '2' MINUTES, INTERVAL"
                            + " '60' MINUTES)) WHERE row_num = 1 GROUP BY merchant_id, city,"
                            + " product_id, product_type, price, window_start, window_end,"
                            + " current_state_name");

        StatementSet stmtSet = tEnv.createStatementSet();
        // Insert cancelled Order items
        stmtSet.addInsert("inventory_sold_sink", cancelledOrderItems);
        // Insert approved Order items
        stmtSet.addInsert("inventory_sold_sink", approvedOrderItems);

        stmtSet.execute();
    }
}
