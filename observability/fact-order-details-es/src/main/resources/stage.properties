ORDER_LIFECYCLE_EVENTS_SOURCE_BROKERS = ****************************************.groupblinkit-agent.kafka.discoveryv2.prod-y.ap-southeast-1.warpstream.com:9092
ORDER_LIFECYCLE_EVENTS_SOURCE_CONSUMER_GROUP_ID = blinkit.data.test.flink-fact-order-details-emergency
ORDER_LIFECYCLE_EVENTS_SOURCE_TOPIC = blinkit.oms.emergency-services-order.lifecycle-events
ORDER_FACT_SINK_BROKERS = b-2.testdatacommon.vkkz3t.c5.kafka.ap-southeast-1.amazonaws.com:9092,b-1.testdatacommon.vkkz3t.c5.kafka.ap-southeast-1.amazonaws.com:9092
ORDER_FACT_SINK_TOPIC = stage.observability.metrics.fact-order-details-emergency
STATE_BACKEND_LOCATION = s3a://grofers-test-dse-singapore/flink-streams/observability/fact-order-details-es/checkpoints/