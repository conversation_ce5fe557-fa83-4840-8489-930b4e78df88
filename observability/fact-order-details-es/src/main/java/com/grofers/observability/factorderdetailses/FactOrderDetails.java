/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.grofers.observability.factorderdetailses;

import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.contrib.streaming.state.EmbeddedRocksDBStateBackend;
import org.apache.flink.streaming.api.CheckpointingMode;
import org.apache.flink.streaming.api.environment.CheckpointConfig;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.table.annotation.DataTypeHint;
import org.apache.flink.table.api.bridge.java.StreamTableEnvironment;
import org.apache.flink.table.functions.ScalarFunction;
import org.apache.flink.types.Row;

import com.grofers.observability.factorderdetailses.configs.JobConfigManager;

import java.io.IOException;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeFormatterBuilder;
import java.time.temporal.ChronoField;
import java.util.*;

import static com.grofers.observability.factorderdetailses.Queries.*;
import static com.grofers.observability.factorderdetailses.configs.JobConfig.*;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;

/**
 * Order Metrics Job Emergency Service
 *
 * <p>For a tutorial how to write a Flink streaming application, check the tutorials and examples on
 * the <a href="https://flink.apache.org/docs/stable/">Flink Website</a>.
 *
 * <p>To package your application into a JAR file for execution, run 'mvn clean package' on the
 * command line.
 *
 * <p>If you change the name of the main class (with the public static void main(String[] args))
 * method, change the respective entry in the POM.xml file (simply search for 'mainClass').
 */
public class FactOrderDetails {
    public static class GetTypeOfOrderFunction extends ScalarFunction {
        public String eval(
                @DataTypeHint("ARRAY< ROW <`id` INT, `type` VARCHAR(63) >>") Row[] arr,
                Integer key) {
            if (arr != null) {
                for (Row i : arr) {
                    if ((int) i.getFieldAs("id") == key) {
                        return i.getFieldAs("type").toString();
                    }
                }
            }
            return null;
        }
    }

    public static class GetTimeStampFromOrderEventsFunction extends ScalarFunction {
        public Long eval(
                @DataTypeHint(
                        "ARRAY< ROW< `event_state_change_to` STRING, `event_type` STRING,"
                                + " `timestamp` STRING >>")
                        Row[] arr,
                String eventValue) {
            if (arr != null) {
                String timestamp = null;
                for (Row i : arr) {
                    if (i.getFieldAs("event_type").equals(eventValue)
                            || (i.getFieldAs("event_type").equals("state_change")
                            && i.getFieldAs("event_state_change_to").equals(eventValue))) {
                        if (timestamp == null
                                || i.getFieldAs("timestamp").toString().compareTo(timestamp)
                                > 0) {
                            timestamp = i.getFieldAs("timestamp").toString();
                        }
                    }
                }
                return getEpochFromTimestamp(timestamp);
            }
            return null;
        }
    }


    public static class ExtractAdditionalChargeAmount extends ScalarFunction {
        public double eval(
                @DataTypeHint(
                        "ARRAY<ROW< `id` INT, `name` STRING, `amount` DOUBLE, `charge_id`"
                                + " INT >>")
                        Row[] arr,
                int chargeId) {

            if (arr != null) {
                for (Row row : arr) {
                    if (row != null) {
                        if (row.getField("charge_id") != null
                                && (int) row.getField("charge_id") == chargeId
                                && row.getField("amount") != null) {
                            return (Double) row.getField("amount");
                        }
                    }
                }
            }
            return 0.0;
        }
    }

    private static Long getEpochFromTimestamp(String utcTimestamp) {
        if (utcTimestamp == null) return null;
        // Check if the utcTimestamp is in the form of an epoch
        try {
            long epochTimestamp = Long.parseLong(utcTimestamp);
            // If the parsing succeeds, it means the timestamp is already in epoch format
            return epochTimestamp;
        } catch (NumberFormatException e) {
            // If parsing fails, it means the timestamp is in string format
            // Define two formats to handle both cases
            DateTimeFormatter formatter =
                    new DateTimeFormatterBuilder()
                            .appendPattern("yyyy-MM-dd'T'HH:mm:ss")
                            .optionalStart() // Optional start for milliseconds
                            .appendFraction(ChronoField.MICRO_OF_SECOND, 0, 6, true)
                            .optionalEnd() // Optional end for milliseconds
                            .optionalStart() // Optional start for the timezone offset
                            .appendOffset("+HHMM", "Z") // Offset and 'Z' are optional
                            .optionalEnd() // Optional end for the timezone offset
                            .toFormatter();

            ZonedDateTime zonedDateTimeDate;
            try {
                // First try to parse with the formatter without milliseconds and 'Z'
                zonedDateTimeDate =
                        LocalDateTime.parse(utcTimestamp, formatter).atZone(ZoneId.of("UTC"));
                return zonedDateTimeDate.toInstant().toEpochMilli();
            } catch (java.time.format.DateTimeParseException ex) {
                return null;
            }
        }
    }

    public static void main(String[] args)
            throws JobConfigManager.InvalidEnvironmentException, IOException {
        final StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment();
        final StreamTableEnvironment tEnv = StreamTableEnvironment.create(env);
        tEnv.createTemporarySystemFunction("GetTypeOfOrderFunction", GetTypeOfOrderFunction.class);
        tEnv.createTemporarySystemFunction(
                "GetTimeStampFromOrderEventsFunction", GetTimeStampFromOrderEventsFunction.class);
        tEnv.createTemporarySystemFunction(
                "ExtractAdditionalChargeAmount", ExtractAdditionalChargeAmount.class);
        Configuration configuration = tEnv.getConfig().getConfiguration();
        configuration.setString("table.exec.source.idle-timeout", "1200 s");
        configuration.setString("table.exec.state.ttl", "9000 s");

        // local-global aggregation depends on mini-batch is enabled
        configuration.setString("table.exec.mini-batch.enabled", "true");
        configuration.setString("table.exec.mini-batch.allow-latency", "1 s");
        configuration.setString("table.exec.mini-batch.size", "500");

        configuration.setString("table.optimizer.agg-phase-strategy", "TWO_PHASE");
        configuration.setString("table.optimizer.distinct-agg.split.enabled", "true");

        ParameterTool params = ParameterTool.fromArgs(args);
        String userEnv = params.getRequired("environment");

        // Get job configs
        JobConfigManager.setJobConfigsPath(userEnv);
        String jobPropertiesPath = JobConfigManager.getJobConfigsPath(userEnv);
        JobConfigManager.setJobConfigs(tEnv, jobPropertiesPath);
        JobConfigManager.getJobConfigs(tEnv);

        // Checkpoint Configs
        env.enableCheckpointing(600000);
        // set mode to exactly-once (default)
        env.getCheckpointConfig().setCheckpointingMode(CheckpointingMode.EXACTLY_ONCE);
        // make sure 900000 ms of progress happen between checkpoints
        env.getCheckpointConfig().setMinPauseBetweenCheckpoints(300000);
        // checkpoints have to complete within this minute, or are discarded
        env.getCheckpointConfig().setCheckpointTimeout(600000);
        // only two consecutive checkpoint failures are tolerated
        env.getCheckpointConfig().setTolerableCheckpointFailureNumber(2);
        // allow only one checkpoint to be in progress at the same time
        env.getCheckpointConfig().setMaxConcurrentCheckpoints(1);
        // enable externalized checkpoints which are retained after job cancellation
        env.getCheckpointConfig()
                .setExternalizedCheckpointCleanup(
                        CheckpointConfig.ExternalizedCheckpointCleanup.DELETE_ON_CANCELLATION);
        // enable unaligned checkpoints
        env.getCheckpointConfig().enableUnalignedCheckpoints();
        // sets a RocksDB checkpoint storage where checkpoint snapshots will be incrementally
        // written
        env.setStateBackend(new EmbeddedRocksDBStateBackend(true));
        env.getCheckpointConfig().setCheckpointStorage(STATE_BACKEND_LOCATION);

        String omsOrderCreateTable =
                String.format(
                        EMERGENCY_ORDER_LIFECYCLE_EVENTS,
                        ORDER_LIFECYCLE_EVENTS_SOURCE_TOPIC,
                        ORDER_LIFECYCLE_EVENTS_SOURCE_BROKERS,
                        ORDER_LIFECYCLE_EVENTS_SOURCE_CONSUMER_GROUP_ID);
        tEnv.executeSql(omsOrderCreateTable);


        String orderMetricsCreateTable =
                String.format(
                        ORDER_FACT_CREATE_TABLE, ORDER_FACT_SINK_TOPIC, ORDER_FACT_SINK_BROKERS);
        tEnv.executeSql(orderMetricsCreateTable);

        tEnv.executeSql(ORDER_FACT_METRIC);
    }
}
