package com.grofers.observability.factorderdetailses;

public class Queries {
    public static final String EMERGENCY_ORDER_LIFECYCLE_EVENTS =
            "CREATE TABLE IF NOT EXISTS order_lifecycle_events ( `payload` ROW(order_id INT,"
                + " `order` ROW(datetime_created BIGINT, datetime_updated BIGINT,"
                + " current_state_name VARCHAR(50), device_id STRING, additional_charges_data"
                + " ARRAY<ROW< `id` INT, `name` STRING, `amount` DOUBLE, `charge_id` INT >>, "
                + " source STRING, cart_id BIGINT, total_cost DOUBLE,"
                + " delivery_cost DOUBLE, offer_details ROW(`total_discount` DOUBLE), net_cost DOUBLE,"
                + "   order_events ARRAY<"
                + " ROW(`event_state_change_to` STRING, `event_type` STRING, `timestamp` STRING"
                + " )>, `slot_properties`"
                + " ROW(`tags` ARRAY<STRING>, `slot_charge` DOUBLE,"
                + " `serviceability` ROW(`eta` INT,"
                + " `surge_charge_v2` ROW(`source` STRING,"
                + " `surge_amount` DOUBLE ), `session_id` STRING, `components` ARRAY< ROW(`name`"
                + " STRING, `duration` DOUBLE) >)), additional_charges_amount DOUBLE,"
                + " customer"
                + " ROW( `id` INT), cart ROW(`orders` ARRAY< ROW (`id` INT, `type` VARCHAR(63)"
                + " )>),"
                + " payment ROW(`mode` STRING), org_channel_id VARCHAR(10), node ROW(`id` INT, `name` STRING, `type` STRING))),"
                + " flink_event_time_epoch AS TO_TIMESTAMP_LTZ(`payload`.`order`.datetime_updated,"
                + " 0),WATERMARK FOR flink_event_time_epoch AS flink_event_time_epoch - INTERVAL"
                + " '60' seconds) WITH (  'connector' = 'kafka', 'format' = 'json', 'topic' ="
                + " '%s', 'properties.bootstrap.servers' = '%s', 'properties.group.id' = '%s',"
                + " 'scan.startup.mode' = 'group-offsets'  )";

    public static final String ORDER_FACT_METRIC =
            " INSERT INTO fact_order_details          SELECT o.`payload`.order_id,"
                + " o.`payload`.`order`.datetime_created*1000 AS insert_timestamp_epoch,       "
                + " o.`payload`.`order`.datetime_updated*1000 AS update_timestamp_epoch,"
                + " o.`payload`.`order`.`customer`.id, o.`payload`.`order`.current_state_name,    "
                + "    o.`payload`.`order`.total_cost,        o.`payload`.`order`.delivery_cost,"
                + " o.`payload`.`order`.`offer_details`.`total_discount`,        o.`payload`.`order`.net_cost,"
                + " GetTypeOfOrderFunction(o.`payload`.`order`.cart.orders, o.`payload`.order_id)"
                + " AS TYPE,        o.`payload`.`order`.cart_id,       "
                + " o.`payload`.`order`.additional_charges_amount,       "
                + " o.`payload`.`order`.slot_properties.slot_charge AS slot_charge,"
                + "     o.`payload`.`order`.device_id AS device_id,"
                + " o.`payload`.`order`.source AS SOURCE,       "
                + " o.`payload`.`order`.org_channel_id,"
                + " o.`payload`.`order`.slot_properties.serviceability.eta"
                + " AS eta_shown,"
                + " GetTimeStampFromOrderEventsFunction(o.`payload`.`order`.order_events,"
                + " 'BILLED') AS order_billed_timestamp,"
                + " GetTimeStampFromOrderEventsFunction(o.`payload`.`order`.order_events,"
                + " 'COMPLETED') AS completed_timestamp,"
                + " GetTimeStampFromOrderEventsFunction(o.`payload`.`order`.order_events,"
                + " 'ASSIGNED') AS assigned_timestamp,"
                + " GetTimeStampFromOrderEventsFunction(o.`payload`.`order`.order_events,"
                + " 'REACHED_PICKUP') AS reached_pickup_timestamp,"
                + " GetTimeStampFromOrderEventsFunction(o.`payload`.`order`.order_events,"
                + " 'ENROUTE_DROP') AS enroute_drop_timestamp,"
                + " GetTimeStampFromOrderEventsFunction(o.`payload`.`order`.order_events,"
                + " 'PAYMENT_PENDING') AS payment_pending_timestamp,"
                + " GetTimeStampFromOrderEventsFunction(o.`payload`.`order`.order_events,"
                + " 'CANCELLED') AS cancelled_timestamp,"
                + " GetTimeStampFromOrderEventsFunction(o.`payload`.`order`.order_events,"
                + " 'ENROUTE_PICKUP') AS order_enroute_pickup_timestamp,"
                + " GetTimeStampFromOrderEventsFunction(o.`payload`.`order`.order_events,"
                + " 'CREATED') AS checkout_timestamp,"
                + " GetTimeStampFromOrderEventsFunction(o.`payload`.`order`.order_events,"
                + " 'APPROVED') AS approved_timestamp,"
                + " o.`payload`.`order`.slot_properties.serviceability.surge_charge_v2.source AS"
                + " surge_type,"
                + " o.`payload`.`order`.slot_properties.serviceability.surge_charge_v2.surge_amount"
                + " AS surge_amount,       "
                + " o.`payload`.`order`.slot_properties.serviceability.session_id AS session_id,"
                + " ExtractAdditionalChargeAmount(o.`payload`.additional_charges_data, 3) AS"
                + " handling_charge,"
                + " ExtractAdditionalChargeAmount(o.`payload`.additional_charges_data, 4) AS"
                + " convenience_charge,       "
                + " o.`order`.payment.mode AS payment_mode, o.`payload`.`order`.`node`.`id` AS node_id, o.`payload`.`order`.`node`.`name` AS node_name, o.`payload`.`order`.`node`.`type` AS node_type FROM"
                + " order_lifecycle_events AS o "
                + " WHERE (GetTypeOfOrderFunction(o.`payload`.`order`.cart.orders, o.`payload`.order_id)"
                + " IS NULL OR GetTypeOfOrderFunction(o.`payload`.`order`.cart.orders,"
                + " o.`payload`.order_id) IN ('EmergencyServiceAmbulanceOrder')) AND o.`payload`.`order`.`node`.`id`<>7" ;

    public static final String ORDER_FACT_CREATE_TABLE =
            "CREATE TABLE IF NOT EXISTS fact_order_details (     id INT,     insert_timestamp"
                + " BIGINT,     update_timestamp BIGINT,     customer_id INT,     current_status"
                + " VARCHAR(50),     total_cost DOUBLE,     delivery_cost DOUBLE,     discount"
                + " DOUBLE,     net_cost DOUBLE,   type"
                + " VARCHAR(63),     cart_id BIGINT, "
                + "    additional_charges_amount DOUBLE,    "
                + " slot_charge DOUBLE,  device_id STRING,"
                + " source STRING,     org_channel_id VARCHAR(10), "
                + " eta_shown BIGINT,     order_billed_timestamp BIGINT,    "
                + "  completed_timestamp BIGINT,    "
                + "  assigned_timestamp BIGINT,    "
                + "  reached_pickup_timestamp BIGINT,    "
                + "  enroute_drop_timestamp BIGINT,    "
                + "  payment_pending_timestamp BIGINT,    "
                + "  cancelled_timestamp BIGINT,    "
                + "    order_enroute_pickup_timestamp BIGINT,    "
                + " checkout_timestamp BIGINT,     approved_timestamp BIGINT,  "
                + " surge_type STRING,"
                + " surge_amount DOUBLE, session_id"
                + " STRING, handling_charge DOUBLE, convenience_charge DOUBLE,"
                + " payment_mode STRING, node_id INT, node_name STRING, node_type STRING ) WITH (    'connector' = 'kafka',    'topic' = '%s',"
                + "    'properties.bootstrap.servers' = '%s',    'key.format' = 'json',   "
                + " 'value.format' = 'json', 'key.fields' = 'cart_id',  'sink.partitioner' ="
                + " 'fixed' )";
}
