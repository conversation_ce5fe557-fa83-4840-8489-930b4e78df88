
# Properties for prod environment

#Checkpoints
CHECKPOINTS_STORAGE_LOCATION = s3a://prod-data-flink-states/flink-streams/observability/daily-active-users-bistro/checkpoints/

# Events Source
EVENTS_SOURCE_BROKERS = ****************************************.groupblinkit-agent.kafka.discoveryv2.prod-y.ap-southeast-1.warpstream.com:9092
EVENTS_SOURCE_CONSUMER_GROUP_ID = flink-observability-daily-active-users-bistro-consumer_group
EVENTS_SOURCE_TOPIC = jumbo_transformed.blinkit.bistro_click_events

#Watermark Idleness Configs
IDLENESS_TIME_IN_SECS = 600

# DAU Window Configs
DAU_WINDOW_SIZE_IN_DAYS = 1
MERCHANT_DAU_METRICS_SINK_BROKERS = b-1.prod-data-corestr.uynfnv.c4.kafka.ap-southeast-1.amazonaws.com:9092,b-2.prod-data-corestr.uynfnv.c4.kafka.ap-southeast-1.amazonaws.com:9092,b-3.prod-data-corestr.uynfnv.c4.kafka.ap-southeast-1.amazonaws.com:9092
MERCHANT_DAU_METRICS_SINK_TOPIC = observability.metrics.merchant-daily-active-users-bistro

CITY_DAU_METRICS_SINK_BROKERS = b-1.prod-data-corestr.uynfnv.c4.kafka.ap-southeast-1.amazonaws.com:9092,b-2.prod-data-corestr.uynfnv.c4.kafka.ap-southeast-1.amazonaws.com:9092,b-3.prod-data-corestr.uynfnv.c4.kafka.ap-southeast-1.amazonaws.com:9092
CITY_DAU_METRICS_SINK_TOPIC = observability.metrics.city-daily-active-users-bistro
