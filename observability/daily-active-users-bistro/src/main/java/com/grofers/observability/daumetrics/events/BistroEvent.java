package com.grofers.observability.daumetrics.events;

import lombok.Getter;
import lombok.NoArgsConstructor;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.annotation.JsonProperty;

@JsonIgnoreProperties(ignoreUnknown = true)
@Getter
@NoArgsConstructor(force = true)
public class BistroEvent {
    @JsonProperty(value = "event_name", required = true)
    private final String eventName;

    @JsonProperty(value = "user_id")
    private final String userId;

    @JsonProperty(value = "source")
    private final String source;

    @JsonProperty(value = "time")
    private final Long eventTimestamp;

    @JsonProperty(value = "device_id")
    private final String deviceId;

    @JsonProperty(value = "city_name")
    private final String cityName;

    @JsonProperty(value = "merchant_id")
    private final Integer merchantId;

    @JsonProperty(value = "channel")
    private final String channel;

    @JsonProperty(value = "device_uuid")
    private final String deviceUuid;

    public BistroEvent(BistroEvent event) {
        this.eventName = event.eventName;
        this.userId = event.userId;
        this.source = event.source;
        this.eventTimestamp = event.eventTimestamp;
        this.deviceId = event.deviceId;
        this.cityName = event.cityName;
        this.merchantId = event.merchantId;
        this.channel = event.channel;
        this.deviceUuid = event.deviceUuid;
    }
}