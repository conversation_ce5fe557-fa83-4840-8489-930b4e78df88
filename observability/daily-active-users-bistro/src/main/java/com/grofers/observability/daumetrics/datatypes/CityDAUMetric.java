package com.grofers.observability.daumetrics.datatypes;

import lombok.Value;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.annotation.JsonProperty;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.annotation.JsonSerialize;

@Value
@JsonSerialize
public class CityDAUMetric {
    @JsonProperty("app")
    String app;

    @JsonProperty("city")
    String city;

    @JsonProperty("window_start")
    Long windowStart;

    @JsonProperty("window_end")
    Long windowEnd;

    @JsonProperty("unique_device_count")
    Integer uniqueDeviceCount;

    public CityDAUMetric(
            String app, String city, Long windowStart, Long windowEnd, Integer uniqueDeviceCount) {
        this.app = app;
        this.city = city;
        this.windowStart = windowStart;
        this.windowEnd = windowEnd;
        this.uniqueDeviceCount = uniqueDeviceCount;
    }
}
