package com.grofers.observability.daumetrics;

import java.util.HashSet;
import java.util.Iterator;
import java.util.Set;
import com.grofers.observability.daumetrics.events.BistroEvent;
import org.apache.flink.streaming.api.windowing.evictors.Evictor;
import org.apache.flink.streaming.api.windowing.windows.Window;
import org.apache.flink.streaming.runtime.operators.windowing.TimestampedValue;

public class UniqueUserEvictor<W extends Window> implements Evictor<BistroEvent, W> {
    @Override
    public void evictBefore(
            Iterable<TimestampedValue<BistroEvent>> iterable,
            int i,
            W w,
            EvictorContext evictorContext) {}

    @Override
    public void evictAfter(
            Iterable<TimestampedValue<BistroEvent>> iterable,
            int i,
            W w,
            EvictorContext evictorContext) {
        this.evict(iterable, evictorContext);
    }

    private void evict(Iterable<TimestampedValue<BistroEvent>> elements, EvictorContext ctx) {
        Set<String> uniqueIds = new HashSet<>();
        Iterator<TimestampedValue<BistroEvent>> iterator = elements.iterator();
        while (iterator.hasNext()) {
            BistroEvent event = iterator.next().getValue();
            String uniqueId = DAUMetrics.getUniqueId(event);
            if (uniqueIds.contains(uniqueId)) {
                iterator.remove();
            } else {
                uniqueIds.add(uniqueId);
            }
        }
    }

    public static <W extends Window> UniqueUserEvictor<W> create() {
        return new UniqueUserEvictor<>();
    }
}
