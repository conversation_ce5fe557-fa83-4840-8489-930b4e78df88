name: Serviceability Metrics
image: 442534439095.dkr.ecr.ap-southeast-1.amazonaws.com/data/flink-streams/observability/serviceability-metrics
flink_config:
  taskmanager.memory.process.size: 10240m
  jobmanager.memory.process.size: 1024m
  taskmanager.numberOfTaskSlots: 3
  parallelism.default: 3
  process.working-dir: serviceability-metrics/process
  state.backend.local-recovery: true
  taskmanager.resource-id: TaskManager_ServiceabilityMetrics
deployment_config:
  kubernetes.rest-service.exposed.type: NodePort
  metrics.reporters: prom
  metrics.reporter.prom.factory.class: org.apache.flink.metrics.prometheus.PrometheusReporterFactory
allow_non_restored_state: False # Set to True when changes are made to Operator
codeartifact_access: True