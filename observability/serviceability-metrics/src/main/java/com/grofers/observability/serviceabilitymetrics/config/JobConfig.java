
package com.grofers.observability.serviceabilitymetrics.config;

public final class JobConfig {
    public static String STATE_BACKEND_LOCATION;
    public static String SERVICEABILITY_SOURCE_BROKERS;
    public static String SERVICEABILITY_SOURCE_CONSUMER_GROUP_ID;
    public static String SERVICEABILITY_SOURCE_TOPIC;
    public static String SINK_BROKERS;
    public static String SURGE_METRICS_SINK_TOPIC;
    public static String SURGE_METRICS_HOURLY_SINK_TOPIC;
    public static String SURGE_METRICS_DAILY_SINK_TOPIC;
    public static String SURGE_METRICS_CITY_HOURLY_SINK_TOPIC;
    public static String SURGE_METRICS_CITY_DAILY_SINK_TOPIC;

    public JobConfig(
            String STATE_BACKEND_LOCATION,
            String SERVICEABILITY_SOURCE_BROKERS,
            String SERVICEABILITY_SOURCE_CONSUMER_GROUP_ID,
            String SERVICEABILITY_SOURCE_TOPIC,
            String SINK_BROKERS,
            String SURGE_METRICS_SINK_TOPIC,
            String SURGE_METRICS_HOURLY_SINK_TOPIC,
            String SURGE_METRICS_DAILY_SINK_TOPIC,
            String SURGE_METRICS_CITY_DAILY_SINK_TOPIC,
            String SURGE_METRICS_CITY_HOURLY_SINK_TOPIC) {

        JobConfig.STATE_BACKEND_LOCATION = STATE_BACKEND_LOCATION;
        JobConfig.SERVICEABILITY_SOURCE_BROKERS = SERVICEABILITY_SOURCE_BROKERS;
        JobConfig.SERVICEABILITY_SOURCE_CONSUMER_GROUP_ID = SERVICEABILITY_SOURCE_CONSUMER_GROUP_ID;
        JobConfig.SERVICEABILITY_SOURCE_TOPIC = SERVICEABILITY_SOURCE_TOPIC;
        JobConfig.SINK_BROKERS = SINK_BROKERS;
        JobConfig.SURGE_METRICS_SINK_TOPIC = SURGE_METRICS_SINK_TOPIC;
        JobConfig.SURGE_METRICS_HOURLY_SINK_TOPIC = SURGE_METRICS_HOURLY_SINK_TOPIC;
        JobConfig.SURGE_METRICS_DAILY_SINK_TOPIC = SURGE_METRICS_DAILY_SINK_TOPIC;
        JobConfig.SURGE_METRICS_CITY_HOURLY_SINK_TOPIC = SURGE_METRICS_CITY_HOURLY_SINK_TOPIC;
        JobConfig.SURGE_METRICS_CITY_DAILY_SINK_TOPIC = SURGE_METRICS_CITY_DAILY_SINK_TOPIC;
    }
}
