package com.grofers.observability.serviceabilitymetrics;

import static com.grofers.observability.serviceabilitymetrics.Queries.*;
import static com.grofers.observability.serviceabilitymetrics.config.JobConfig.*;

import com.grofers.observability.serviceabilitymetrics.config.JobConfigManager;
import java.io.IOException;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.contrib.streaming.state.EmbeddedRocksDBStateBackend;
import org.apache.flink.streaming.api.CheckpointingMode;
import org.apache.flink.streaming.api.environment.CheckpointConfig;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.table.api.StatementSet;
import org.apache.flink.table.api.TableResult;
import org.apache.flink.table.api.bridge.java.StreamTableEnvironment;

public class ServiceabilityMetrics {
    public static void main(String[] args)
            throws JobConfigManager.InvalidEnvironmentException, IOException {
        final StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment();
        final StreamTableEnvironment tEnv = StreamTableEnvironment.create(env);
        Configuration configuration = tEnv.getConfig().getConfiguration();
        configuration.setString("table.exec.source.idle-timeout", "1200 s");
        configuration.setString("table.exec.state.ttl", "1800 s");

        // local-global aggregation depends on mini-batch is enabled
        configuration.setString("table.exec.mini-batch.enabled", "true");
        configuration.setString("table.exec.mini-batch.allow-latency", "1 s");
        configuration.setString("table.exec.mini-batch.size", "500");

        configuration.setString("table.optimizer.agg-phase-strategy", "TWO_PHASE");
        configuration.setString("table.optimizer.distinct-agg.split.enabled", "true");

        ParameterTool params = ParameterTool.fromArgs(args);
        String userEnv = params.getRequired("environment");

        // Get job configs
        JobConfigManager.setJobConfigsPath(userEnv);
        String jobPropertiesPath = JobConfigManager.getJobConfigsPath(userEnv);
        JobConfigManager.setJobConfigs(tEnv, jobPropertiesPath);
        JobConfigManager.getJobConfigs(tEnv);

        // Checkpoint Configs
        env.enableCheckpointing(600000);
        // set mode to exactly-once (default)
        env.getCheckpointConfig().setCheckpointingMode(CheckpointingMode.EXACTLY_ONCE);
        // make sure 900000 ms of progress happen between checkpoints
        env.getCheckpointConfig().setMinPauseBetweenCheckpoints(300000);
        // checkpoints have to complete within this minute, or are discarded
        env.getCheckpointConfig().setCheckpointTimeout(600000);
        // only two consecutive checkpoint failures are tolerated
        env.getCheckpointConfig().setTolerableCheckpointFailureNumber(2);
        // allow only one checkpoint to be in progress at the same time
        env.getCheckpointConfig().setMaxConcurrentCheckpoints(1);
        // enable externalized checkpoints which are retained after job cancellation
        env.getCheckpointConfig()
                .setExternalizedCheckpointCleanup(
                        CheckpointConfig.ExternalizedCheckpointCleanup.DELETE_ON_CANCELLATION);
        // enable unaligned checkpoints
        env.getCheckpointConfig().enableUnalignedCheckpoints();
        // sets a RocksDB checkpoint storage where checkpoint snapshots will be incrementally
        // written
        env.setStateBackend(new EmbeddedRocksDBStateBackend(true));
        env.getCheckpointConfig().setCheckpointStorage(STATE_BACKEND_LOCATION);

        String serviceabilityCreateTable =
                String.format(
                        SERVICEABILITY_EVENTS,
                        SERVICEABILITY_SOURCE_TOPIC,
                        SERVICEABILITY_SOURCE_BROKERS,
                        SERVICEABILITY_SOURCE_CONSUMER_GROUP_ID);
        tEnv.executeSql(serviceabilityCreateTable);

        String surgeMetricsCreateTable =
                String.format(SURGE_METRIC_CREATE_TABLE, SURGE_METRICS_SINK_TOPIC, SINK_BROKERS);
        tEnv.executeSql(surgeMetricsCreateTable);

        String surgeMetricsHourlyCreateTable =
                String.format(SURGE_METRIC_HOURLY_CREATE_TABLE, SURGE_METRICS_HOURLY_SINK_TOPIC, SINK_BROKERS);
        tEnv.executeSql(surgeMetricsHourlyCreateTable);

        String surgeMetricsDailyCreateTable =
                String.format(SURGE_METRIC_DAILY_CREATE_TABLE, SURGE_METRICS_DAILY_SINK_TOPIC, SINK_BROKERS);
        tEnv.executeSql(surgeMetricsDailyCreateTable);

        String surgeMetricsCityHourlyCreateTable =
                String.format(SURGE_METRIC_CITY_HOURLY_CREATE_TABLE, SURGE_METRICS_CITY_HOURLY_SINK_TOPIC, SINK_BROKERS);
        tEnv.executeSql(surgeMetricsCityHourlyCreateTable);

        String surgeMetricsCityDailyCreateTable =
                String.format(SURGE_METRIC_CITY_DAILY_CREATE_TABLE, SURGE_METRICS_CITY_DAILY_SINK_TOPIC, SINK_BROKERS);
        tEnv.executeSql(surgeMetricsCityDailyCreateTable);

        StatementSet stmtSet = tEnv.createStatementSet();
        stmtSet.addInsertSql(SURGE_METRIC_INSERT);
        stmtSet.addInsertSql(SURGE_METRIC_HOURLY_INSERT);
        stmtSet.addInsertSql(SURGE_METRIC_DAILY_INSERT);
        stmtSet.addInsertSql(SURGE_METRIC_CITY_HOURLY_INSERT);
        stmtSet.addInsertSql(SURGE_METRIC_CITY_DAILY_INSERT);
        // execute all statements together
        TableResult tableResult2 = stmtSet.execute();
    }
}
