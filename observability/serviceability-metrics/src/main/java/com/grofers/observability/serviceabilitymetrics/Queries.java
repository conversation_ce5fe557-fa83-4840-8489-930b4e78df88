
package com.grofers.observability.serviceabilitymetrics;

public class Queries {
    public static final String SERVICEABILITY_EVENTS =
            "CREATE TABLE IF NOT EXISTS serviceability_events ("
                    + "  `city_name` STRING,"
                    + "  `merchant_id` BIGINT,"
                    + "  `downstream_service` STRING,"
                    + "  `surge_charge_v2` ROW("
                    + "      `surge_amount` INT,"
                    + "      `source` STRING"
                    + "  ),"
                    + "  `time` INT,"
                    + "  flink_event_time AS TO_TIMESTAMP_LTZ(`time`,0) + INTERVAL '5' HOUR + INTERVAL '30' MINUTE,"
                    + "  WATERMARK FOR flink_event_time AS flink_event_time"
                    + ") WITH ("
                    + "  'connector' = 'kafka',"
                    + "  'format' = 'json',"
                    + "  'topic' = '%s',"
                    + "  'properties.bootstrap.servers' = '%s',"
                    + "  'properties.group.id' = '%s',"
                    + "  'scan.startup.mode' = 'latest-offset' )";

    public static final String SURGE_METRIC_INSERT =
            "INSERT INTO ticktock_serviceability_surge "
                    + "SELECT window_start, "
                    + "window_end, "
                    + "merchant_id, "
                    + "COUNT(*) FILTER (WHERE surge_charge_v2.surge_amount > 0) AS total_surge_carts, "
                    + "COUNT(*) FILTER (WHERE surge_charge_v2.surge_amount > 0 and surge_charge_v2.source LIKE '%fe%') AS rider_surge_carts, "
                    + "COUNT(*) FILTER (WHERE surge_charge_v2.surge_amount > 0 and surge_charge_v2.source LIKE '%picker%') AS picker_surge_carts, "
                    + "COUNT(*) FILTER (WHERE surge_charge_v2.surge_amount > 0 and surge_charge_v2.source = 'rain_surge') AS rain_surge_carts, "
                    + "COUNT(*) AS total_carts "
                    + "FROM TABLE("
                    + "HOP(TABLE serviceability_events, "
                    + "DESCRIPTOR(flink_event_time), "
                    + "INTERVAL '1' MINUTES, "
                    + "INTERVAL '15' MINUTES)) "
                    + "WHERE downstream_service IN ('checkout-service-primary','checkout-service-canary','checkout-service') AND flink_event_time is NOT NULL "
                    + "GROUP BY window_start, window_end, merchant_id";

    public static final String SURGE_METRIC_HOURLY_INSERT =
            "INSERT INTO ticktock_serviceability_surge_hourly "
                    + "SELECT window_start, "
                    + "window_end, "
                    + "merchant_id, "
                    + "COUNT(*) FILTER (WHERE surge_charge_v2.surge_amount > 0) AS total_surge_carts, "
                    + "COUNT(*) AS total_carts "
                    + "FROM TABLE("
                    + "CUMULATE(TABLE serviceability_events, "
                    + "DESCRIPTOR(flink_event_time), "
                    + "INTERVAL '5' MINUTES, INTERVAL '60' MINUTES)) "
                    + "WHERE downstream_service IN ('checkout-service-primary','checkout-service-canary','checkout-service') AND flink_event_time is NOT NULL "
                    + "GROUP BY window_start, window_end, merchant_id";

    public static final String SURGE_METRIC_DAILY_INSERT =
            "INSERT INTO ticktock_serviceability_surge_daily "
                    + "SELECT window_start, "
                    + "window_end, "
                    + "merchant_id, "
                    + "COUNT(*) FILTER (WHERE surge_charge_v2.surge_amount > 0) AS total_surge_carts, "
                    + "COUNT(*) FILTER (WHERE surge_charge_v2.surge_amount > 0 and surge_charge_v2.source LIKE '%fe%') AS rider_surge_carts, "
                    + "COUNT(*) FILTER (WHERE surge_charge_v2.surge_amount > 0 and surge_charge_v2.source LIKE '%picker%') AS picker_surge_carts, "
                    + "COUNT(*) FILTER (WHERE surge_charge_v2.surge_amount > 0 and surge_charge_v2.source = 'rain_surge') AS rain_surge_carts, "
                    + "COUNT(*) AS total_carts "
                    + "FROM TABLE("
                    + "CUMULATE(TABLE serviceability_events, "
                    + "DESCRIPTOR(flink_event_time), "
                    + "INTERVAL '5' MINUTES, INTERVAL '24' HOURS)) "
                    + "WHERE downstream_service IN ('checkout-service-primary','checkout-service-canary','checkout-service') AND flink_event_time is NOT NULL "
                    + "GROUP BY window_start, window_end, merchant_id";

    public static final String SURGE_METRIC_CITY_HOURLY_INSERT =
            "INSERT INTO ticktock_serviceability_surge_city_hourly "
                    + "SELECT window_start, "
                    + "window_end, "
                    + "city_name, "
                    + "COUNT(*) FILTER (WHERE surge_charge_v2.surge_amount > 0) AS total_surge_carts, "
                    + "COUNT(*) AS total_carts "
                    + "FROM TABLE("
                    + "CUMULATE(TABLE serviceability_events, "
                    + "DESCRIPTOR(flink_event_time), "
                    + "INTERVAL '5' MINUTES, INTERVAL '60' MINUTES)) "
                    + "WHERE downstream_service IN ('checkout-service-primary','checkout-service-canary','checkout-service') AND flink_event_time is NOT NULL "
                    + "GROUP BY window_start, window_end, city_name";

    public static final String SURGE_METRIC_CITY_DAILY_INSERT =
            "INSERT INTO ticktock_serviceability_surge_city_daily "
                    + "SELECT window_start, "
                    + "window_end, "
                    + "city_name, "
                    + "COUNT(*) FILTER (WHERE surge_charge_v2.surge_amount > 0) AS total_surge_carts, "
                    + "COUNT(*) FILTER (WHERE surge_charge_v2.surge_amount > 0 and surge_charge_v2.source LIKE '%fe%') AS rider_surge_carts, "
                    + "COUNT(*) FILTER (WHERE surge_charge_v2.surge_amount > 0 and surge_charge_v2.source LIKE '%picker%') AS picker_surge_carts, "
                    + "COUNT(*) FILTER (WHERE surge_charge_v2.surge_amount > 0 and surge_charge_v2.source = 'rain_surge') AS rain_surge_carts, "
                    + "COUNT(*) AS total_carts "
                    + "FROM TABLE("
                    + "CUMULATE(TABLE serviceability_events, "
                    + "DESCRIPTOR(flink_event_time), "
                    + "INTERVAL '5' MINUTES, INTERVAL '24' HOURS)) "
                    + "WHERE downstream_service IN ('checkout-service-primary','checkout-service-canary','checkout-service') AND flink_event_time is NOT NULL "
                    + "GROUP BY window_start, window_end, city_name";

    public static final String SURGE_METRIC_CREATE_TABLE =
            "CREATE TABLE IF NOT EXISTS ticktock_serviceability_surge (     "
                    + "window_start TIMESTAMP(3),     "
                    + "window_end TIMESTAMP(3),     "
                    + "frontend_merchant_id BIGINT,    "
                    + "total_surge_carts BIGINT,     "
                    + "rider_surge_carts BIGINT,     "
                    + "picker_surge_carts BIGINT,     "
                    + "rain_surge_carts BIGINT,     "
                    + "total_carts BIGINT) WITH (    "
                    + "'connector' = 'kafka',   "
                    + "'topic' = '%s',    "
                    + "'properties.bootstrap.servers' = '%s',    "
                    + "'format' = 'json'    )";

    public static final String SURGE_METRIC_HOURLY_CREATE_TABLE =
            "CREATE TABLE IF NOT EXISTS ticktock_serviceability_surge_hourly (     "
                    + "window_start TIMESTAMP(3),     "
                    + "window_end TIMESTAMP(3),     "
                    + "frontend_merchant_id BIGINT,    "
                    + "total_surge_carts BIGINT,     "
                    + "total_carts BIGINT) WITH (    "
                    + "'connector' = 'kafka',   "
                    + "'topic' = '%s',    "
                    + "'properties.bootstrap.servers' = '%s',    "
                    + "'format' = 'json'    )";

    public static final String SURGE_METRIC_DAILY_CREATE_TABLE =
            "CREATE TABLE IF NOT EXISTS ticktock_serviceability_surge_daily (     "
                    + "window_start TIMESTAMP(3),     "
                    + "window_end TIMESTAMP(3),     "
                    + "frontend_merchant_id BIGINT,    "
                    + "total_surge_carts BIGINT,     "
                    + "rider_surge_carts BIGINT,     "
                    + "picker_surge_carts BIGINT,     "
                    + "rain_surge_carts BIGINT,     "
                    + "total_carts BIGINT) WITH (    "
                    + "'connector' = 'kafka',   "
                    + "'topic' = '%s',    "
                    + "'properties.bootstrap.servers' = '%s',    "
                    + "'format' = 'json'    )";

    public static final String SURGE_METRIC_CITY_HOURLY_CREATE_TABLE =
            "CREATE TABLE IF NOT EXISTS ticktock_serviceability_surge_city_hourly (     "
                    + "window_start TIMESTAMP(3),     "
                    + "window_end TIMESTAMP(3),     "
                    + "city_name STRING,    "
                    + "total_surge_carts BIGINT,     "
                    + "total_carts BIGINT) WITH (    "
                    + "'connector' = 'kafka',   "
                    + "'topic' = '%s',    "
                    + "'properties.bootstrap.servers' = '%s',    "
                    + "'format' = 'json'    )";

    public static final String SURGE_METRIC_CITY_DAILY_CREATE_TABLE =
            "CREATE TABLE IF NOT EXISTS ticktock_serviceability_surge_city_daily (     "
                    + "window_start TIMESTAMP(3),     "
                    + "window_end TIMESTAMP(3),     "
                    + "city_name STRING,    "
                    + "total_surge_carts BIGINT,     "
                    + "rider_surge_carts BIGINT,     "
                    + "picker_surge_carts BIGINT,     "
                    + "rain_surge_carts BIGINT,     "
                    + "total_carts BIGINT) WITH (    "
                    + "'connector' = 'kafka',   "
                    + "'topic' = '%s',    "
                    + "'properties.bootstrap.servers' = '%s',    "
                    + "'format' = 'json'    )";
}