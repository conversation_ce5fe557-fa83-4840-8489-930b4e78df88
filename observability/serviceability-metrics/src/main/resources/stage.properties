SERVICEABILITY_SOURCE_BROKERS = vpce-06233f70360f757f8-enprgsf3.vpce-svc-0dcaa5761ef958410.ap-southeast-1.vpce.amazonaws.com:9093,vpce-06233f70360f757f8-enprgsf3.vpce-svc-0dcaa5761ef958410.ap-southeast-1.vpce.amazonaws.com:9094,vpce-06233f70360f757f8-enprgsf3.vpce-svc-0dcaa5761ef958410.ap-southeast-1.vpce.amazonaws.com:9095
SERVICEABILITY_SOURCE_CONSUMER_GROUP_ID = test.flink-serviceability-metrics.v2
SERVICEABILITY_SOURCE_TOPIC = blinkit.serviceability_info_events
SINK_BROKERS = b-2.testdatacommon.vkkz3t.c5.kafka.ap-southeast-1.amazonaws.com:9092,b-1.testdatacommon.vkkz3t.c5.kafka.ap-southeast-1.amazonaws.com:9092
SURGE_METRICS_SINK_TOPIC = testing.surge.metrics
SURGE_METRICS_DAILY_SINK_TOPIC = testing.surge.metrics.daily
SURGE_METRICS_HOURLY_SINK_TOPIC = testing.surge.metrics.hourly
SURGE_METRICS_CITY_HOURLY_SINK_TOPIC = testing.surge.metrics.city.hourly
SURGE_METRICS_CITY_DAILY_SINK_TOPIC = testing.surge.metrics.city.daily
STATE_BACKEND_LOCATION = s3a://grofers-test-dse-singapore/flink-streams/observability/serviceability-metrics/checkpoints/