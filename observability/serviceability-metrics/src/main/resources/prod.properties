SERVICEABILITY_SOURCE_BROKERS = vpce-06233f70360f757f8-enprgsf3.vpce-svc-0dcaa5761ef958410.ap-southeast-1.vpce.amazonaws.com:9093,vpce-06233f70360f757f8-enprgsf3.vpce-svc-0dcaa5761ef958410.ap-southeast-1.vpce.amazonaws.com:9094,vpce-06233f70360f757f8-enprgsf3.vpce-svc-0dcaa5761ef958410.ap-southeast-1.vpce.amazonaws.com:9095
SERVICEABILITY_SOURCE_CONSUMER_GROUP_ID = flink-observability-eta-surge-metrics-consumer_group-v1
SERVICEABILITY_SOURCE_TOPIC = blinkit.serviceability_info_events
SINK_BROKERS = b-1.prod-data-corestr.uynfnv.c4.kafka.ap-southeast-1.amazonaws.com:9092,b-2.prod-data-corestr.uynfnv.c4.kafka.ap-southeast-1.amazonaws.com:9092,b-3.prod-data-corestr.uynfnv.c4.kafka.ap-southeast-1.amazonaws.com:9092
SURGE_METRICS_SINK_TOPIC = observability.serviceabilitymetrics.surge
SURGE_METRICS_HOURLY_SINK_TOPIC = observability.serviceabilitymetrics.surge.hourly
SURGE_METRICS_DAILY_SINK_TOPIC = observability.serviceabilitymetrics.surge.daily
SURGE_METRICS_CITY_HOURLY_SINK_TOPIC = observability.serviceabilitymetrics.surge.city.hourly
SURGE_METRICS_CITY_DAILY_SINK_TOPIC = observability.serviceabilitymetrics.surge.city.daily
STATE_BACKEND_LOCATION = s3a://prod-data-flink-states/flink-streams/observability/serviceability-metrics/checkpoints/