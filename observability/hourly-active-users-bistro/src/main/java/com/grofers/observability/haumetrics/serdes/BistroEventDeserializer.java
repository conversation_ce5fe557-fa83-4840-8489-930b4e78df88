/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.grofers.observability.haumetrics.serdes;

import com.grofers.observability.haumetrics.events.BistroEvent;
import java.io.IOException;
import org.apache.flink.api.common.serialization.DeserializationSchema;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class BistroEventDeserializer implements DeserializationSchema<BistroEvent> {
    private static final long serialVersionUID = 1L;
    private static final Logger LOG = LoggerFactory.getLogger(BistroEventDeserializer.class);
    private static final ObjectMapper objectMapper = new ObjectMapper();

    @Override
    public BistroEvent deserialize(byte[] bytes) throws IOException {
        try {
            return new BistroEvent(
                    objectMapper.readValue(bytes, BistroEvent.class));
        } catch (Exception exception) {
            exception.printStackTrace();
            LOG.info("Failed to deserialize rudder message: {}", new String(bytes));
            return null;
        }
    }

    @Override
    public boolean isEndOfStream(BistroEvent bistroEvent) {
        return false;
    }

    @Override
    public TypeInformation<BistroEvent> getProducedType() {
        return TypeInformation.of(BistroEvent.class);
    }
}
