# Hourly Active Users

This stream maintains a Hourly Window to calculate the Unique Daily active users at a City & Merchant level. We use an evictor here to make sure we only maintain a state of distinct users

We have a city and merchant level HAU table created out of our stream on Pinot: `city_hourly_active_users` & `merchant_hourly_active_users` which helps power the `ACTIVE USERS - HOURLY TRENDS` metric you see on [Sonar](https://sonar.grofer.io/)
