name: Fact Order Item Details Bistro
image: 442534439095.dkr.ecr.ap-southeast-1.amazonaws.com/data/flink-streams/observability/fact-order-item-details-bistro
flink_config:
  taskmanager.memory.process.size: 1024m
  jobmanager.memory.process.size: 1024m
  taskmanager.memory.managed.size: 0
  taskmanager.numberOfTaskSlots: 1
  parallelism.default: 1
  process.working-dir: fact-order-item-details-bistro/process
  state.backend.local-recovery: true
  taskmanager.resource-id: TaskManager_factorderitemdetailsbistro
deployment_config:
  kubernetes.rest-service.exposed.type: NodePort
  metrics.reporters: prom
  metrics.reporter.prom.factory.class: org.apache.flink.metrics.prometheus.PrometheusReporterFactory
allow_non_restored_state: False # Set to True when changes are made to Operator
codeartifact_access: True
