package com.grofers.observability.factorderitemdetailsbistro.configs;

public class JobConfig {
    public static String ORDER_LIFECYCLE_EVENTS_SOURCE_BROKERS;
    public static String ORDER_LIFECYCLE_EVENTS_SOURCE_CONSUMER_GROUP_ID;
    public static String ORDER_LIFECYCLE_EVENTS_SOURCE_TOPIC;
    public static String STATE_BACKEND_LOCATION;
    public static String ORDER_ITEM_FACT_SINK_BROKERS;
    public static String ORDER_ITEM_FACT_SINK_TOPIC;

    public JobConfig(
            String ORDER_LIFECYCLE_EVENTS_SOURCE_BROKERS,
            String ORDER_LIFECYCLE_EVENTS_SOURCE_CONSUMER_GROUP_ID,
            String ORDER_LIFECYCLE_EVENTS_SOURCE_TOPIC,
            String STATE_BACKEND_LOCATION,
            String ORDER_ITEM_FACT_SINK_BROKERS,
            String ORDER_ITEM_FACT_SINK_TOPIC) {
        JobConfig.ORDER_LIFECYCLE_EVENTS_SOURCE_BROKERS = ORDER_LIFECYCLE_EVENTS_SOURCE_BROKERS;
        JobConfig.ORDER_LIFECYCLE_EVENTS_SOURCE_CONSUMER_GROUP_ID =
                ORDER_LIFECYCLE_EVENTS_SOURCE_CONSUMER_GROUP_ID;
        JobConfig.ORDER_LIFECYCLE_EVENTS_SOURCE_TOPIC = ORDER_LIFECYCLE_EVENTS_SOURCE_TOPIC;
        JobConfig.STATE_BACKEND_LOCATION = STATE_BACKEND_LOCATION;
        JobConfig.ORDER_ITEM_FACT_SINK_BROKERS = ORDER_ITEM_FACT_SINK_BROKERS;
        JobConfig.ORDER_ITEM_FACT_SINK_TOPIC = ORDER_ITEM_FACT_SINK_TOPIC;
    }
}
