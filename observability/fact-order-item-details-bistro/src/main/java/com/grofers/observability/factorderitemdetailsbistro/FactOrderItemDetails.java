/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.grofers.observability.factorderitemdetailsbistro;

import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.contrib.streaming.state.EmbeddedRocksDBStateBackend;
import org.apache.flink.streaming.api.CheckpointingMode;
import org.apache.flink.streaming.api.environment.CheckpointConfig;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.table.annotation.DataTypeHint;
import org.apache.flink.table.api.bridge.java.StreamTableEnvironment;
import org.apache.flink.table.functions.ScalarFunction;
import org.apache.flink.table.functions.TableFunction;
import org.apache.flink.types.Row;

import com.grofers.observability.factorderitemdetailsbistro.configs.JobConfigManager;

import java.io.IOException;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeFormatterBuilder;
import java.time.temporal.ChronoField;
import java.util.*;

import static com.grofers.observability.factorderitemdetailsbistro.Queries.*;
import static com.grofers.observability.factorderitemdetailsbistro.configs.JobConfig.*;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;

import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.api.common.typeinfo.Types;

/**
 * Order Metrics Job Bistro
 *
 * <p>For a tutorial how to write a Flink streaming application, check the tutorials and examples on
 * the <a href="https://flink.apache.org/docs/stable/">Flink Website</a>.
 *
 * <p>To package your application into a JAR file for execution, run 'mvn clean package' on the
 * command line.
 *
 * <p>If you change the name of the main class (with the public static void main(String[] args))
 * method, change the respective entry in the POM.xml file (simply search for 'mainClass').
 */
public class FactOrderItemDetails {
    public static class GetFillRateFunction extends ScalarFunction {
        private static final List<String> PNA_CANCELLATION_REASONS =
                new ArrayList<String>() {
                    {
                        add("ITEM_CANCEL_PROCUREMENT_UPDATE_MISSING");
                    }
                };

        public Double eval(
                @DataTypeHint(
                                "ARRAY<ROW<" +
                                "`procured_quantity` INT, " +
                                "`quantity` INT, " +
                                "`cancellations` ARRAY<ROW<`reason` STRING, `quantity` INT>>, " +
                                "`categories` ARRAY<STRING>, " +
                                "`product_id` INT, " +
                                "`name` STRING, " +
                                "`cost` DOUBLE" +
                                ">>")
                        Row[] arr,
                boolean includePNAItems) {

            if (arr != null) {
                int quantity = 0, procured_quantity = 0;
                double sm_fill_rate = 100.0;

                for (Row i : arr) {
                    // Aggregate quantity and procured_quantity
                    if (i.getField("quantity") != null && i.getField("procured_quantity") != null) {
                        quantity += (int) i.getField("quantity");
                        procured_quantity += (int) i.getField("procured_quantity");
                    }

                    // Aggregate cancelled quantity for each reason
                    Row[] cancellations = i.getFieldAs("cancellations");
                    if (cancellations != null) {
                        for (Row cancellation : cancellations) {
                            String cancellationReason = cancellation.getFieldAs("reason");
                            Integer cancelledQuantity = cancellation.getFieldAs("quantity");
                            if (cancelledQuantity != null
                                    && PNA_CANCELLATION_REASONS.contains(cancellationReason)) {
                                sm_fill_rate = 0.0;
                                break;
                                // if even a single item is marked as PNA(Product not available)
                                // then make SM fill rate as 0 and don't check further cancellation
                                // reasons
                            }
                        }
                    }
                }
                if (quantity == 0) return null;

                double fill_rate = ((double) procured_quantity / (double) quantity) * 100;

                return includePNAItems ? fill_rate : sm_fill_rate;
            }
            return null;
        }
    }

    public static class GetTypeOfOrderFunction extends ScalarFunction {
        public String eval(
                @DataTypeHint("ARRAY< ROW <`id` INT, `type` VARCHAR(63) >>") Row[] arr,
                Integer key) {
            if (arr != null) {
                for (Row i : arr) {
                    if ((int) i.getFieldAs("id") == key) {
                        return i.getFieldAs("type").toString();
                    }
                }
            }
            return null;
        }
    }

    public static class GetTimeStampFromOrderEventsFunction extends ScalarFunction {
        public Long eval(
                @DataTypeHint(
                                "ARRAY< ROW< `event_state_change_to` STRING, `event_type` STRING,"
                                        + " `timestamp` STRING, `extra` ROW< `meta` ROW<"
                                        + " `batched_order_ids` ARRAY<STRING>, `picking_end_time` STRING >, `partner_emp_id`"
                                        + " STRING, `picker_imei` STRING, `picker_name` STRING,"
                                        + " `timestamp` STRING, `assignment_queued_ts` STRING >>>")
                        Row[] arr,
                String eventValue) {
            if (arr != null) {
                String timestamp = null;
                for (Row i : arr) {
                    if (i.getFieldAs("event_type").equals(eventValue)
                            || (i.getFieldAs("event_type").equals("state_change")
                                    && i.getFieldAs("event_state_change_to").equals(eventValue))) {
                        if (timestamp == null
                                || i.getFieldAs("timestamp").toString().compareTo(timestamp)
                                        > 0) {
                            timestamp = i.getFieldAs("timestamp").toString();
                        }
                    }
                }
                return getEpochFromTimestamp(timestamp);
            }
            return null;
        }
    }

    public static class GetIfBatchedOrderFromOrderEventsFunction extends ScalarFunction {
        public boolean eval(
                @DataTypeHint(
                                "ARRAY< ROW< `event_state_change_to` STRING, `event_type` STRING,"
                                        + " `timestamp` STRING, `extra` ROW< `meta` ROW<"
                                        + " `batched_order_ids` ARRAY<STRING>, `picking_end_time` STRING >, `partner_emp_id`"
                                        + " STRING, `picker_imei` STRING, `picker_name` STRING,"
                                        + " `timestamp` STRING, `assignment_queued_ts` STRING >>>")
                        Row[] arr) {
            if (arr != null) {
                for (Row row : arr) {
                    if (row != null) {
                        if (row.getField("event_state_change_to") != null
                                && row.getField("event_state_change_to")
                                        .toString()
                                        .equals("ENROUTE")
                                && row.getField("extra") != null) {
                            Row extra = (Row) row.getField("extra");
                            if (extra != null && extra.getField("meta") != null) {
                                Row meta = (Row) extra.getField("meta");
                                if (meta != null && meta.getField("batched_order_ids") != null) {
                                    List<String> batchedOrderIds =
                                            Arrays.asList(
                                                    (String[]) meta.getField("batched_order_ids"));
                                    if (batchedOrderIds != null && batchedOrderIds.size() > 0) {
                                        return true;
                                    }
                                }
                            }
                        }
                    }
                }
            }
            return false;
        }
    }

    public static class GetIfTagExistsOrderFunction extends ScalarFunction {
        public boolean eval(
                @DataTypeHint(
                                "ROW < `tags` ARRAY<STRING>, `slot_charge` DOUBLE,"
                                    + " `checkout_properties` ROW < `slot_charge` DOUBLE >,"
                                    + " `serviceability` ROW <`eta` INT, `serviceability_reason`"
                                    + " STRING, `surge_charge_v2` ROW < `source` STRING,"
                                    + " `surge_amount` DOUBLE >, `session_id` STRING, `components`"
                                    + " ARRAY< ROW<`name` STRING, `duration` DOUBLE> > >>")
                        Row row,
                String tag) {
            if (row != null) {
                if (row.getField("tags") != null) {
                    List<String> tags = Arrays.asList((String[]) row.getField("tags"));
                    if (tags != null && tags.contains(tag)) {
                        return true;
                    }
                }
            }
            return false;
        }
    }

    public static class GetTaskDuration extends ScalarFunction {
        public Double eval(
                @DataTypeHint(
                                "ROW <`eta` INT, `serviceability_reason` STRING, `surge_charge_v2`"
                                        + " ROW < `source` STRING, `surge_amount` DOUBLE >,"
                                        + " `session_id` STRING, `components` ARRAY< ROW<`name`"
                                        + " STRING, `duration` DOUBLE> >>")
                        Row row,
                List<String> componentNames) {

            if (row != null && row.getField("components") != null) {
                Row[] components = (Row[]) row.getField("components");
                double sumDuration = 0.0;

                for (Row component : components) {
                    String name = (String) component.getField("name");
                    if (name != null && componentNames.contains(name)) {
                        Double duration = (Double) component.getField("duration");
                        if (duration != null) {
                            sumDuration += duration;
                        }
                    }
                }
                return sumDuration;
            }
            return null;
        }
    }

    public static class GetPartnerEmpIdFromOrderEventsFunction extends ScalarFunction {
        public String eval(
                @DataTypeHint(
                                "ARRAY< ROW< `event_state_change_to` STRING, `event_type` STRING,"
                                        + " `timestamp` STRING, `extra` ROW< `meta` ROW<"
                                        + " `batched_order_ids` ARRAY<STRING>, `picking_end_time` STRING >, `partner_emp_id`"
                                        + " STRING, `picker_imei` STRING, `picker_name` STRING,"
                                        + " `timestamp` STRING, `assignment_queued_ts` STRING >>>")
                        Row[] arr) {
            if (arr != null) {
                String partnerEmpId = null, timestamp = null;
                for (Row row : arr) {
                    if (row != null) {
                        if (row.getField("event_type")
                                        .toString()
                                        .equals("delivery_partner_reassigned")
                                || row.getField("event_type")
                                        .toString()
                                        .equals("delivery_partner_assigned")) {
                            if (timestamp == null
                                    || row.getFieldAs("timestamp").toString().compareTo(timestamp)
                                            > 0) {
                                timestamp = row.getFieldAs("timestamp").toString();
                                Row extra = (Row) row.getField("extra");
                                if (extra != null && extra.getField("partner_emp_id") != null) {
                                    partnerEmpId = extra.getField("partner_emp_id").toString();
                                }
                            }
                        }
                    }
                }
                return partnerEmpId;
            }
            return null;
        }
    }

    public static class GetPickerDetailsFromOrderEventsFunction extends ScalarFunction {
        public String eval(
                @DataTypeHint(
                                "ARRAY< ROW< `event_state_change_to` STRING, `event_type` STRING,"
                                        + " `timestamp` STRING, `extra` ROW< `meta` ROW<"
                                        + " `batched_order_ids` ARRAY<STRING>, `picking_end_time` STRING >, `partner_emp_id`"
                                        + " STRING, `picker_imei` STRING, `picker_name` STRING,"
                                        + " `timestamp` STRING, `assignment_queued_ts` STRING >>>")
                        Row[] arr,
                String pickerField, String event_type, boolean is_timestamp) {
            if (arr != null) {
                String pickerFieldValue = null, timestamp = null;
                for (Row row : arr) {
                    if (row != null) {
                        Object eventTypeObj = row.getField("event_type");
                        Object stateChangeObj = row.getField("event_state_change_to");
                        
                        // Safe null checks before toString() calls
                        String rowEventType = eventTypeObj != null ? eventTypeObj.toString() : null;
                        String stateChange = stateChangeObj != null ? stateChangeObj.toString() : null;
                        
                        if ((rowEventType != null && event_type.equals(rowEventType)) || 
                            ("state_change".equals(rowEventType) && stateChange != null && event_type.equals(stateChange))) {
                            
                            Object timestampObj = row.getField("timestamp");
                            String rowTimestamp = timestampObj != null ? timestampObj.toString() : null;
                            
                            if (rowTimestamp != null && (timestamp == null || rowTimestamp.compareTo(timestamp) > 0)) {
                                timestamp = rowTimestamp;
                                Row extra = (Row) row.getField("extra");
                                if (extra != null) {
                                    try {
                                        Object field = extra.getField(pickerField);
                                        if (field != null) {
                                            pickerFieldValue = field.toString();
                                        }
                                    }
                                    catch(IllegalArgumentException e) {
                                        Row meta = (Row) extra.getField("meta");
                                        if (meta != null && meta.getField(pickerField) != null) {
                                            Object fieldValue = meta.getField(pickerField);
                                            if (fieldValue != null) {
                                                pickerFieldValue = fieldValue.toString();
                                            }
                                        }
                                    }
                                    timestamp = is_timestamp && pickerFieldValue != null ? pickerFieldValue : timestamp;
                                }
                            }
                        }
                    }
                }
                return (is_timestamp && pickerFieldValue != null) ? getEpochFromTimestamp(pickerFieldValue).toString() : pickerFieldValue;
            }
            return null;
        }
    }

    public static class ExtractAdditionalChargeAmount extends ScalarFunction {
        public double eval(
                @DataTypeHint(
                                "ARRAY<ROW< `id` INT, `name` STRING, `amount` DOUBLE, `charge_id`"
                                        + " INT >>")
                        Row[] arr,
                int chargeId) {

            if (arr != null) {
                for (Row row : arr) {
                    if (row != null) {
                        if (row.getField("charge_id") != null
                                && (int) row.getField("charge_id") == chargeId
                                && row.getField("amount") != null) {
                            return (Double) row.getField("amount");
                        }
                    }
                }
            }
            return 0.0;
        }
    }

    private static Long getEpochFromTimestamp(String utcTimestamp) {
        if (utcTimestamp == null) return null;
        // Check if the utcTimestamp is in the form of an epoch
        try {
            long epochTimestamp = Long.parseLong(utcTimestamp);
            // If the parsing succeeds, it means the timestamp is already in epoch format
            return epochTimestamp;
        } catch (NumberFormatException e) {
            // If parsing fails, it means the timestamp is in string format
            // Define two formats to handle both cases
            DateTimeFormatter formatter =
                    new DateTimeFormatterBuilder()
                            .appendPattern("yyyy-MM-dd'T'HH:mm:ss")
                            .optionalStart() // Optional start for milliseconds
                            .appendFraction(ChronoField.MICRO_OF_SECOND, 0, 6, true)
                            .optionalEnd() // Optional end for milliseconds
                            .optionalStart() // Optional start for the timezone offset
                            .appendOffset("+HHMM", "Z") // Offset and 'Z' are optional
                            .optionalEnd() // Optional end for the timezone offset
                            .toFormatter();

            ZonedDateTime zonedDateTimeDate;
            try {
                // First try to parse with the formatter without milliseconds and 'Z'
                zonedDateTimeDate =
                        LocalDateTime.parse(utcTimestamp, formatter).atZone(ZoneId.of("UTC"));
                return zonedDateTimeDate.toInstant().toEpochMilli();
            } catch (java.time.format.DateTimeParseException ex) {
                return null;
            }
        }
    }

    public static class ExplodeOrderItemsFunction extends TableFunction<Row> {
        @DataTypeHint("ROW<name STRING, product_id INT, cost DOUBLE, quantity INT>")
        public void eval(
                @DataTypeHint(
                    "ARRAY<ROW<" +
                    "`procured_quantity` INT, " +
                    "`quantity` INT, " +
                    "`cancellations` ARRAY<ROW<`reason` STRING, `quantity` INT>>, " +
                    "`categories` ARRAY<STRING>, " +
                    "`product_id` INT, " +
                    "`name` STRING, " +
                    "`cost` DOUBLE" +
                    ">>")
                Row[] items) {
            if (items == null) {
                collect(Row.of(null, null));
                return;
            }

            for (Row item : items) {
                if (item == null) continue;
                
                collect(Row.of(
                    item.getFieldAs("name"),       // String
                    item.getFieldAs("product_id"), // Integer
                    item.getFieldAs("cost"),        // Double
                    item.getFieldAs("quantity")      // Integer
                ));
            }
        }
    }

    public static void main(String[] args)
            throws JobConfigManager.InvalidEnvironmentException, IOException {
        final StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment();
        final StreamTableEnvironment tEnv = StreamTableEnvironment.create(env);
        tEnv.createTemporarySystemFunction("GetTypeOfOrderFunction", GetTypeOfOrderFunction.class);
        tEnv.createTemporarySystemFunction(
                "GetTimeStampFromOrderEventsFunction", GetTimeStampFromOrderEventsFunction.class);
        tEnv.createTemporarySystemFunction(
                "GetIfBatchedOrderFromOrderEventsFunction",
                GetIfBatchedOrderFromOrderEventsFunction.class);
        tEnv.createTemporarySystemFunction(
                "GetPartnerEmpIdFromOrderEventsFunction",
                GetPartnerEmpIdFromOrderEventsFunction.class);
        tEnv.createTemporarySystemFunction(
                "GetPickerDetailsFromOrderEventsFunction",
                GetPickerDetailsFromOrderEventsFunction.class);
        tEnv.createTemporarySystemFunction("GetFillRateFunction", GetFillRateFunction.class);
        tEnv.createTemporarySystemFunction(
                "GetIfTagExistsOrderFunction", GetIfTagExistsOrderFunction.class);
        tEnv.createTemporarySystemFunction(
                "ExtractAdditionalChargeAmount", ExtractAdditionalChargeAmount.class);
        tEnv.createTemporarySystemFunction("GetTaskDuration", GetTaskDuration.class);
        tEnv.createTemporarySystemFunction("ExplodeOrderItemsFunction", ExplodeOrderItemsFunction.class);
        Configuration configuration = tEnv.getConfig().getConfiguration();
        configuration.setString("table.exec.source.idle-timeout", "1200 s");

        // local-global aggregation depends on mini-batch is enabled
        configuration.setString("table.exec.mini-batch.enabled", "true");
        configuration.setString("table.exec.mini-batch.allow-latency", "1 s");
        configuration.setString("table.exec.mini-batch.size", "500");

        ParameterTool params = ParameterTool.fromArgs(args);
        String userEnv = params.getRequired("environment");

        // Get job configs
        JobConfigManager.setJobConfigsPath(userEnv);
        String jobPropertiesPath = JobConfigManager.getJobConfigsPath(userEnv);
        JobConfigManager.setJobConfigs(tEnv, jobPropertiesPath);
        JobConfigManager.getJobConfigs(tEnv);

        // Checkpoint Configs
        env.enableCheckpointing(600000);
        // set mode to exactly-once (default)
        env.getCheckpointConfig().setCheckpointingMode(CheckpointingMode.EXACTLY_ONCE);
        // make sure 900000 ms of progress happen between checkpoints
        env.getCheckpointConfig().setMinPauseBetweenCheckpoints(300000);
        // checkpoints have to complete within this minute, or are discarded
        env.getCheckpointConfig().setCheckpointTimeout(600000);
        // only two consecutive checkpoint failures are tolerated
        env.getCheckpointConfig().setTolerableCheckpointFailureNumber(2);
        // allow only one checkpoint to be in progress at the same time
        env.getCheckpointConfig().setMaxConcurrentCheckpoints(1);
        // enable externalized checkpoints which are retained after job cancellation
        env.getCheckpointConfig()
                .setExternalizedCheckpointCleanup(
                        CheckpointConfig.ExternalizedCheckpointCleanup.DELETE_ON_CANCELLATION);
        // enable unaligned checkpoints
        env.getCheckpointConfig().enableUnalignedCheckpoints();
        env.getCheckpointConfig().setCheckpointStorage(STATE_BACKEND_LOCATION);


        String omsOrderCreateTable =
                String.format(
                        BISTRO_ORDER_LIFECYCLE_EVENTS,
                        ORDER_LIFECYCLE_EVENTS_SOURCE_TOPIC,
                        ORDER_LIFECYCLE_EVENTS_SOURCE_BROKERS,
                        ORDER_LIFECYCLE_EVENTS_SOURCE_CONSUMER_GROUP_ID);
        tEnv.executeSql(omsOrderCreateTable);


        String orderItemMetricsCreateTable =
                String.format(
                        ORDER_ITEM_FACT_CREATE_TABLE, ORDER_ITEM_FACT_SINK_TOPIC, ORDER_ITEM_FACT_SINK_BROKERS);
        tEnv.executeSql(orderItemMetricsCreateTable);
        tEnv.executeSql(ORDER_ITEM_FACT_METRIC);
    }
}
