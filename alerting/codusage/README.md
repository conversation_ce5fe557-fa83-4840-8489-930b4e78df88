# COD usage Alerting

This job is used to prevent Cash On Delivery payments beyond a particular limit since we want to limit these transactions to avoid abuse, We have a Daily window and track if a user has placed more than 3 COD order and it they have we send out the customerID of the user to profilestore which is then used by our consumer app to block the said customer from placing further COD orders

- Source: Order lifecycle events `blinkit.order.lifecycle-events`
- Sink: Profile store `profilestore.realtime.properties`
