package com.grofers.alerting.codusage;

import com.grofers.alerting.codusage.datatypes.DeviceCODCountAlert;
import com.grofers.alerting.codusage.datatypes.OrderLifecycleEvent;
import java.time.Instant;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.util.HashSet;
import java.util.Set;
import org.apache.flink.api.common.state.StateTtlConfig;
import org.apache.flink.api.common.state.ValueState;
import org.apache.flink.api.common.state.ValueStateDescriptor;
import org.apache.flink.api.common.time.Time;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.KeyedProcessFunction;
import org.apache.flink.util.Collector;

class DeviceCartIdsWithTimestamp {
    public String deviceId;
    public Set<Integer> cartIds;
    public Long lastModified;
}

public class CODDeviceOrderCountWindow
        extends KeyedProcessFunction<String, OrderLifecycleEvent, DeviceCODCountAlert> {

    private ValueState<DeviceCartIdsWithTimestamp> deviceCartsState;

    @Override
    public void open(Configuration parameters) throws Exception {
        StateTtlConfig stateTtlConfig =
                StateTtlConfig.newBuilder(Time.days(1))
                        .setStateVisibility(StateTtlConfig.StateVisibility.NeverReturnExpired)
                        .build();
        ValueStateDescriptor<DeviceCartIdsWithTimestamp> descriptor =
                new ValueStateDescriptor<>("deviceCarts", DeviceCartIdsWithTimestamp.class);
        descriptor.enableTimeToLive(stateTtlConfig);
        deviceCartsState = getRuntimeContext().getState(descriptor);
    }

    @Override
    public void processElement(
            OrderLifecycleEvent orderLifecycleEvent,
            KeyedProcessFunction<String, OrderLifecycleEvent, DeviceCODCountAlert>.Context context,
            Collector<DeviceCODCountAlert> collector)
            throws Exception {
        DeviceCartIdsWithTimestamp currentCartIds = deviceCartsState.value();
        if (currentCartIds == null) {
            currentCartIds = new DeviceCartIdsWithTimestamp();
            currentCartIds.deviceId = orderLifecycleEvent.getOrder().getDeviceId();
            currentCartIds.cartIds = new HashSet<>();
            currentCartIds.cartIds.add(orderLifecycleEvent.getOrder().getCartId());
        } else {
            currentCartIds.cartIds.add(orderLifecycleEvent.getOrder().getCartId());
        }

        currentCartIds.lastModified = context.timestamp();
        deviceCartsState.update(currentCartIds);
        Instant eod =
                Instant.ofEpochMilli(currentCartIds.lastModified)
                        .atZone(ZoneId.of("Asia/Kolkata"))
                        .plus(1, ChronoUnit.DAYS)
                        .truncatedTo(ChronoUnit.DAYS)
                        .toInstant();
        context.timerService().registerEventTimeTimer(eod.toEpochMilli());

        if (currentCartIds.cartIds.size() >= 4) {
            collector.collect(new DeviceCODCountAlert(currentCartIds.deviceId, eod.toEpochMilli()));
        }
    }

    @Override
    public void onTimer(
            long timestamp, OnTimerContext context, Collector<DeviceCODCountAlert> collector)
            throws Exception {
        DeviceCartIdsWithTimestamp timerState = deviceCartsState.value();
        if (timerState != null
                && timerState.lastModified != null
                && timerState.lastModified < timestamp) {
            deviceCartsState.clear();
        }
    }
}
