package com.grofers.alerting.codusage.datatypes;

import java.time.Instant;

public class DeviceCODCountAlert {
    private final String deviceId;
    private final Long expiry;

    public DeviceCODCountAlert(String deviceId, Long expiry) {
        this.deviceId = deviceId;
        this.expiry = expiry;
    }

    @Override
    public String toString() {

        return String.format(
                "{"
                        + "\"blinkID\":\"device:%s\","
                        + "\"context\":\"abuse_config\","
                        + "\"dataType\":\"MAP\","
                        + "\"ttl\":%s,"
                        + "\"contextProperties\":{"
                        + "\"payment_cod\":true}}",
                deviceId, Instant.ofEpochMilli(expiry).getEpochSecond());
    }
}
