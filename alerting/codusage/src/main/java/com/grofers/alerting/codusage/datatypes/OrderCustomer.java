package com.grofers.alerting.codusage.datatypes;

import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.annotation.JsonProperty;

@JsonIgnoreProperties(ignoreUnknown = true)
public class OrderCustomer {
    private final Long id;

    public OrderCustomer(@JsonProperty(value = "id", required = true) Long id) {
        this.id = id;
    }

    public Long getId() {
        return id;
    }
}
