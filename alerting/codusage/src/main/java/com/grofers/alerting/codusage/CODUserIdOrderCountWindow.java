package com.grofers.alerting.codusage;

import com.grofers.alerting.codusage.datatypes.OrderLifecycleEvent;
import com.grofers.alerting.codusage.datatypes.UserCODCountAlert;
import java.time.Instant;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.util.HashSet;
import java.util.Set;
import org.apache.flink.api.common.state.StateTtlConfig;
import org.apache.flink.api.common.state.ValueState;
import org.apache.flink.api.common.state.ValueStateDescriptor;
import org.apache.flink.api.common.time.Time;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.KeyedProcessFunction;
import org.apache.flink.util.Collector;

/** The data type stored in the state */
class CartIdsWithTimestamp {
    public Long customerId;
    public Set<Integer> cartIds;
    public Long lastModified;
}

public class CODUserIdOrderCountWindow
        extends KeyedProcessFunction<Long, OrderLifecycleEvent, UserCODCountAlert> {

    private ValueState<CartIdsWithTimestamp> userCartsState;

    @Override
    public void open(Configuration parameters) throws Exception {
        StateTtlConfig stateTtlConfig =
                StateTtlConfig.newBuilder(Time.days(1))
                        .setStateVisibility(StateTtlConfig.StateVisibility.NeverReturnExpired)
                        .build();
        ValueStateDescriptor<CartIdsWithTimestamp> descriptor =
                new ValueStateDescriptor<>("userCarts", CartIdsWithTimestamp.class);
        descriptor.enableTimeToLive(stateTtlConfig);
        userCartsState = getRuntimeContext().getState(descriptor);
    }

    @Override
    public void processElement(
            OrderLifecycleEvent orderLifecycleEvent,
            KeyedProcessFunction<Long, OrderLifecycleEvent, UserCODCountAlert>.Context context,
            Collector<UserCODCountAlert> collector)
            throws Exception {
        CartIdsWithTimestamp currentCartIds = userCartsState.value();
        if (currentCartIds == null) {
            currentCartIds = new CartIdsWithTimestamp();
            currentCartIds.customerId = orderLifecycleEvent.getOrder().getCustomer().getId();
            currentCartIds.cartIds = new HashSet<>();
            currentCartIds.cartIds.add(orderLifecycleEvent.getOrder().getCartId());
        } else {
            currentCartIds.cartIds.add(orderLifecycleEvent.getOrder().getCartId());
        }

        currentCartIds.lastModified = context.timestamp();
        userCartsState.update(currentCartIds);
        Instant eod =
                Instant.ofEpochMilli(currentCartIds.lastModified)
                        .atZone(ZoneId.of("Asia/Kolkata"))
                        .plus(1, ChronoUnit.DAYS)
                        .truncatedTo(ChronoUnit.DAYS)
                        .toInstant();
        context.timerService().registerEventTimeTimer(eod.toEpochMilli());

        if (currentCartIds.cartIds.size() >= 4) {
            collector.collect(new UserCODCountAlert(currentCartIds.customerId, eod.toEpochMilli()));
        }
    }

    @Override
    public void onTimer(
            long timestamp, OnTimerContext context, Collector<UserCODCountAlert> collector)
            throws Exception {
        CartIdsWithTimestamp timerState = userCartsState.value();
        if (timerState != null
                && timerState.lastModified != null
                && timerState.lastModified < timestamp) {
            userCartsState.clear();
        }
    }
}
