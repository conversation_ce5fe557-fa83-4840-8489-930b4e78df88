/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.grofers.alerting.codusage;

import static java.time.Duration.ofSeconds;

import com.grofers.alerting.codusage.configs.JobConfig;
import com.grofers.alerting.codusage.configs.JobConfigManager;
import com.grofers.alerting.codusage.datatypes.DeviceCODCountAlert;
import com.grofers.alerting.codusage.datatypes.OrderLifecycleEvent;
import com.grofers.alerting.codusage.datatypes.UserCODCountAlert;
import com.grofers.alerting.codusage.serdes.DeviceAlertsSerializationSchema;
import com.grofers.alerting.codusage.serdes.JsonDeserializationSchema;
import com.grofers.alerting.codusage.serdes.UserAlertsSerializationSchema;
import org.apache.flink.api.common.ExecutionConfig;
import org.apache.flink.api.common.eventtime.WatermarkStrategy;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.connector.base.DeliveryGuarantee;
import org.apache.flink.connector.kafka.sink.KafkaSink;
import org.apache.flink.connector.kafka.source.KafkaSource;
import org.apache.flink.connector.kafka.source.enumerator.initializer.OffsetsInitializer;
import org.apache.flink.streaming.api.CheckpointingMode;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.datastream.DataStreamSource;
import org.apache.flink.streaming.api.environment.CheckpointConfig;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.kafka.clients.consumer.OffsetResetStrategy;

import java.util.Properties;

/** Flink Streaming Job for calculating COD usage */
public class CODUsageJob {
    private final DataStreamSource<OrderLifecycleEvent> orderLifecycleEventDataStreamSource;
    private final KafkaSink<UserCODCountAlert> codAlertsSink;
    private final KafkaSink<DeviceCODCountAlert> deviceAlertsSink;
    static final String COD_MODE_FILTER = "COD";
    static final String EVENT_FILTER = "approve_order";

    public CODUsageJob(
            DataStreamSource<OrderLifecycleEvent> orderLifecycleEventDataStreamSource,
            KafkaSink<UserCODCountAlert> codAlertsSink,
            KafkaSink<DeviceCODCountAlert> deviceAlertsSink) {
        this.orderLifecycleEventDataStreamSource = orderLifecycleEventDataStreamSource;
        this.codAlertsSink = codAlertsSink;
        this.deviceAlertsSink = deviceAlertsSink;
    }

    public static void main(String[] args) throws Exception {
        // Sets up the execution environment, which is the main entry point
        // to building Flink applications.
        final StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment();

        // Parameters for environment
        ParameterTool params = ParameterTool.fromArgs(args);
        String userEnv = params.getRequired("environment");

        // Set PropertiesPath for userEnv
        JobConfigManager.setJobConfigsPath(userEnv);
        // Get PropertiesPath for userEnv
        String jobPropertiesPath = JobConfigManager.getJobConfigsPath(userEnv);
        // Fetch job config
        JobConfigManager.setJobConfigs(env, jobPropertiesPath);
        JobConfigManager.getJobConfigs(env);

        // Checkpoint Configs
        // start a checkpoint every 240000 ms
        env.enableCheckpointing(240000);
        // set mode to exactly-once (default)
        env.getCheckpointConfig().setCheckpointingMode(CheckpointingMode.EXACTLY_ONCE);
        // make sure 120000 ms of progress happen between checkpoints
        env.getCheckpointConfig().setMinPauseBetweenCheckpoints(240000);
        // checkpoints have to complete within a minute, or are discarded
        env.getCheckpointConfig().setCheckpointTimeout(210000);
        // only two consecutive checkpoint failures are tolerated
        env.getCheckpointConfig().setTolerableCheckpointFailureNumber(2);
        // allow only one checkpoint to be in progress at the same time
        env.getCheckpointConfig().setMaxConcurrentCheckpoints(1);
        // enable externalized checkpoints which are retained after job cancellation
        env.getCheckpointConfig()
                .setExternalizedCheckpointCleanup(
                        CheckpointConfig.ExternalizedCheckpointCleanup.RETAIN_ON_CANCELLATION);
        // enables the experimental unaligned checkpoints
        env.getCheckpointConfig().enableUnalignedCheckpoints();
        // sets the checkpoint storage where checkpoint snapshots will be written
        env.getCheckpointConfig().setCheckpointStorage(JobConfig.CHECKPOINTS_STORAGE_LOCATION);
        // Enable checkpoint compression
        ExecutionConfig executionConfig = new ExecutionConfig();
        executionConfig.setUseSnapshotCompression(true);

        // Determine the environment (prod or stage) and set properties accordingly
        String envPrefix = userEnv.equalsIgnoreCase("prod") ? "PROD" : "STAGE";
        String bootstrapServerUrl = System.getenv().getOrDefault("SINK_BOOTSTRAP_SERVER_URL_" + envPrefix, "localhost:9092");
        String bootstrapServerUsername = System.getenv().getOrDefault("SINK_BOOTSTRAP_SERVER_USERNAME_" + envPrefix, "username");
        String bootstrapServerPassword = System.getenv().getOrDefault("SINK_BOOTSTRAP_SERVER_PASSWORD_" + envPrefix, "password");

        Properties kafkaSinkProperties = new Properties();
        kafkaSinkProperties.setProperty("bootstrap.servers", bootstrapServerUrl);
        kafkaSinkProperties.setProperty("security.protocol", "SASL_SSL");
        kafkaSinkProperties.setProperty("sasl.mechanism", "SCRAM-SHA-512");
        kafkaSinkProperties.setProperty("sasl.jaas.config",
                "org.apache.kafka.common.security.scram.ScramLoginModule required " +
                        "username=\"" + bootstrapServerUsername + "\" " +
                        "password=\"" + bootstrapServerPassword + "\";");


        env.setMaxParallelism(8);

        KafkaSource<OrderLifecycleEvent> orderLifecycleEventKafkaSource =
                KafkaSource.<OrderLifecycleEvent>builder()
                        .setBootstrapServers(JobConfig.ORDER_LIFECYCLE_EVENTS_SOURCE_BROKERS)
                        .setGroupId(JobConfig.ORDER_LIFECYCLE_EVENTS_CONSUMER_GROUP_ID)
                        .setValueOnlyDeserializer(new JsonDeserializationSchema())
                        .setStartingOffsets(
                                OffsetsInitializer.committedOffsets())
                        .setTopics(JobConfig.ORDER_LIFECYCLE_EVENTS_SOURCE_TOPIC)
                        .setProperty("partition.discovery.interval.ms", "600000")
                        .setProperty("reconnect.backoff.max.ms", "300000")
                        .setProperty("reconnect.backoff.ms", "60000")
                        .build();

        WatermarkStrategy<OrderLifecycleEvent> orderLifecycleEventWatermarkStrategy =
                WatermarkStrategy.<OrderLifecycleEvent>forBoundedOutOfOrderness(ofSeconds(600))
                        .withIdleness(ofSeconds(JobConfig.IDLENESS_TIME_IN_SECS))
                        .withTimestampAssigner(
                                (orderLifecycleEvent, time) ->
                                        orderLifecycleEvent.getEventEpochTimeInMillis());

        DataStreamSource<OrderLifecycleEvent> orderLifecycleEventDataStreamSource =
                env.fromSource(
                        orderLifecycleEventKafkaSource,
                        orderLifecycleEventWatermarkStrategy,
                        "order-lifecycle-events-kafka-source",
                        TypeInformation.of(OrderLifecycleEvent.class));

        KafkaSink<UserCODCountAlert> codAlertsSink =
                KafkaSink.<UserCODCountAlert>builder()
                        .setBootstrapServers(bootstrapServerUrl)
                        .setDeliverGuarantee(DeliveryGuarantee.AT_LEAST_ONCE)
                        .setRecordSerializer(
                                new UserAlertsSerializationSchema(JobConfig.ALERTS_SINK_TOPIC))
                        .setKafkaProducerConfig(kafkaSinkProperties)
                        .build();

        KafkaSink<DeviceCODCountAlert> deviceAlertsSink =
                KafkaSink.<DeviceCODCountAlert>builder()
                        .setBootstrapServers(bootstrapServerUrl)
                        .setDeliverGuarantee(DeliveryGuarantee.AT_LEAST_ONCE)
                        .setRecordSerializer(
                                new DeviceAlertsSerializationSchema(JobConfig.ALERTS_SINK_TOPIC))
                        .setKafkaProducerConfig(kafkaSinkProperties)
                        .build();

        CODUsageJob codUsageJob =
                new CODUsageJob(
                        orderLifecycleEventDataStreamSource, codAlertsSink, deviceAlertsSink);

        codUsageJob.execute(env);
    }

    private void execute(StreamExecutionEnvironment env) throws Exception {
        DataStream<OrderLifecycleEvent> filteredOrderApprovedStream =
                orderLifecycleEventDataStreamSource
                        .name("order-lifecycle-events-source")
                        .uid("order-lifecycle-events-source")
                        .setParallelism(2)
                        .filter(
                                orderLifecycleEvent ->
                                        orderLifecycleEvent.getMeta() != null
                                                && orderLifecycleEvent.getOrder() != null
                                                && orderLifecycleEvent
                                                        .getOrder()
                                                        .getPayment()
                                                        .getMode()
                                                        .equalsIgnoreCase(COD_MODE_FILTER)
                                                && orderLifecycleEvent
                                                        .getMeta()
                                                        .getEventName()
                                                        .equalsIgnoreCase(EVENT_FILTER))
                        .name("filter-cod-orders")
                        .uid("filter-cod-orders");

        filteredOrderApprovedStream
                .filter(orderLifecycleEvent -> orderLifecycleEvent.getOrder().getDeviceId() != null)
                .name("filter-null-device-ids")
                .uid("filter-null-device-ids")
                .keyBy(orderLifecycleEvent -> orderLifecycleEvent.getOrder().getDeviceId())
                .process(new CODDeviceOrderCountWindow())
                .name("process-device-cod-orders")
                .uid("process-device-cod-orders")
                .sinkTo(deviceAlertsSink)
                .name("device-cod-alerts-sink")
                .uid("device-cod-alerts-sink");

        filteredOrderApprovedStream
                .keyBy(orderLifecycleEvent -> orderLifecycleEvent.getOrder().getCustomer().getId())
                .process(new CODUserIdOrderCountWindow())
                .name("process-cod-orders")
                .uid("process-cod-orders")
                .sinkTo(codAlertsSink)
                .name("cod-alerts-sink")
                .uid("cod-alerts-sink");

        env.execute("COD Usage Alerts");
    }
}
