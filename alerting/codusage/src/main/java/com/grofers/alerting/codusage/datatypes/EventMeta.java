package com.grofers.alerting.codusage.datatypes;

import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.annotation.JsonProperty;

@JsonIgnoreProperties(ignoreUnknown = true)
public class EventMeta {
    private final String eventName;

    public EventMeta(@JsonProperty(value = "event_name", required = true) String eventName) {
        this.eventName = eventName;
    }

    public String getEventName() {
        return eventName;
    }
}
