package com.grofers.alerting.codusage.datatypes;

import java.time.Instant;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.annotation.JsonProperty;

@JsonIgnoreProperties(ignoreUnknown = true)
public class OrderLifecycleEvent {
    private final String eventId;
    private final EventMeta meta;
    private final EventOrder order;

    public OrderLifecycleEvent(
            @JsonProperty(value = "event_id", defaultValue = "null") String eventId,
            @JsonProperty(value = "_meta", defaultValue = "null") EventMeta meta,
            @JsonProperty(value = "order", defaultValue = "null") EventOrder order) {
        this.eventId = eventId;
        this.meta = meta;
        this.order = order;
    }

    public Long getEventEpochTimeInMillis() {
        if (this.order != null) {
            return this.order.getOrderCreated().toEpochMilli();
        }

        return Instant.now().minusSeconds(60 * 60 * 24 * 365).toEpochMilli();
    }

    public EventOrder getOrder() {
        return this.order;
    }

    public EventMeta getMeta() {
        return meta;
    }
}
