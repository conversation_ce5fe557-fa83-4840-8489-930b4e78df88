package com.grofers.alerting.codusage.datatypes;

import java.time.Instant;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.annotation.JsonProperty;

@JsonIgnoreProperties(ignoreUnknown = true)
public class EventOrder {
    private final Instant orderCreated;
    private final Instant orderUpdated;
    private final Integer orderId;
    private final Integer cartId;
    private final String deviceId;
    private final OrderPayment payment;
    private final OrderCustomer customer;

    public EventOrder(
            @JsonProperty(value = "datetime_created", required = true) Long orderCreated,
            @JsonProperty(value = "datetime_updated", required = true) Long orderUpdated,
            @JsonProperty(value = "order_id") Integer orderId,
            @JsonProperty(value = "cart_id", required = true) Integer cartId,
            @JsonProperty(value = "device_id") String deviceId,
            @JsonProperty(value = "payment", required = true) OrderPayment payment,
            @JsonProperty(value = "customer", required = true) OrderCustomer customer) {
        this.orderCreated = Instant.ofEpochSecond(orderCreated);
        this.orderUpdated = Instant.ofEpochSecond(orderUpdated);
        this.orderId = orderId;
        this.cartId = cartId;
        this.deviceId = deviceId;
        this.payment = payment;
        this.customer = customer;
    }

    public Integer getCartId() {
        return cartId;
    }

    public Instant getOrderCreated() {
        return orderCreated;
    }

    public Instant getOrderUpdated() {
        return orderUpdated;
    }

    public OrderPayment getPayment() {
        return payment;
    }

    public OrderCustomer getCustomer() {
        return customer;
    }

    public String getDeviceId() {
        return deviceId;
    }
}
