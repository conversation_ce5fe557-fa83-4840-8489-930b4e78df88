package com.grofers.alerting.codusage.datatypes;

import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.annotation.JsonProperty;

@JsonIgnoreProperties(ignoreUnknown = true)
public class OrderPayment {
    private final String mode;

    public OrderPayment(@JsonProperty(value = "mode", required = true) String mode) {
        this.mode = mode;
    }

    public String getMode() {
        return mode;
    }
}
