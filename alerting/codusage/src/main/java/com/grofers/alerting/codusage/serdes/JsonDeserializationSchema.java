package com.grofers.alerting.codusage.serdes;

import com.grofers.alerting.codusage.datatypes.OrderLifecycleEvent;
import java.io.IOException;
import org.apache.flink.api.common.serialization.DeserializationSchema;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.MapperFeature;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.ObjectMapper;

public class JsonDeserializationSchema implements DeserializationSchema<OrderLifecycleEvent> {
    private static final long serialVersionUID = 1L;
    private static final ObjectMapper objectMapper =
            new ObjectMapper().configure(MapperFeature.AUTO_DETECT_GETTERS, false);

    @Override
    public OrderLifecycleEvent deserialize(byte[] bytes) throws IOException {
        return objectMapper.readValue(bytes, OrderLifecycleEvent.class);
    }

    @Override
    public boolean isEndOfStream(OrderLifecycleEvent orderLifecycleEvent) {
        return false;
    }

    @Override
    public TypeInformation<OrderLifecycleEvent> getProducedType() {
        return null;
    }
}
