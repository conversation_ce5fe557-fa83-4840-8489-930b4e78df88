package com.grofers.alerting.codusage.serdes;

import com.grofers.alerting.codusage.datatypes.UserCODCountAlert;
import java.nio.charset.StandardCharsets;
import org.apache.flink.api.common.serialization.SerializationSchema;
import org.apache.flink.connector.kafka.sink.KafkaRecordSerializationSchema;
import org.apache.kafka.clients.producer.ProducerRecord;

public class UserAlertsSerializationSchema
        implements KafkaRecordSerializationSchema<UserCODCountAlert> {
    private static final long serialVersionUID = 1L;
    private final String topic;

    public UserAlertsSerializationSchema(String topic) {
        this.topic = topic;
    }

    @Override
    public void open(
            SerializationSchema.InitializationContext context, KafkaSinkContext sinkContext)
            throws Exception {
        KafkaRecordSerializationSchema.super.open(context, sinkContext);
    }

    @Override
    public ProducerRecord<byte[], byte[]> serialize(
            UserCODCountAlert result, KafkaSinkContext kafkaSinkContext, Long aLong) {
        return new ProducerRecord<>(this.topic, result.toString().getBytes(StandardCharsets.UTF_8));
    }
}
