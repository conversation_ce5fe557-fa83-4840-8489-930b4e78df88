package com.grofers.alerting.codusage.datatypes;

import java.time.Instant;

public class UserCODCountAlert {
    private final Long customerId;
    private final Long expiry;

    public UserCODCountAlert(Long customerId, Long expiry) {
        this.customerId = customerId;
        this.expiry = expiry;
    }

    @Override
    public String toString() {

        return String.format(
                "{"
                        + "\"blinkID\":\"user:%s\","
                        + "\"context\":\"abuse_config\","
                        + "\"dataType\":\"MAP\","
                        + "\"ttl\":%s,"
                        + "\"contextProperties\":{"
                        + "\"payment_cod\":true}}",
                customerId, Instant.ofEpochMilli(expiry).getEpochSecond());
    }
}
