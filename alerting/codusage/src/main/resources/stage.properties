CHECKPOINTS_STORAGE_LOCATION = s3a://grofers-test-dse-singapore/flink-streams/alerting/codusage/checkpoints/
ORDER_LIFECYCLE_EVENTS_SOURCE_BROKERS = ****************************************.groupblinkit-agent.kafka.discoveryv2.prod-y.ap-southeast-1.warpstream.com:9092
ORDER_LIFECYCLE_EVENTS_SOURCE_CONSUMER_GROUP_ID = blinkit.data.test.flink-cod-usage-pipeline.v1
ORDER_LIFECYCLE_EVENTS_SOURCE_TOPIC = blinkit.order.lifecycle-events
IDLENESS_TIME_IN_SECS = 60
ALLOWED_LATE_EVENTS_IN_SECS = 600
ALERTS_SINK_TOPIC = test.profilestore.sql.properties
