CHECKPOINTS_STORAGE_LOCATION = s3a://prod-data-flink-states/flink-streams/alerting/codusage/checkpoints/
ORDER_LIFECYCLE_EVENTS_SOURCE_BROKERS = b-1.prod-pubsub-clust.fezhdo.c4.kafka.ap-southeast-1.amazonaws.com:9092,b-3.prod-pubsub-clust.fezhdo.c4.kafka.ap-southeast-1.amazonaws.com:9092,b-2.prod-pubsub-clust.fezhdo.c4.kafka.ap-southeast-1.amazonaws.com:9092
ORDER_LIFECYCLE_EVENTS_SOURCE_CONSUMER_GROUP_ID = blinkit.data.flink-cod-usage-pipeline.v1
ORDER_LIFECYCLE_EVENTS_SOURCE_TOPIC = blinkit.order.lifecycle-events
IDLENESS_TIME_IN_SECS = 60
ALLOWED_LATE_EVENTS_IN_SECS = 600
ALERTS_SINK_TOPIC = profilestore.realtime.properties
