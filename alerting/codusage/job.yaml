name: COD Usage
image: 442534439095.dkr.ecr.ap-southeast-1.amazonaws.com/data/flink-streams/alerting/cod-usage
flink_config:
  jobmanager.memory.process.size: 1536m
  taskmanager.memory.process.size: 6144m
  taskmanager.numberOfTaskSlots: 2
deployment_config:
  kubernetes.rest-service.exposed.type: NodePort
  metrics.reporters: prom
  metrics.reporter.prom.class: org.apache.flink.metrics.prometheus.PrometheusReporter
allow_non_restored_state: False # Set to True when changes are made to Operator
secrets:
  master:
    SINK_BOOTSTRAP_SERVER_URL_STAGE: "data/kafka/test-data-common:bootstrap.servers.ssl"
    SINK_BOOTSTRAP_SERVER_PASSWORD_STAGE: "data/kafka/test-data-common:password"
    SINK_BOOTSTRAP_SERVER_USERNAME_STAGE: "data/kafka/test-data-common:username"
    SINK_BOOTSTRAP_SERVER_URL_PROD: "data/kafka/feature-store-realtime:bootstrap.servers.ssl"
    SINK_BOOTSTRAP_SERVER_PASSWORD_PROD: "data/kafka/feature-store-realtime:password"
    SINK_BOOTSTRAP_SERVER_USERNAME_PROD: "data/kafka/feature-store-realtime:username"
