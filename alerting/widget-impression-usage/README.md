# 📊 Widget Impression Process Pipeline (Flink Job)

## ✅ Overview
This Flink job monitors widget impressions to detect when a user has seen a specific widget across a threshold number of **distinct sessions**. When the threshold is met, an alert is pushed to a Kafka topic used by the **Feature Store** to segment users accordingly.

---

## 📥 Source
- **Kafka Topic**: `jumbo_transformed.blinkit.product_image_shown_events`

---

## 📤 Sink
- **Kafka Topic**: `profilestore.realtime.properties`
    - Consuming in **Feature Store**
    - Enables segment tagging (e.g., `birthday_pt_shown`)

## To add new widget
    - Put the mapping in WidgetMaxImpressionCount 

---
