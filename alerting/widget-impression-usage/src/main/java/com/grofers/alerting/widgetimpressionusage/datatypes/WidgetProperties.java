package com.grofers.alerting.widgetimpressionusage.datatypes;

import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.annotation.JsonProperty;

@JsonIgnoreProperties(ignoreUnknown = true)
public class WidgetProperties {

    private final String widgetTitle;
    private final String widgetName;
    private final String widgetTrackingId;

    public WidgetProperties(
            @JsonProperty("widget_title") String widgetTitle,
            @JsonProperty("widget_name") String widgetName,
            @JsonProperty("widget_tracking_id") String widgetTrackingId) {
        this.widgetTitle = widgetTitle != null ? widgetTitle : "";
        this.widgetName = widgetName != null ? widgetName : "";
        this.widgetTrackingId = widgetTrackingId != null ? widgetTrackingId : "";
    }

    public String getWidgetTitle() {
        return widgetTitle;
    }

    public String getWidgetName() {
        return widgetName;
    }

    public String getWidgetTrackingId() {
        return widgetTrackingId;
    }
}