package com.grofers.alerting.widgetimpressionusage.serdes;

import java.io.IOException;

import com.grofers.alerting.widgetimpressionusage.datatypes.WidgetImpressionEvent;
import org.apache.flink.api.common.serialization.DeserializationSchema;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.MapperFeature;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.ObjectMapper;

public class JsonDeserializationSchema implements DeserializationSchema<WidgetImpressionEvent> {
    private static final long serialVersionUID = 1L;
    private static final ObjectMapper objectMapper =
            new ObjectMapper().configure(MapperFeature.AUTO_DETECT_GETTERS, false);

    @Override
    public WidgetImpressionEvent deserialize(byte[] bytes) throws IOException {
        return objectMapper.readValue(bytes, WidgetImpressionEvent.class);
    }

    @Override
    public boolean isEndOfStream(WidgetImpressionEvent widgetImpressionEvent) {
        return false;
    }

    @Override
    public TypeInformation<WidgetImpressionEvent> getProducedType() {
        return null;
    }
}
