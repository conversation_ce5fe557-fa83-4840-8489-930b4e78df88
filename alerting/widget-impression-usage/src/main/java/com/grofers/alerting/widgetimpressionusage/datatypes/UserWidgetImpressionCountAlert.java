package com.grofers.alerting.widgetimpressionusage.datatypes;

import org.apache.commons.lang3.StringUtils;

import java.time.Instant;

public class UserWidgetImpressionCountAlert {
    private final String userId;
    private final String widgetTitle;
    private final Long expiry;

    public UserWidgetImpressionCountAlert(String userId, String widgetTitle, Long expiry) {
        this.userId = userId;
        this.widgetTitle = widgetTitle;
        this.expiry = expiry;
    }

    @Override
    public String toString() {
        String segmentName = WidgetMaxImpressionCount.getSegmentName(this.widgetTitle);
        return String.format(
                "{"
                        + "\"blinkID\":\"user:%s\","
                        + "\"context\":\"audiences_realtime\","
                        + "\"dataType\":\"MAP\","
                        + "\"contextProperties\":{"
                        + "\"%s\":true}}",
                userId,
                segmentName
        );
    }
}
