package com.grofers.alerting.widgetimpressionusage.datatypes;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

public class WidgetMaxImpressionCount {

    private static final String BIRTHDAY_PT_WIDGET_TITLE = "pt_rail_all_feed_consumer_birthday";
    private static final String BIRTHDAY_PT_SHOWN_SEGMENT = "birthday_pt_shown";

    private static final Map<String, Integer> maxImpressionCountMap;
    private static final Map<String, String> widgetSegmentMap;

    static {
        // Max impression count setup
        Map<String, Integer> impressionMap = new HashMap<>();
        impressionMap.put(BIRTHDAY_PT_WIDGET_TITLE, 1);
        maxImpressionCountMap = Collections.unmodifiableMap(impressionMap);

        // Segment name setup
        Map<String, String> segmentMap = new HashMap<>();
        segmentMap.put(BIRTHDAY_PT_WIDGET_TITLE, BIRTHDAY_PT_SHOWN_SEGMENT);
        widgetSegmentMap = Collections.unmodifiableMap(segmentMap);
    }

    public static int getMaxImpressionCount(String widgetTitle) {
        return maxImpressionCountMap.getOrDefault(widgetTitle, 0);
    }

    public static Map<String, Integer> getMaxImpressionCountMap() {
        return maxImpressionCountMap;
    }

    public static String getSegmentName(String widgetTitle) {
        return widgetSegmentMap.get(widgetTitle);
    }

    public static Map<String, String> getWidgetSegmentMap() {
        return widgetSegmentMap;
    }
}
