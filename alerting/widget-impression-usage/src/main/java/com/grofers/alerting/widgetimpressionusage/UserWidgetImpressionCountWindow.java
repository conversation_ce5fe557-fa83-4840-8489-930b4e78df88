package com.grofers.alerting.widgetimpressionusage;

import java.time.Instant;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.util.HashSet;
import java.util.Set;

import com.grofers.alerting.widgetimpressionusage.datatypes.UserWidgetImpressionCountAlert;
import com.grofers.alerting.widgetimpressionusage.datatypes.WidgetImpressionEvent;
import com.grofers.alerting.widgetimpressionusage.datatypes.WidgetMaxImpressionCount;
import org.apache.flink.api.common.state.StateTtlConfig;
import org.apache.flink.api.common.state.ValueState;
import org.apache.flink.api.common.state.ValueStateDescriptor;
import org.apache.flink.api.common.time.Time;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.KeyedProcessFunction;
import org.apache.flink.util.Collector;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * The data type stored in the state
 * Also considering 1 session as 1 impression
 */
class CustomerWidgetImpressionsWithTimestamp {
    public String userId;
    public String widgetTitle;
    public Set<String> sessionIds;
    public Long lastModified;
}

public class UserWidgetImpressionCountWindow extends KeyedProcessFunction<String, WidgetImpressionEvent, UserWidgetImpressionCountAlert> {
    private static final Logger LOG = LoggerFactory.getLogger(UserWidgetImpressionCountWindow.class);
    private ValueState<CustomerWidgetImpressionsWithTimestamp> userWidgetImpressionState;

    @Override
    public void open(Configuration parameters) throws Exception {

        StateTtlConfig stateTtlConfig =
                StateTtlConfig.newBuilder(Time.days(1))
                        .setStateVisibility(StateTtlConfig.StateVisibility.NeverReturnExpired)
                        .build();
        ValueStateDescriptor<CustomerWidgetImpressionsWithTimestamp> descriptor =
                new ValueStateDescriptor<>("userWidgetImpressions", CustomerWidgetImpressionsWithTimestamp.class);
        descriptor.enableTimeToLive(stateTtlConfig);
        userWidgetImpressionState = getRuntimeContext().getState(descriptor);
    }

    @Override
    public void processElement(
            WidgetImpressionEvent widgetImpressionEvent,
            KeyedProcessFunction<String, WidgetImpressionEvent, UserWidgetImpressionCountAlert>.Context context,
            Collector<UserWidgetImpressionCountAlert> collector)
            throws Exception {

        CustomerWidgetImpressionsWithTimestamp currentImpressions = userWidgetImpressionState.value();
        if (currentImpressions != null && currentImpressions.sessionIds.contains(widgetImpressionEvent.getSessionId())) {
            return; // Duplicate session, ignore
        }
        if (currentImpressions == null) {
            currentImpressions = new CustomerWidgetImpressionsWithTimestamp();
            currentImpressions.userId = widgetImpressionEvent.getUserId();
            currentImpressions.widgetTitle = widgetImpressionEvent.getProperties().getWidgetTitle();
            currentImpressions.sessionIds = new HashSet<>();
        }

        // Add new session ID
        currentImpressions.sessionIds.add(widgetImpressionEvent.getSessionId());
        currentImpressions.lastModified = context.timestamp();
        userWidgetImpressionState.update(currentImpressions);

        // Calculate next midnight in IST
        Instant nextMidnight = Instant.ofEpochMilli(context.timestamp())
                .atZone(ZoneId.of("Asia/Kolkata"))
                .plusDays(1)
                .truncatedTo(ChronoUnit.DAYS)
                .toInstant();
        context.timerService().registerEventTimeTimer(nextMidnight.toEpochMilli());

        int maxImpressionCount = WidgetMaxImpressionCount.getMaxImpressionCount(currentImpressions.widgetTitle);
        if (currentImpressions.sessionIds.size() == maxImpressionCount) {
            collector.collect(new UserWidgetImpressionCountAlert(currentImpressions.userId, currentImpressions.widgetTitle, nextMidnight.toEpochMilli()));
        }
    }

    @Override
    public void onTimer(
            long timestamp, OnTimerContext context, Collector<UserWidgetImpressionCountAlert> collector)
            throws Exception {

        LOG.info("Timer triggered at timestamp: {}", timestamp);

        // Clear state when timer fires at midnight
        CustomerWidgetImpressionsWithTimestamp currentState = userWidgetImpressionState.value();
        if (currentState != null) {
            // Get the day boundary for the current timer
            Instant timerDayBoundary = Instant.ofEpochMilli(timestamp)
                    .atZone(ZoneId.of("Asia/Kolkata"))
                    .truncatedTo(ChronoUnit.DAYS)
                    .toInstant();
            LOG.info("Timer day boundary: {}", timerDayBoundary);

            // Get the day boundary for the last modification
            Instant lastModifiedDay = Instant.ofEpochMilli(currentState.lastModified)
                    .atZone(ZoneId.of("Asia/Kolkata"))
                    .truncatedTo(ChronoUnit.DAYS)
                    .toInstant();
            LOG.info("Last modified day: {}", lastModifiedDay);

            // Clear if the timer is for a different day than the last modification
            if (timerDayBoundary.isAfter(lastModifiedDay)) {
                userWidgetImpressionState.clear();
                LOG.info("State cleared");
            }
        }

    }
}
