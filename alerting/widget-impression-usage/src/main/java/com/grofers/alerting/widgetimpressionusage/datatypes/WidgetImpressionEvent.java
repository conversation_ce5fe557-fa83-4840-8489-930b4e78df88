package com.grofers.alerting.widgetimpressionusage.datatypes;

import java.time.Instant;

import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.annotation.JsonProperty;

@JsonIgnoreProperties(ignoreUnknown = true)
public class WidgetImpressionEvent {
    private final String eventName;
    private final WidgetProperties properties;
    private final Instant timestamp;
    private final String userId;
    private final String sessionId;


    public WidgetImpressionEvent(
            @JsonProperty(value = "event_name", required = false) String eventName,
            @JsonProperty(value = "properties", required = false) WidgetProperties properties,
            @JsonProperty(value = "timestamp", required = false) Long timestamp,
            @JsonProperty(value = "user_id", required = false) String userId,
            @JsonProperty(value = "session_id", required = false) String sessionId) {
        this.eventName = eventName != null ? eventName : "";
        this.properties = properties != null ? properties : new WidgetProperties("", "", "");
        this.timestamp = timestamp != null ? Instant.ofEpochSecond(timestamp) : Instant.now();
        this.userId = userId != null ? userId : "";
        this.sessionId = sessionId != null ? sessionId : "";
    }

    public String getEventName() {
        return eventName;
    }

    public WidgetProperties getProperties() {
        return properties;
    }

    public Long getEventEpochTimeInMillis() {
        if (this.timestamp != null) {
            return this.timestamp.toEpochMilli();
        }
        return Instant.now().minusSeconds(60 * 60 * 24 * 365).toEpochMilli();
    }

    public String getUserId() {
        return userId;
    }

    public String getSessionId() {
        return sessionId;
    }

}
