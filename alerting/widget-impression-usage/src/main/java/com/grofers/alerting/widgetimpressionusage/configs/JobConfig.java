package com.grofers.alerting.widgetimpressionusage.configs;

public final class JobConfig {
    public static String CHECKPOINTS_STORAGE_LOCATION;
    public static String WIDGET_IMPRESSION_EVENTS_SOURCE_BROKERS;
    public static String WIDGET_IMPRESSION_EVENTS_CONSUMER_GROUP_ID;
    public static String[] WIDGET_IMPRESSION_EVENTS_SOURCE_TOPIC;
    public static int IDLENESS_TIME_IN_SECS;
    public static int ALLOWED_LATE_EVENTS_IN_SECS;
    public static String ALERTS_SINK_TOPIC;

    public JobConfig(
            String CHECKPOINTS_STORAGE_LOCATION,
            String WIDGET_IMPRESSION_EVENTS_SOURCE_BROKERS,
            String WIDGET_IMPRESSION_EVENTS_CONSUMER_GROUP_ID,
            String WIDGET_IMPRESSION_EVENTS_SOURCE_TOPIC,
            int IDLENESS_TIME_IN_SECS,
            int ALLOWED_LATE_EVENTS_IN_SECS,
            String ALERTS_SINK_TOPIC) {

        JobConfig.CHECKPOINTS_STORAGE_LOCATION = CHECKPOINTS_STORAGE_LOCATION;
        JobConfig.WIDGET_IMPRESSION_EVENTS_SOURCE_BROKERS = WIDGET_IMPRESSION_EVENTS_SOURCE_BROKERS;
        JobConfig.WIDGET_IMPRESSION_EVENTS_CONSUMER_GROUP_ID =
                WIDGET_IMPRESSION_EVENTS_CONSUMER_GROUP_ID;
        JobConfig.WIDGET_IMPRESSION_EVENTS_SOURCE_TOPIC =
                WIDGET_IMPRESSION_EVENTS_SOURCE_TOPIC.split(",");
        JobConfig.IDLENESS_TIME_IN_SECS = IDLENESS_TIME_IN_SECS;
        JobConfig.ALLOWED_LATE_EVENTS_IN_SECS = ALLOWED_LATE_EVENTS_IN_SECS;
        JobConfig.ALERTS_SINK_TOPIC = ALERTS_SINK_TOPIC;
    }
}
