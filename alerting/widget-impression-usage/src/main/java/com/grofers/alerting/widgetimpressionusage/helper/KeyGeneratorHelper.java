package com.grofers.alerting.widgetimpressionusage.helper;

import com.grofers.alerting.widgetimpressionusage.datatypes.WidgetImpressionEvent;
import org.apache.commons.lang3.StringUtils;

public final class KeyGeneratorHelper {
    private static final String KEY_SEPARATOR = "_";

    private KeyGeneratorHelper() {
        // Private constructor to prevent instantiation
    }

    /**
     * Generates a composite key combining customerId and widgetTitle
     * @param event WidgetImpressionEvent containing customer and widget information
     * @return String in format "customerId_widgetTitle"
     */
    public static String generateWidgetImpressionKey(WidgetImpressionEvent event) {
        if (event == null || event.getProperties() == null) {
            throw new IllegalArgumentException("Event or widget properties cannot be null");
        }
        if (StringUtils.isBlank(event.getUserId()) || StringUtils.isBlank(event.getSessionId())) {
            throw new IllegalArgumentException("user id and session id cannot be null or empty");
        }

        return String.join(
                KEY_SEPARATOR,
                event.getUserId(),
                event.getProperties().getWidgetTitle()
        );
    }
}
