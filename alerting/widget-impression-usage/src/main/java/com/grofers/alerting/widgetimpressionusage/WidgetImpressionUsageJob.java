/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.grofers.alerting.widgetimpressionusage;

import static java.time.Duration.ofSeconds;

import com.grofers.alerting.widgetimpressionusage.configs.JobConfig;
import com.grofers.alerting.widgetimpressionusage.configs.JobConfigManager;
import com.grofers.alerting.widgetimpressionusage.datatypes.WidgetImpressionEvent;
import com.grofers.alerting.widgetimpressionusage.datatypes.UserWidgetImpressionCountAlert;
import com.grofers.alerting.widgetimpressionusage.datatypes.WidgetMaxImpressionCount;
import com.grofers.alerting.widgetimpressionusage.helper.KeyGeneratorHelper;
import com.grofers.alerting.widgetimpressionusage.serdes.JsonDeserializationSchema;
import com.grofers.alerting.widgetimpressionusage.serdes.UserAlertsSerializationSchema;
import org.apache.flink.api.common.ExecutionConfig;
import org.apache.flink.api.common.eventtime.WatermarkStrategy;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.connector.base.DeliveryGuarantee;
import org.apache.flink.connector.kafka.sink.KafkaSink;
import org.apache.flink.connector.kafka.source.KafkaSource;
import org.apache.flink.connector.kafka.source.enumerator.initializer.OffsetsInitializer;
import org.apache.flink.streaming.api.CheckpointingMode;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.datastream.DataStreamSource;
import org.apache.flink.streaming.api.environment.CheckpointConfig;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.kafka.clients.consumer.OffsetResetStrategy;

import java.util.Arrays;
import java.util.Properties;

/**
 * Flink Streaming Job for calculating widget impression usage
 */
public class WidgetImpressionUsageJob {

    private final DataStreamSource<WidgetImpressionEvent> WidgetImpressionEventDataStreamSource;
    private final KafkaSink<UserWidgetImpressionCountAlert> widgetImpressionAlertsSink;
    static final String[] EVENT_FILTERS = {
            "Image Shown",
            "Banner Widget Shown"
    };

    public WidgetImpressionUsageJob(
            DataStreamSource<WidgetImpressionEvent> WidgetImpressionEventDataStreamSource,
            KafkaSink<UserWidgetImpressionCountAlert> widgetImpressionAlertsSink) {
        this.WidgetImpressionEventDataStreamSource = WidgetImpressionEventDataStreamSource;
        this.widgetImpressionAlertsSink = widgetImpressionAlertsSink;
    }

    public static void main(String[] args) throws Exception {

        // Sets up the execution environment, which is the main entry point
        // to building Flink applications.
        final StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment();

        // Parameters for environment
        ParameterTool params = ParameterTool.fromArgs(args);
        String userEnv = params.getRequired("environment");


        // Set PropertiesPath for userEnv
        JobConfigManager.setJobConfigsPath(userEnv);
        // Get PropertiesPath for userEnv
        String jobPropertiesPath = JobConfigManager.getJobConfigsPath(userEnv);

        // Fetch job config
        JobConfigManager.setJobConfigs(env, jobPropertiesPath);
        JobConfigManager.getJobConfigs(env);

        // Checkpoint Configs
        // start a checkpoint every 240000 ms
        env.enableCheckpointing(240000);
        // set mode to exactly-once (default)
        env.getCheckpointConfig().setCheckpointingMode(CheckpointingMode.EXACTLY_ONCE);
        // make sure 120000 ms of progress happen between checkpoints
        env.getCheckpointConfig().setMinPauseBetweenCheckpoints(240000);
        // checkpoints have to complete within a minute, or are discarded
        env.getCheckpointConfig().setCheckpointTimeout(210000);
        // only two consecutive checkpoint failures are tolerated
        env.getCheckpointConfig().setTolerableCheckpointFailureNumber(2);
        // allow only one checkpoint to be in progress at the same time
        env.getCheckpointConfig().setMaxConcurrentCheckpoints(1);
        // enable externalized checkpoints which are retained after job cancellation
        env.getCheckpointConfig()
                .setExternalizedCheckpointCleanup(
                        CheckpointConfig.ExternalizedCheckpointCleanup.RETAIN_ON_CANCELLATION);
        // enables the experimental unaligned checkpoints
        env.getCheckpointConfig().enableUnalignedCheckpoints();
        // sets the checkpoint storage where checkpoint snapshots will be written
        env.getCheckpointConfig().setCheckpointStorage(JobConfig.CHECKPOINTS_STORAGE_LOCATION);
        // Enable checkpoint compression
        ExecutionConfig executionConfig = new ExecutionConfig();
        executionConfig.setUseSnapshotCompression(true);

        // Determine the environment (prod or stage) and set properties accordingly
        String envPrefix = userEnv.equalsIgnoreCase("prod") ? "PROD" : "STAGE";
        String bootstrapServerUrl = System.getenv().getOrDefault("SINK_BOOTSTRAP_SERVER_URL_" + envPrefix, "localhost:9092");
        String bootstrapServerUsername = System.getenv().getOrDefault("SINK_BOOTSTRAP_SERVER_USERNAME_" + envPrefix, "username");
        String bootstrapServerPassword = System.getenv().getOrDefault("SINK_BOOTSTRAP_SERVER_PASSWORD_" + envPrefix, "password");

        Properties kafkaSinkProperties = new Properties();
        kafkaSinkProperties.setProperty("bootstrap.servers", bootstrapServerUrl);
        kafkaSinkProperties.setProperty("security.protocol", "SASL_SSL");
        kafkaSinkProperties.setProperty("sasl.mechanism", "SCRAM-SHA-512");
        kafkaSinkProperties.setProperty("sasl.jaas.config",
                "org.apache.kafka.common.security.scram.ScramLoginModule required " +
                        "username=\"" + bootstrapServerUsername + "\" " +
                        "password=\"" + bootstrapServerPassword + "\";");

        env.setMaxParallelism(12);

        KafkaSource<WidgetImpressionEvent> widgetImpressionEventKafkaSource =
                KafkaSource.<WidgetImpressionEvent>builder()
                        .setBootstrapServers(JobConfig.WIDGET_IMPRESSION_EVENTS_SOURCE_BROKERS)
                        .setGroupId(JobConfig.WIDGET_IMPRESSION_EVENTS_CONSUMER_GROUP_ID)
                        .setValueOnlyDeserializer(new JsonDeserializationSchema())
                        .setStartingOffsets(
                                OffsetsInitializer.committedOffsets(OffsetResetStrategy.LATEST))
                        .setTopics(JobConfig.WIDGET_IMPRESSION_EVENTS_SOURCE_TOPIC)
                        .setProperty("partition.discovery.interval.ms", "600000")
                        .setProperty("reconnect.backoff.max.ms", "300000")
                        .setProperty("reconnect.backoff.ms", "60000")
                        .setProperty("metadata.max.age.ms", "60000")
                        .setProperty("max.partition.fetch.bytes", "26214400")
                        .setProperty("fetch.max.bytes", "50242880")
                        .build();

        WatermarkStrategy<WidgetImpressionEvent> widgetImpressionEventWatermarkStrategy =
                WatermarkStrategy.<WidgetImpressionEvent>forBoundedOutOfOrderness(ofSeconds(600))
                        .withIdleness(ofSeconds(JobConfig.IDLENESS_TIME_IN_SECS))
                        .withTimestampAssigner(
                                (WidgetImpressionEvent, time) ->
                                        WidgetImpressionEvent.getEventEpochTimeInMillis());

        DataStreamSource<WidgetImpressionEvent> widgetImpressionEventDataStreamSource =
                env.fromSource(
                        widgetImpressionEventKafkaSource,
                        widgetImpressionEventWatermarkStrategy,
                        "widget-impression-events-kafka-source",
                        TypeInformation.of(WidgetImpressionEvent.class));

        KafkaSink<UserWidgetImpressionCountAlert> widgetImpressionAlertsSink =
                KafkaSink.<UserWidgetImpressionCountAlert>builder()
                        .setBootstrapServers(bootstrapServerUrl)
                        .setDeliverGuarantee(DeliveryGuarantee.AT_LEAST_ONCE)
                        .setRecordSerializer(
                                new UserAlertsSerializationSchema(JobConfig.ALERTS_SINK_TOPIC))
                        .setKafkaProducerConfig(kafkaSinkProperties)
                        .build();

        WidgetImpressionUsageJob widgetImpressionUsageJob =
                new WidgetImpressionUsageJob(
                        widgetImpressionEventDataStreamSource, widgetImpressionAlertsSink);

        widgetImpressionUsageJob.execute(env);
    }

    private void execute(StreamExecutionEnvironment env) throws Exception {

        DataStream<WidgetImpressionEvent> filteredWidgetImpressionStream =
                WidgetImpressionEventDataStreamSource
                        .name("widget-impression-events-source")
                        .uid("widget-impression-events-source")
                        .setParallelism(6)
                        .filter(event ->
                                event.getEventName() != null &&
                                Arrays.stream(EVENT_FILTERS)
                                        .anyMatch(filter -> filter.equalsIgnoreCase(event.getEventName())) &&
                                        event.getProperties() != null &&
                                        event.getProperties().getWidgetTitle() != null &&
                                        WidgetMaxImpressionCount.getMaxImpressionCountMap()
                                                .containsKey(event.getProperties().getWidgetTitle())
                        )
                        .name("filter-widget-impressions")
                        .uid("filter-widget-impressions");

        filteredWidgetImpressionStream
                .keyBy(KeyGeneratorHelper::generateWidgetImpressionKey)
                .process(new UserWidgetImpressionCountWindow())
                .name("process-widget-impressions")
                .uid("process-widget-impressions")
                .sinkTo(widgetImpressionAlertsSink)
                .name("widget-impression-alerts-sink")
                .uid("widget-impression-alerts-sink");

        env.execute("Widget Impression Usage Alerts");
    }
}