CHECKPOINTS_STORAGE_LOCATION = s3a://prod-data-flink-states/flink-streams/alerting/widget-impression-usage/checkpoints/
WIDGET_IMPRESSION_EVENTS_SOURCE_BROKERS = ****************************************.groupblinkit-agent.kafka.discoveryv2.prod-y.ap-southeast-1.warpstream.com:9092
WIDGET_IMPRESSION_EVENTS_SOURCE_CONSUMER_GROUP_ID = blinkit.data.flink-widget-impression-usage-pipeline.v1
WIDGET_IMPRESSION_EVENTS_SOURCE_TOPIC = jumbo_transformed.blinkit.product_image_shown_events
IDLENESS_TIME_IN_SECS = 60
ALLOWED_LATE_EVENTS_IN_SECS = 600
ALERTS_SINK_TOPIC = profilestore.realtime.properties
