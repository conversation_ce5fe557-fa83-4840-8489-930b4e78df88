from asyncio import run_coroutine_threadsafe
import os
import sys
import yaml
import logging
import subprocess
from jinja2 import Template
from kubernetes import config, client
from utils import fetch_aws_keys, fetch_secret_from_path

FLINK_VERSION = "flink-1.13.2"
SCALA_VERSION = "scala_2.12"
FLINK_ZIP_FILENAME = f"{FLINK_VERSION}-bin-{SCALA_VERSION}.tgz"
NAMESPACE = "pinot"
BUCKET_NAME = {"stage": "grofers-test-dse-singapore", "prod": "prod-data-flink-states"}
SAVEPOINT_BUCKET_NAME = "s3a://{BUCKET_NAME}/flink-streams/{folder}/{job_name}/savepoints"
HA_BUCKET_NAME = "s3a://{BUCKET_NAME}/flink-streams/{folder}/{job_name}/high-availability"
JOB_SUFFIX = {"prod": "", "stage": "stage-"}
CLUSTER_ID = "{job_suffix}{folder}-{job_name}"
DOCKER_TAG = {"stage": "stage", "prod": "stable"}
HA_CONFIGMAPS = [
    "{job_suffix}{folder}-{job_name}-00000000000000000000000000000000-jobmanager-leader",
    "{job_suffix}{folder}-{job_name}-dispatcher-leader",
    "{job_suffix}{folder}-{job_name}-resourcemanager-leader",
    "{job_suffix}{folder}-{job_name}-restserver-leader",
    "{job_suffix}{folder}-{job_name}-00000000000000000000000000000000-config-map",
    "{job_suffix}{folder}-{job_name}-cluster-config-map"
]
FLINK_BINARY_VERSION = os.environ.get("FLINK_BINARY_VERSION", "1.13.2")

logger = logging.getLogger(__name__)
logging.basicConfig(level=logging.INFO)

def _get_kube_client(in_cluster: bool):
    if in_cluster:
        config.load_incluster_config()
    else:
        config.load_kube_config()
    return None

def run_command(cmds=[], dryrun=False):
    command = " ".join(cmds)
    logger.info(command)
    if dryrun:
        logger.info("** Not Running the command due to dry run **")
        return

    subprocess.run(cmds)


def _default_config(job_name, folder, config_type, env):
    if config_type == "deployment_config":
        HA_BUCKET_NAME_FORMATTED = HA_BUCKET_NAME.format(
            BUCKET_NAME=BUCKET_NAME[env], folder=folder, job_name=job_name
        )
        spot_node_selector = "nodetype:data-common-spot"
        spot_tolerations = "key:service,operator:Equal,value:data-common,effect:NoSchedule"
        jobmanager_node_selector = "nodetype:data-ondemand,workload:data-ondemand-servers" if env == "prod" else spot_node_selector
        jobmanager_tolerations = "key:service,operator:Equal,value:data-ondemand-servers,effect:NoSchedule" if env == "prod" else spot_tolerations
        is_ondemand = os.getenv("ON_DEMAND", 'False').lower() in ('true', '1', 't')
        taskmanager_node_selector = jobmanager_node_selector if is_ondemand else spot_node_selector
        taskmanager_tolerations = jobmanager_tolerations if is_ondemand else spot_tolerations

        return [
            f"-Dkubernetes.cluster-id={CLUSTER_ID.format(job_name=job_name, folder=folder, job_suffix=JOB_SUFFIX[env])}",
            f"-Dkubernetes.namespace={NAMESPACE}",
            f"-Dkubernetes.container.image={_fetch_docker_image(job_name, folder, env)}",
            "-Dkubernetes.service-account=flink-service-account",
            "-Dkubernetes.container.image.pull-policy=Always",
            "-Dkubernetes.pod-template-file=kubernetes/common/pod-template.yaml",
            "-Dhigh-availability.type=kubernetes" if FLINK_BINARY_VERSION >= "1.19" else "-Dhigh-availability=org.apache.flink.kubernetes.highavailability.KubernetesHaServicesFactory",
            f"-Dhigh-availability.storageDir={HA_BUCKET_NAME_FORMATTED}",
            f"-Dkubernetes.jobmanager.node-selector={jobmanager_node_selector}",
            f"-Dkubernetes.jobmanager.tolerations={jobmanager_tolerations}",
            f"-Dkubernetes.taskmanager.node-selector={taskmanager_node_selector}",
            f"-Dkubernetes.taskmanager.tolerations={taskmanager_tolerations}",

        ]
    if config_type == "flink_config":
        aws_access_key, aws_secret_key = fetch_aws_keys(env)
        flink_aws_access_key, flink_aws_secret_key = fetch_aws_keys("prod")
        return [
            f"-Ds3a.access-key={aws_access_key}",
            f"-Ds3a.secret-key={aws_secret_key}",
            f"-Dcontainerized.taskmanager.env.AWS_ACCESS_KEY={flink_aws_access_key}",
            f"-Dcontainerized.taskmanager.env.AWS_SECRET_KEY={flink_aws_secret_key}",
        ]
    else:
        raise Exception(
            "Unknown config_type ! Must be `flink_config` or `deployment_config`"
        )


def _fetch_job_yaml(job_name, folder):
    job_path = os.path.join(f"{folder}/{job_name}", "job.yaml")
    with open(job_path, "r") as f:
        job_config = yaml.load(f, Loader=yaml.FullLoader)
    return job_config


def _fetch_job_config(job_name, folder, env=None, config_type="flink_config"):
    job_yaml = _fetch_job_yaml(job_name, folder)
    job_config = [f"-D{key}={value}" for key, value in job_yaml[config_type].items()]
    return _default_config(job_name, folder, config_type, env) + job_config


def _fetch_secret_config(job_name, folder, env=None):
    secret_config = []
    job_yaml = _fetch_job_yaml(job_name, folder)
    secrets = job_yaml.get("secrets")
    if secrets:
        for _process_name, _dict in secrets.items():
            for _key_name, _value in _dict.items():
                _path, _key = _value.split(":")
                secret_config.append(
                    f"-Dcontainerized.{_process_name}.env.{_key_name}={fetch_secret_from_path(_path)[_key]}"
                )
    return secret_config


def install_flink():
    FLINK_PATH = f".flink/{FLINK_VERSION}"
    if os.environ.get("FLINK_HOME"):
        print("FLINK_HOME is set")
        FLINK_PATH = os.environ["FLINK_HOME"]
    elif not os.path.exists(f".flink/{FLINK_VERSION}"):
        # Create a path to install Flink on
        os.mkdir(".flink")
        # Fetch flink binaries
        run_command(
            [
                "wget",
                f"https://downloads.apache.org/flink/{FLINK_VERSION}/{FLINK_ZIP_FILENAME}",
                "-P",
                ".flink",
            ]
        )
        # Unzip binaries
        run_command(["tar", "xzf", f".flink/{FLINK_ZIP_FILENAME}", "-C", ".flink"])
    else:
        print("Flink already exists ! clean up `.flink` directory to Reinstall")
    return FLINK_PATH


def _get_flink_path():
    return f"{install_flink()}/bin/flink"


def _fetch_docker_image(job_name, folder, env):
    job_yaml = _fetch_job_yaml(job_name, folder)
    image = f"{job_yaml['image']}:{DOCKER_TAG[env]}"
    return image


def docker_build(job_name, folder, env):
    if 'AWS_PROFILE' in os.environ:
            del os.environ['AWS_PROFILE']
    image = _fetch_docker_image(job_name, folder, env)
    codeartifact_access = _fetch_job_yaml(job_name, folder).get("codeartifact_access")
    if codeartifact_access:
        print("SETTING CODEARTIFACT_AUTH_TOKEN")
        
        command = [
            'aws',
            'codeartifact',
            'get-authorization-token',
            '--domain',
            'grofers',
            '--domain-owner',
            '442534439095',
            '--query',
            'authorizationToken',
            '--output',
            'text'
        ]
        process = subprocess.Popen(command, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        output, error = process.communicate()
        if process.returncode == 0:
            # Extract the authorization token from the output
            authorization_token = output.decode('utf-8').strip()

            # Set the environment variable
            os.environ['CODEARTIFACT_AUTH_TOKEN'] = authorization_token
        else:
            print("Error:", error.decode('utf-8'))

        # maven compile
        subprocess.run("mvn -s settings.xml compile", cwd=f"{folder}/{job_name}", shell=True)
    # package and build
    subprocess.run("mvn spotless:apply", cwd=f"{folder}/{job_name}", shell=True)
    subprocess.run("mvn clean package", cwd=f"{folder}/{job_name}", shell=True)
    cmd = f"docker build . -t {image}"
    result = subprocess.run(cmd, cwd=f"{folder}/{job_name}", shell=True)
    if result.stderr:
        raise subprocess.CalledProcessError(
                returncode = result.returncode,
                cmd = result.args,
                stderr = result.stderr)
    result = subprocess.run(f"aws ecr get-login-password --region ap-southeast-1 | docker login --username AWS --password-stdin 442534439095.dkr.ecr.ap-southeast-1.amazonaws.com && docker push {image}", shell=True)
    if result.stderr:
        raise subprocess.CalledProcessError(
                returncode = result.returncode,
                cmd = result.args,
                stderr = result.stderr)
    return image

def get_kubernetes_params(job_name, folder, env):
    return (
        _fetch_job_config(job_name, folder, env, config_type="deployment_config")
        + _fetch_job_config(job_name, folder, env, config_type="flink_config")
        + _fetch_secret_config(job_name, folder, env)
        + [f"local:///opt/flink/usrlib/{job_name}.jar", "-environment", env]
    )


def deploy_job(job_name, folder, env, *args, **kwargs):
    cmd = [
        f"{_get_flink_path()}",
        "run-application",
        "--target",
        "kubernetes-application",
    ] + get_kubernetes_params(job_name, folder, env)
    run_command(cmd)


def stop_job_with_savepoint(job_name, folder, env, flink_job_params, *args, **kwargs):
    if ',' in flink_job_params:
        flink_job_url, flink_job_id  = flink_job_params.split(",")
    else:
        flink_job_id = None
        flink_job_url = flink_job_params
    job_id=flink_job_id or '00000000000000000000000000000000'
    flink_job_host, flink_job_port = flink_job_url.split(":")
    cmd = [
        f"{_get_flink_path()}",
        "stop",
        f"-Drest.address={flink_job_host}",
        f"-Drest.port={flink_job_port}",
        "--savepointPath",
        SAVEPOINT_BUCKET_NAME.format(BUCKET_NAME=BUCKET_NAME[env], folder=folder, job_name=job_name),
        f"{job_id}",
    ]
    run_command(cmd)


def restart_job_from_savepoint(job_name, folder, env, savepoint_path, *args, **kwargs):
    savepoint_cmd = [
        f"{_get_flink_path()}",
        "run-application",
        f"--fromSavepoint={savepoint_path}",
    ]
    if _fetch_job_yaml(job_name, folder)["allow_non_restored_state"]:
        savepoint_cmd += ["--allowNonRestoredState"]
    cmd = (
        savepoint_cmd
        + ["--target", "kubernetes-application"]
        + get_kubernetes_params(job_name, folder, env)
    )
    run_command(cmd)


def delete_deployment(job_name, folder, env, *args, **kwargs):
    # Delete the Deployment
    cluster_id = CLUSTER_ID.format(job_name=job_name, folder=folder, job_suffix=JOB_SUFFIX[env])
    run_command(["kubectl", "delete", f"deployment/{cluster_id}", "-n", "pinot"])


def delete_configmap(job_name, folder, env, *args, **kwargs):
    # Delete Remnant configmaps
    for configmap in HA_CONFIGMAPS:
        rendered_configmap = configmap.format(
            job_name=job_name, folder=folder, job_suffix=JOB_SUFFIX[env]
        )
        run_command(
            ["kubectl", "delete", "configmap", "-n", "pinot", f"{rendered_configmap}"]
        )


def delete_job(job_name, folder, env, *args, **kwargs):
    delete_deployment(job_name, folder, env)
    delete_configmap(job_name, folder, env)

def deploy_service_account(*args, **kwargs):
    run_command(["kubectl", "apply", "-f", "kubernetes/prod/rbac.yml", "-n", "pinot"])


def deploy_podmonitor(job_name, folder, *args, **kwargs):
    config_dict = {
        "job_name": job_name,
        "folder": folder
    }
    deployment = "kubernetes/monitoring/pod-monitor.yaml.j2"
    with open(deployment) as f:
        t = Template(f.read())
        config = t.render(**config_dict)
        monitors = yaml.safe_load_all(config)
        for monitor in monitors:
            _get_kube_client(False)
            api = client.CustomObjectsApi()
            logger.info(f"Creating {monitor['metadata']['name']}")
            api.create_namespaced_custom_object(
                group="monitoring.coreos.com",
                version="v1",
                namespace="pinot",
                plural="podmonitors",
                body=monitor
            )

class IllegalOperation(Exception):
    pass


if __name__ == "__main__":
    operation, args = sys.argv[1], sys.argv[2:]
    if operation == "docker-build-push":
        docker_build(*args)
    elif operation == "install-flink":
        install_flink()
    elif operation == "deploy-job":
        deploy_job(*args)
    elif operation == "delete-job":
        delete_job(*args)
    elif operation == "delete-deployment":
        delete_deployment(*args)
    elif operation == "delete-configmap":
        delete_configmap(*args)
    elif operation == "stop-job-with-savepoint":
        stop_job_with_savepoint(*args)
    elif operation == "restart-job-from-savepoint":
        restart_job_from_savepoint(*args)
    elif operation == "deploy-podmonitor":
        deploy_podmonitor(*args)
    elif operation == "deploy-service-account":
        deploy_service_account(*args)
    else:
        raise IllegalOperation(f"Allowed operations are {','.join(allowed_operations)}")
