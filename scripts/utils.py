import os
import hvac

AWS_CREDS_PATH = {
    "prod": "dse/flink/iam-roles/application-prod-data-flink",
    "stage": "dse/pencilbox/aws"
}

def fetch_secret_from_path(path):
    client = hvac.Client(url="https://vault-ui-prod.grofer.io")
    client.auth.github.login(token=os.environ.get("VAULT_AUTH_GITHUB_TOKEN"))
    if not client.is_authenticated():
        raise ValueError("Could not authenticate to Vault")
    return client.read(path)["data"]

def fetch_aws_keys(env):
    aws_creds = fetch_secret_from_path(AWS_CREDS_PATH[env])
    return aws_creds["aws_access_key_id"], aws_creds["aws_secret_access_key"]

def prompt_before_execution(func):
    def inner_func(*args, **kwargs):
        env = args[-1]
        if env == "prod":
            reply = input("Are you sure you want to run this on Production (y/n): ")
            if reply == ("y"):
                print ("Running command")
                func(*args, **kwargs)
            elif reply == ("n"):
                print ("Discarded command")
        elif env=="stage":
            func(*args, **kwargs)
    return inner_func
