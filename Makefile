ifndef STREAM
$(error STREAM is not set)
endif

ENV ?= stage

.PHONY: install
install:
	pip install -r scripts/requirements.txt

# STREAM=merchant-demand ENV=stage make docker-build
.PHONY: docker-build
docker-build:
	python scripts/commands.py docker-build $(STREAM) $(FOLDER) $(ENV)

# STREAM=merchant-demand ENV=stage make docker-push
.PHONY: docker-push
docker-push:
	python scripts/commands.py docker-push $(STREAM) $(FOLDER) $(ENV)

# make install
.PHONY: install-flink
install-flink:
	python scripts/commands.py install-flink

# STREAM=merchant-demand ENV=stage make deploy
.PHONY: deploy
deploy:
	python scripts/commands.py deploy-job $(STREAM) $(FOLDER) $(ENV)

# Port forward the Jobmanager to run on 8081 before executing the below commands

# STREAM=merchant-demand ENV=stage make stop-job-with-savepoint job_id=3f17593dca947442f0e8bf80ecf5dc78
.PHONY: stop-job-with-savepoint
stop-job-with-savepoint:
	python scripts/commands.py stop-job-with-savepoint $(STREAM) $(FOLDER) $(ENV) $(job_id)

# STREAM=merchant-demand ENV=stage make restart-job-from-savepoint savepoint=s3a://<path>
.PHONY: restart-job-from-savepoint
restart-job-from-savepoint:
	python scripts/commands.py restart-job-from-savepoint $(STREAM) $(FOLDER) $(savepoint) $(ENV)

.PHONY: delete-job
delete-job:
	python scripts/commands.py delete-job $(STREAM) $(FOLDER) $(ENV)

.PHONY: delete-deployment
delete-deployment:
	python scripts/commands.py delete-deployment $(STREAM) $(FOLDER) $(ENV)

.PHONY: delete-configmap
delete-configmap:
	python scripts/commands.py delete-configmap $(STREAM) $(FOLDER) $(ENV)
