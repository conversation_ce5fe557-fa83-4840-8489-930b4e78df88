# data-core-streams

## Purpose

This project contains all the core streams which can be used in general over multiple systems.


## Development

Head to the core stream you want to develop with `cd core-streams/<stream_name>`

* Create a fat jar:
```
mvn clean package
```

* Apply code style:
```
mvn spotless:apply
```

* To compile external jars from code artifactory

Make sure you have a created a new profile in `~/.aws/credentials`

```
[codeartifact]
aws_access_key_id = <ACCESS_KEY>
aws_secret_access_key = <SECRET_KEY>
region = <REGION>
```

For the actual creds ping us @team-de-real-time

## Installation

* Make sure you have `FLINK_HOME` set in your environment variables or Flink wil be installed in the `.flink` directory if you run this command to install flink

```
make install-flink
```

## Deployment

* Building and pushing the latest image for a core stream

```
STREAM=merchant-demand FOLDER=core-streams ENV=stage make docker-build
STREAM=merchant-demand FOLDER=core-streams ENV=stage make docker-push
```

* Deploying the stream on Kubernetes

```
STREAM=merchant-demand FOLDER=core-streams ENV=stage make deploy
```

* Before running the below steps, Make sure to open a port so that you are able to connect to the JobManager

```
kubectl port-forward -n <namespace> <jobmanager-pod> 8081:8081
```

Fetch the job_id from the UI or run `./bin/flink list` to get it in your terminal

* Stopping the Job with a Savepoint

```
STREAM=merchant-demand FOLDER=core-streams ENV=stage make stop-job-with-savepoint
```

* Resuming the Job from a savepoint Path returned by the command above

```
STREAM=merchant-demand FOLDER=core-streams ENV=stage make restart-job-from-savepoint savepoint=s3a://grofers-test-dse-singapore/core-stream-savepoints/merchant-demand/93ac032f6a8899746112edd7019cc80c/savepoint-93ac03-e07daec60c31
```

* Delete a Job deployment along with corresponding HA Configmaps

```
STREAM=merchant-demand FOLDER=core-streams ENV=stage make delete-job
```

* Delete a Job deployment

```
STREAM=merchant-demand FOLDER=core-streams ENV=stage make delete-deployment
```

* Delete a Job HA configmaps

```
STREAM=merchant-demand FOLDER=core-streams ENV=stage make delete-configmap
```
