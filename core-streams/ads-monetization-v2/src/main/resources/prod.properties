# Properties for stage environment

#Checkpoints
CHECKPOINTS_STORAGE_LOCATION = s3a://prod-data-flink-states/flink-streams/core-streams/ads-monetization-v2/checkpoints/

# Events Source
EVENTS_SOURCE_BROKERS = ****************************************.groupblinkit-agent.kafka.discoveryv2.prod-y.ap-southeast-1.warpstream.com:9092
EVENTS_IMPRESSION_CONSUMER_GROUP_ID = flink-core_streams-ads_monetization-impressions-consumer_group-v2
EVENTS_PRODUCT_IMAGE_SHOWN_CONSUMER_GROUP_ID = flink-core_streams-ads_monetization-product_image_shown-consumer_group-v2
EVENTS_WEB_CONSUMER_GROUP_ID = flink-core_streams-ads_monetization-web-consumer_group-v2
EVENTS_SOURCE_PRODUCT_IMAGE_SHOWN_TOPIC = jumbo_transformed.blinkit.product_image_shown_events
EVENTS_SOURCE_IMPRESSION_TOPIC = jumbo_transformed.blinkit.impression_events
EVENTS_SOURCE_WEB_TOPIC = jumbo_transformed.blinkit.blinkit_web_events

#Watermark Idleness Configs
IDLENESS_TIME_IN_SECS = 60

# Ads Window Configs
WINDOW_SIZE_IN_MINS = 5
ALLOWED_LATENESS_IN_SECS = 1800
AD_MONETIZATION_SINK_BROKERS = b-1.prod-data-corestr.uynfnv.c4.kafka.ap-southeast-1.amazonaws.com:9092,b-2.prod-data-corestr.uynfnv.c4.kafka.ap-southeast-1.amazonaws.com:9092,b-3.prod-data-corestr.uynfnv.c4.kafka.ap-southeast-1.amazonaws.com:9092
AD_MONETIZATION_SINK_TOPIC = corestreams.ads.ads-monetization-v2