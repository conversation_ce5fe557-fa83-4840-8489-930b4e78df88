# Properties for stage environment

#Checkpoints
CHECKPOINTS_STORAGE_LOCATION = s3a://grofers-test-dse-singapore/flink-streams/core-streams/neelesh-jumbo-test-ads-monetization/checkpoints/


# Events Source
#EVENTS_SOURCE_BROKERS = b-2.testdatacommon.vkkz3t.c5.kafka.ap-southeast-1.amazonaws.com:9092,b-1.testdatacommon.vkkz3t.c5.kafka.ap-southeast-1.amazonaws.com:9092
EVENTS_SOURCE_BROKERS = ****************************************.groupblinkit-agent.kafka.discoveryv2.prod-y.ap-southeast-1.warpstream.com:9092
EVENTS_IMPRESSION_CONSUMER_GROUP_ID = test-impression-flink-core_streams-ads_monetization-consumer_group-v1
EVENTS_PRODUCT_IMAGE_SHOWN_CONSUMER_GROUP_ID = test-product_image_shown-flink-core_streams-ads_monetization-consumer_group-v1
EVENTS_WEB_CONSUMER_GROUP_ID = test-web-flink-core_streams-ads_monetization-consumer_group-v1
EVENTS_SOURCE_PRODUCT_IMAGE_SHOWN_TOPIC = jumbo_transformed.blinkit.product_image_shown_events
EVENTS_SOURCE_IMPRESSION_TOPIC = jumbo_transformed.blinkit.impression_events
EVENTS_SOURCE_WEB_TOPIC = jumbo_transformed.blinkit.blinkit_web_events

#Watermark Idleness Configs
IDLENESS_TIME_IN_SECS = 60

# Ads Window Configs
WINDOW_SIZE_IN_MINS = 5
ALLOWED_LATENESS_IN_SECS = 1800
AD_MONETIZATION_SINK_BROKERS = b-2.testdatacommon.vkkz3t.c5.kafka.ap-southeast-1.amazonaws.com:9092,b-1.testdatacommon.vkkz3t.c5.kafka.ap-southeast-1.amazonaws.com:9092
#AD_MONETIZATION_SINK_BROKERS = localhost:9092
AD_MONETIZATION_SINK_TOPIC = neelesh-admonent-flink-test-1