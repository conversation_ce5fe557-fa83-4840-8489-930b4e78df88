package com.blinkit.corestreams.adsmonetization.events;

import com.grofers.gandalf.events.JumboEvent;
import com.grofers.gandalf.events.EventTraits;
import lombok.Getter;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.annotation.JsonProperty;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.annotation.JsonSerialize;

@JsonSerialize
@Getter
@JsonIgnoreProperties(ignoreUnknown = true)
public class JumboMonetizationEvent extends JumboEvent {
    @JsonProperty(value = "properties")
    EventProperties properties = null;

    @JsonProperty(value = "traits")
    EventTraits traits = null;
}
