package com.blinkit.corestreams.adsmonetization.events;

import com.grofers.gandalf.events.EventTraits;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class MonetizationEvent {
    String eventName;
    Integer adsCampaignId;
    Integer adsSubcampaignId;
    String adsType;
    Integer adsCostId;
    String pageName;
    String osName;
    EventProperties properties;
    Long eventTimestamp;
    EventTraits traits;

    public MonetizationEvent(JumboMonetizationEvent jumboMonetizationEvent) {

        this.adsCampaignId = jumboMonetizationEvent.getProperties().getAdsCampaignId();
        this.adsSubcampaignId = jumboMonetizationEvent.getProperties().getAdsSubcampaignId();
        this.adsType = jumboMonetizationEvent.getProperties().getAdsType();
        this.adsCostId = jumboMonetizationEvent.getProperties().getAdsCostId();
        this.pageName = jumboMonetizationEvent.getProperties().getPageName();
        this.properties = jumboMonetizationEvent.getProperties();
        this.eventName = jumboMonetizationEvent.getEventName();
        this.osName = getOsName(jumboMonetizationEvent.getSource());
        this.eventTimestamp = jumboMonetizationEvent.getEventTimestamp();
        this.traits = jumboMonetizationEvent.getTraits();
    }

    private String getOsName(String osName) {
        if (osName == null) {
            return "";
        } else {
            return osName.toLowerCase();
        }
    }
}
