package com.blinkit.corestreams.adsmonetization.events;

import lombok.Getter;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.annotation.JsonProperty;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.annotation.JsonSerialize;

@JsonSerialize
@Getter
@JsonIgnoreProperties(ignoreUnknown = true)
public class EventProperties {

    @JsonProperty("ads_campaign_id")
    Integer adsCampaignId = -1;

    @JsonProperty("ads_type")
    String adsType = "";

    @JsonProperty("ads_subcampaign_id")
    Integer adsSubcampaignId = -1;

    @JsonProperty("ads_cost_id")
    Integer adsCostId = -1;

    @JsonProperty("page_name")
    String pageName = "";
}
