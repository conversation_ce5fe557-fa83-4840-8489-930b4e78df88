name: Warehouse Outbound Metrics
image: 442534439095.dkr.ecr.ap-southeast-1.amazonaws.com/data/flink-streams/core-streams/warehouse-outbound-metrics
flink_config:
  taskmanager.memory.process.size: 2048m
  jobmanager.memory.process.size: 1024m
  taskmanager.memory.managed.size: 0
  taskmanager.numberOfTaskSlots: 2
  parallelism.default: 2
  process.working-dir: warehouse-outbound-metrics/process
  state.backend.local-recovery: true
  taskmanager.resource-id: TaskManager_WarehouseOutboundMetrics
deployment_config:
  kubernetes.rest-service.exposed.type: NodePort
  metrics.reporters: prom
  metrics.reporter.prom.factory.class: org.apache.flink.metrics.prometheus.PrometheusReporterFactory
allow_non_restored_state: False # Set to True when changes are made to Operator
codeartifact_access: True