# Properties for production environment

#Checkpoints
CHECKPOINTS_STORAGE_LOCATION = s3a://grofers-test-dse-singapore/flink-streams/core-streams/warehouse-outbound-metrics/checkpoints/

# Events Source
EVENTS_SOURCE_BROKERS = ****************************************.groupblinkit-agent.kafka.discoveryv2.prod-y.ap-southeast-1.warpstream.com:9092
EVENTS_SOURCE_CONSUMER_GROUP_ID = corestreams.wms.test.warehouse-outbound-metrics.group-v1
WAREHOUSE_ORDER_EVENTS_SOURCE_TOPIC = jumbo_transformed.blinkit.warehouse_order_events
WAREHOUSE_PICK_LIST_SOURCE_TOPIC = jumbo_transformed.blinkit.warehouse_pick_list_events
WAREHOUSE_PACKAGING_ACTIVITY_SOURCE_TOPIC = jumbo_transformed.blinkit.warehouse_packaging_activity_events
WAREHOUSE_OUTBOUND_CONTAINER_SOURCE_TOPIC = jumbo_transformed.blinkit.warehouse_outbound_container_events

# Events Sink
WAREHOUSE_METRICS_SINK_BROKERS = b-1.testdatacommon.vkkz3t.c5.kafka.ap-southeast-1.amazonaws.com:9092,b-2.testdatacommon.vkkz3t.c5.kafka.ap-southeast-1.amazonaws.com:9092
WAREHOUSE_OUTBOUND_SINK_TOPIC = corestreams.wms.warehouse-outbound-metrics
