package com.grofers.corestreams.warehouseoutboundmetrics.configs;

public class JobConfig {
    public static String EVENTS_SOURCE_BROKERS;
    public static String EVENTS_SOURCE_CONSUMER_GROUP_ID;
    public static String WAREHOUSE_ORDER_EVENTS_SOURCE_TOPIC;
    public static String WAREHOUSE_PICK_LIST_SOURCE_TOPIC;
    public static String WAREHOUSE_PACKAGING_ACTIVITY_SOURCE_TOPIC;
    public static String WAREHOUSE_OUTBOUND_CONTAINER_SOURCE_TOPIC;
    public static String WAREHOUSE_METRICS_SINK_BROKERS;
    public static String WAREHOUSE_OUTBOUND_SINK_TOPIC;
    public static String CHECKPOINTS_STORAGE_LOCATION;

    public JobConfig(
            String EVENTS_SOURCE_BROKERS,
            String EVENTS_SOURCE_CONSUMER_GROUP_ID,
            String WAREHOUSE_ORDER_EVENTS_SOURCE_TOPIC,
            String WAREHOUSE_PICK_LIST_SOURCE_TOPIC,
            String WAREHOUSE_PACKAGING_ACTIVITY_SOURCE_TOPIC,
            String WAREHOUSE_OUTBOUND_CONTAINER_SOURCE_TOPIC,
            String WAREHOUSE_METRICS_SINK_BROKERS,
            String WAREHOUSE_OUTBOUND_SINK_TOPIC,
            String CHECKPOINTS_STORAGE_LOCATION) {
        JobConfig.EVENTS_SOURCE_BROKERS = EVENTS_SOURCE_BROKERS;
        JobConfig.EVENTS_SOURCE_CONSUMER_GROUP_ID = EVENTS_SOURCE_CONSUMER_GROUP_ID;
        JobConfig.WAREHOUSE_ORDER_EVENTS_SOURCE_TOPIC = WAREHOUSE_ORDER_EVENTS_SOURCE_TOPIC;
        JobConfig.WAREHOUSE_PICK_LIST_SOURCE_TOPIC = WAREHOUSE_PICK_LIST_SOURCE_TOPIC;
        JobConfig.WAREHOUSE_PACKAGING_ACTIVITY_SOURCE_TOPIC =
                WAREHOUSE_PACKAGING_ACTIVITY_SOURCE_TOPIC;
        JobConfig.WAREHOUSE_OUTBOUND_CONTAINER_SOURCE_TOPIC =
                WAREHOUSE_OUTBOUND_CONTAINER_SOURCE_TOPIC;
        JobConfig.WAREHOUSE_METRICS_SINK_BROKERS = WAREHOUSE_METRICS_SINK_BROKERS;
        JobConfig.WAREHOUSE_OUTBOUND_SINK_TOPIC = WAREHOUSE_OUTBOUND_SINK_TOPIC;
        JobConfig.CHECKPOINTS_STORAGE_LOCATION = CHECKPOINTS_STORAGE_LOCATION;
    }
}
