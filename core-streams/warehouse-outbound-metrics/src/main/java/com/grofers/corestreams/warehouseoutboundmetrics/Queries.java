package com.grofers.corestreams.warehouseoutboundmetrics;

public class Queries {
    public static final String WAREHOUSE_ORDER_EVENTS =
            "CREATE TABLE IF NOT EXISTS warehouse_order_events (event_ts STRING,"
                + " ingestion_timestamp BIGINT, `timestamp` BIGINT, event_type VARCHAR(100),"
                + " order_details ROW(outbound_demand_id STRING, outlet_id VARCHAR(25),"
                + " destination_outlet_id VARCHAR(25), merchant_id VARCHAR(25), dispatch_time"
                + " STRING, order_id VARCHAR(25), external_order_id VARCHAR(25), order_type"
                + " VARCHAR(50), state VARCHAR(50), source_entity_vendor_id VARCHAR(25),"
                + " source_entity_vendor_name STRING), order_additional_details ROW(sku_quantity"
                + " VARCHAR(25), sku_count VARCHAR(25), cancellation_source String,"
                + " cancelled_items ARRAY<ROW<item_id VARCHAR(25), quantity VARCHAR(25)>>,"
                + " item_details ARRAY<ROW<item_id VARCHAR(25), quantity VARCHAR(25)>>)) WITH"
                + " ('connector' = 'kafka', 'format' = 'json', 'topic' = '%s',"
                + " 'properties.bootstrap.servers' = '%s', 'properties.group.id' = '%s',"
                + " 'scan.startup.mode' = 'group-offsets', 'json.timestamp-format.standard' ="
                + " 'ISO-8601', 'properties.auto.offset.reset' = 'latest')";

    public static final String WAREHOUSE_PICK_LIST_EVENTS =
            "CREATE TABLE IF NOT EXISTS warehouse_pick_list_events (event_ts STRING,"
                + " ingestion_timestamp BIGINT, `timestamp` BIGINT, event_type VARCHAR(100),"
                + " pick_list_details ROW(id VARCHAR(25), wave_id VARCHAR(25), outlet_id"
                + " VARCHAR(25), merchant_id VARCHAR(25), order_type VARCHAR(100), state"
                + " VARCHAR(100), type VARCHAR(100), schedule_time STRING, demand_id STRING,"
                + " destination_outlet_id VARCHAR(25), picking_zone_identifier STRING, sku_count"
                + " VARCHAR(25), sku_qty VARCHAR(25), demand_type STRING),"
                + " pick_list_additional_details ROW(pick_list_container_mapping_details"
                + " ARRAY<ROW<container_id STRING, container_type STRING,"
                + " container_priority_details ROW(priority_type STRING, score DOUBLE)>>,"
                + " pick_list_item_details ARRAY<ROW<pick_list_item_id VARCHAR(25), item_id"
                + " VARCHAR(25), state VARCHAR(50), entity_vendor_id VARCHAR(25), initial_quantity"
                + " VARCHAR(25), required_quantity VARCHAR(25), picked_quantity VARCHAR(25),"
                + " cancelled_quantity VARCHAR(25), item_processing_type STRING,"
                + " suggested_location_ids ARRAY<VARCHAR(25)>>>, discarded_location_details"
                + " ROW(location_id VARCHAR(25), location_name STRING, aisle_name STRING, `floor`"
                + " VARCHAR(25), put_away_zone_id STRING, picking_zone_id STRING), source STRING,"
                + " source_meta ROW(order_id STRING, order_type STRING), cancelled_items"
                + " ARRAY<ROW<pick_list_item_id VARCHAR(25), item_id VARCHAR(25),"
                + " cancelled_quantity VARCHAR(25)>>)) WITH ('connector' = 'kafka', 'format' ="
                + " 'json', 'topic' = '%s', 'properties.bootstrap.servers' = '%s',"
                + " 'properties.group.id' = '%s', 'scan.startup.mode' = 'group-offsets',"
                + " 'json.timestamp-format.standard' = 'ISO-8601', 'properties.auto.offset.reset'"
                + " = 'latest')";

    public static final String WAREHOUSE_PACKAGING_ACTIVITY_EVENTS =
            "CREATE TABLE IF NOT EXISTS warehouse_packaging_activity_events (event_ts STRING,"
                + " ingestion_timestamp BIGINT, `timestamp` BIGINT, event_type VARCHAR(100),"
                + " packaging_process_type STRING, activity_additional_details ROW(entity"
                + " ROW(external_entity_id STRING, external_entity_type STRING, outlet_id"
                + " VARCHAR(25), demand_id STRING, destination_outlet_id VARCHAR(25),"
                + " dispatch_time STRING, picking_zone_identifier STRING, picking_completed_at"
                + " STRING), entity_container_item_mappings ARRAY<ROW<entity_id VARCHAR(25),"
                + " container_id STRING, variant_id STRING, quantity_picked VARCHAR(25),"
                + " quantity_packed VARCHAR(25)>>), activity_details ROW(id VARCHAR(25), outlet_id"
                + " VARCHAR(25), packaging_dropzone_id STRING)) WITH ('connector' = 'kafka',"
                + " 'format' = 'json', 'topic' = '%s', 'properties.bootstrap.servers' = '%s',"
                + " 'properties.group.id' = '%s', 'scan.startup.mode' = 'group-offsets',"
                + " 'json.timestamp-format.standard' = 'ISO-8601', 'properties.auto.offset.reset'"
                + " = 'latest')";

    public static final String WAREHOUSE_OUTBOUND_CONTAINER_EVENTS =
            "CREATE TABLE IF NOT EXISTS warehouse_outbound_container_events (event_ts STRING,"
                    + " ingestion_timestamp BIGINT, `timestamp` BIGINT, event_type VARCHAR(100), id"
                    + " VARCHAR(25), container_id STRING, type VARCHAR(30), state VARCHAR(30),"
                    + " outlet_id VARCHAR(25), current_zone STRING, dispatch_time STRING,"
                    + " destination_outlet_id VARCHAR(25), handling_type STRING, outbound_demand_id"
                    + " STRING, billing_status STRING, container_key STRING, is_partial BOOLEAN,"
                    + " handling_tags ARRAY<STRING>, item_details ROW(packed_quantity VARCHAR(25)))"
                    + " WITH  ('connector' = 'kafka', 'format' = 'json', 'topic' = '%s',"
                    + " 'properties.bootstrap.servers' = '%s', 'properties.group.id' = '%s',"
                    + " 'scan.startup.mode' = 'group-offsets', 'json.timestamp-format.standard' ="
                    + " 'ISO-8601', 'properties.auto.offset.reset' = 'latest')";

    public static final String WAREHOUSE_OUTBOUND_CREATE_TABLE =
            "CREATE TABLE IF NOT EXISTS warehouse_outbound_events (demand_id STRING, outlet_id"
                + " VARCHAR(25), destination_outlet_id VARCHAR(25), merchant_id VARCHAR(25),"
                + " dispatch_time BIGINT, unique_id STRING, unique_columns STRING, event_name"
                + " VARCHAR(100), sku_quantity BIGINT, sku_count INT, insert_ts BIGINT,"
                + " is_packaging BOOLEAN, picking_zone STRING, picklist_type VARCHAR(100), PRIMARY"
                + " KEY (demand_id, unique_id, event_name) NOT ENFORCED ) WITH ('connector' ="
                + " 'upsert-kafka', 'key.format' = 'json', 'value.format' = 'json', 'topic' ="
                + " '%s', 'properties.bootstrap.servers' = '%s', 'properties.request.timeout.ms' ="
                + " '300000')";

    public static final String INSERT_WAREHOUSE_OUTBOUND_EVENTS =
            // warehouse_order_events
            "INSERT INTO warehouse_outbound_events SELECT order_details.outbound_demand_id AS"
                + " demand_id, order_details.outlet_id AS outlet_id,"
                + " order_details.destination_outlet_id AS destination_outlet_id,"
                + " order_details.merchant_id AS merchant_id,"
                + " GetUnixTimeFromString(order_details.dispatch_time) AS dispatch_time, CASE"
                + " event_type WHEN 'ORDER_CREATED' THEN order_details.order_id WHEN"
                + " 'ORDER_CANCELLED' THEN"
                + " order_details.order_id||'-'||SHA1(JSON_STRING(order_additional_details.cancelled_items)||event_ts)"
                + " END AS unique_id, 'order_id' AS unique_columns, event_type AS event_name, CASE"
                + " event_type WHEN 'ORDER_CREATED' THEN"
                + " CAST(order_additional_details.sku_quantity AS BIGINT) WHEN 'ORDER_CANCELLED'"
                + " THEN SumOfItemQuantityFunction(order_additional_details.cancelled_items,"
                + " 'quantity') END AS sku_quantity, CASE event_type WHEN 'ORDER_CREATED' THEN"
                + " CAST(order_additional_details.sku_count AS INT) WHEN 'ORDER_CANCELLED' THEN"
                + " CARDINALITY(order_additional_details.cancelled_items) END AS sku_count,"
                + " GetUnixTimeFromString(event_ts) AS insert_ts,"
                + " order_details.source_entity_vendor_name LIKE '%BCPL%' AS is_packaging,"
                + " CAST(NULL AS STRING) AS picking_zone, CAST(NULL AS VARCHAR(100)) AS"
                + " picklist_type FROM warehouse_order_events WHERE event_type IN"
                + " ('ORDER_CREATED', 'ORDER_CANCELLED') AND order_details.order_type IN ('STO')"
                    // warehouse_pick_list_events
                    + " UNION ALL SELECT pick_list_details.demand_id AS demand_id,"
                    + " pick_list_details.outlet_id AS outlet_id,"
                    + " pick_list_details.destination_outlet_id AS destination_outlet_id,"
                    + " pick_list_details.merchant_id AS merchant_id,"
                    + " GetUnixTimeFromString(pick_list_details.schedule_time) AS dispatch_time,"
                    + " CASE event_type WHEN 'PICK_LIST_ITEM_NOT_FOUND','PICK_LIST_PICK_ITEM' THEN"
                    + " GetElementFromPickListDetails(pick_list_additional_details.pick_list_item_details,"
                    + " 'pick_list_item_id', 0) WHEN 'PICK_LIST_ITEM_LOCATION_NOT_FOUND' THEN"
                    + " pick_list_details.id||'-'||pick_list_additional_details.discarded_location_details.location_id"
                    + " WHEN 'PICK_LIST_CANCELLED', 'PICK_LIST_PARTIALLY_CANCELLED' THEN"
                    + " pick_list_details.id||'-'||SHA1(JSON_STRING(pick_list_additional_details.cancelled_items))||'-'||event_ts"
                    + " WHEN 'PICK_LIST_CREATED', 'PICK_LIST_COMPLETED' THEN pick_list_details.id"
                    + " END AS unique_id, CASE event_type WHEN 'PICK_LIST_ITEM_NOT_FOUND',"
                    + " 'PICK_LIST_PICK_ITEM' THEN 'pick_list_item_id' WHEN"
                    + " 'PICK_LIST_ITEM_LOCATION_NOT_FOUND' THEN 'id-location_id' WHEN"
                    + " 'PICK_LIST_CREATED', 'PICK_LIST_CANCELLED',"
                    + " 'PICK_LIST_PARTIALLY_CANCELLED', 'PICK_LIST_COMPLETED' THEN 'id' END AS"
                    + " unique_columns, event_type AS event_name, CASE event_type WHEN"
                    + " 'PICK_LIST_ITEM_NOT_FOUND', 'PICK_LIST_PICK_ITEM' THEN"
                    + " CAST(GetElementFromPickListDetails(pick_list_additional_details.pick_list_item_details,"
                    + " 'picked_quantity', 0) AS BIGINT) WHEN 'PICK_LIST_ITEM_LOCATION_NOT_FOUND'"
                    + " THEN 1 WHEN 'PICK_LIST_CANCELLED', 'PICK_LIST_PARTIALLY_CANCELLED' THEN"
                    + " SumOfCancelledItemQuantityFunction(pick_list_additional_details.cancelled_items,"
                    + " 'cancelled_quantity') WHEN 'PICK_LIST_CREATED', 'PICK_LIST_COMPLETED' THEN"
                    + " CAST(pick_list_details.sku_qty AS BIGINT) END AS sku_quantity, CASE"
                    + " event_type WHEN 'PICK_LIST_CREATED' THEN CAST(pick_list_details.sku_count"
                    + " AS INT) WHEN 'PICK_LIST_COMPLETED' THEN"
                    + " CARDINALITY(pick_list_additional_details.pick_list_container_mapping_details)"
                    + " WHEN 'PICK_LIST_CANCELLED', 'PICK_LIST_PARTIALLY_CANCELLED' THEN"
                    + " CARDINALITY(pick_list_additional_details.cancelled_items) ELSE 1 END AS"
                    + " sku_count, GetUnixTimeFromString(event_ts) AS insert_ts,"
                    + " pick_list_details.picking_zone_identifier LIKE '%PACKAGING%' OR"
                    + " pick_list_details.picking_zone_identifier LIKE '%RIDER_ASSET%' AS"
                    + " is_packaging, pick_list_details.picking_zone_identifier AS picking_zone,"
                    + " pick_list_details.type AS picklist_type FROM warehouse_pick_list_events"
                    + " WHERE event_type IN ('PICK_LIST_ITEM_NOT_FOUND', 'PICK_LIST_PICK_ITEM',"
                    + " 'PICK_LIST_ITEM_LOCATION_NOT_FOUND', 'PICK_LIST_CREATED',"
                    + " 'PICK_LIST_CANCELLED', 'PICK_LIST_PARTIALLY_CANCELLED',"
                    + " 'PICK_LIST_COMPLETED') AND pick_list_details.demand_type = 'SINGLE' AND"
                    + " pick_list_details.order_type = 'STO' UNION ALL"
                    // warehouse_packaging_events
                    + " SELECT activity_additional_details.entity.demand_id AS demand_id,"
                    + " activity_additional_details.entity.outlet_id AS outlet_id,"
                    + " activity_additional_details.entity.destination_outlet_id AS"
                    + " destination_outlet_id, CAST(NULL AS VARCHAR(25)) AS merchant_id,"
                    + " GetUnixTimeFromString(activity_additional_details.entity.dispatch_time) AS"
                    + " dispatch_time,"
                    + " GetElementFromContainerItemMappings(activity_additional_details.entity_container_item_mappings,"
                    + " 'container_id', 0) AS unique_id, 'container_id' AS unique_columns,"
                    + " event_type AS event_name,"
                    + " SumOfContainerQuantityFunction(activity_additional_details.entity_container_item_mappings)"
                    + " AS sku_quantity, 1 AS sku_count, GetUnixTimeFromString(event_ts) AS"
                    + " insert_ts, activity_additional_details.entity.picking_zone_identifier LIKE"
                    + " '%PACKAGING%' OR"
                    + " activity_additional_details.entity.picking_zone_identifier LIKE"
                    + " '%RIDER_ASSET%' AS is_packaging,"
                    + " activity_additional_details.entity.picking_zone_identifier AS"
                    + " picking_zone, CAST(NULL AS VARCHAR(100)) AS picklist_type FROM"
                    + " warehouse_packaging_activity_events WHERE event_type ="
                    + " 'PACKAGING_ACTIVITY_COMPLETED' AND"
                    + " activity_additional_details.entity_container_item_mappings IS NOT NULL"
                    + " UNION ALL"
                    // warehouse_outbound_events
                    + " SELECT outbound_demand_id AS demand_id, outlet_id, destination_outlet_id,"
                    + " CAST(NULL AS VARCHAR(25)) AS merchant_id,"
                    + " GetUnixTimeFromString(dispatch_time) AS dispatch_time, container_id AS"
                    + " unique_id, 'container_id' AS unique_columns, event_type AS event_name,"
                    + " CAST(item_details.packed_quantity AS BIGINT) AS sku_quantity, 1 AS"
                    + " sku_count, GetUnixTimeFromString(event_ts) AS insert_ts,"
                    + " ARRAY_CONTAINS(handling_tags, 'PACKAGING_MATERIAL') AS is_packaging,"
                    + " current_zone AS picking_zone, CAST(NULL AS VARCHAR(100)) AS picklist_type"
                    + " FROM warehouse_outbound_container_events WHERE event_type IN"
                    + " ('CONTAINER_SORTED', 'CONTAINER_DISPATCHED')";
}
