/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.grofers.corestreams.warehouseoutboundmetrics;

import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.contrib.streaming.state.EmbeddedRocksDBStateBackend;
import org.apache.flink.streaming.api.CheckpointingMode;
import org.apache.flink.streaming.api.environment.CheckpointConfig;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.table.annotation.DataTypeHint;
import org.apache.flink.table.api.bridge.java.StreamTableEnvironment;
import org.apache.flink.table.functions.ScalarFunction;
import org.apache.flink.types.Row;

import com.grofers.corestreams.warehouseoutboundmetrics.configs.JobConfigManager;

import java.io.IOException;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeFormatterBuilder;
import java.time.temporal.ChronoField;
import java.util.*;

import static com.grofers.corestreams.warehouseoutboundmetrics.Queries.*;
import static com.grofers.corestreams.warehouseoutboundmetrics.configs.JobConfig.*;

/**
 * Warehouse Outbound Metrics
 *
 * <p>For a tutorial how to write a Flink application, check the tutorials and examples on the <a
 * href="https://flink.apache.org">Flink Website</a>.
 *
 * <p>To package your application into a JAR file for execution, run 'mvn clean package' on the
 * command line.
 *
 * <p>If you change the name of the main class (with the public static void main(String[] args))
 * method, change the respective entry in the POM.xml file (simply search for 'mainClass').
 */
public class WarehouseOutboundMetrics {

    public static Integer sumOfFieldInRowArray(Row[] arr, String key) {
        if (arr != null) {
            int sum = 0;
            for (Row i : arr) {
                if (i.getFieldAs(key) != null) {
                    sum += Integer.parseInt(i.getFieldAs(key));
                }
            }
            return sum;
        }
        return null;
    }

    public static class SumOfContainerQuantityFunction extends ScalarFunction {
        public Long eval(
                @DataTypeHint(
                                "ARRAY<ROW<entity_id VARCHAR(25), container_id STRING, variant_id"
                                        + " STRING, quantity_picked VARCHAR(25), quantity_packed"
                                        + " VARCHAR(25)>>")
                        Row[] arr) {
            if (arr != null) {
                long sum = 0;
                for (Row i : arr) {
                    if (i.getFieldAs("quantity_picked") != null
                            && i.getFieldAs("quantity_packed") != null) {
                        sum +=
                                Math.max(
                                        Long.parseLong(i.getFieldAs("quantity_picked")),
                                        Long.parseLong(i.getFieldAs("quantity_packed")));
                    }
                }
                return sum;
            }
            return null;
        }
    }

    public static class SumOfItemQuantityFunction extends ScalarFunction {
        public Integer eval(
                @DataTypeHint("ARRAY<ROW<item_id VARCHAR(25), quantity VARCHAR(25)>>") Row[] arr,
                String key) {
            return sumOfFieldInRowArray(arr, key);
        }
    }

    public static class SumOfCancelledItemQuantityFunction extends ScalarFunction {
        public Integer eval(
                @DataTypeHint(
                                "ARRAY<ROW<pick_list_item_id VARCHAR(25), item_id VARCHAR(25),"
                                        + " cancelled_quantity VARCHAR(25)>>")
                        Row[] arr,
                String key) {
            return sumOfFieldInRowArray(arr, key);
        }
    }

    public static class GetUnixTimeFromString extends ScalarFunction {
        public Long eval(String s) {
            return getEpochFromTimestamp(s);
        }
    }

    public static String getElementFormRowArray(Row[] arr, String key, Integer element) {
        if (arr != null) {
            int cnt = 0;
            for (Row i : arr) {
                if (cnt == element) {
                    return i.getFieldAs(key).toString();
                }
                cnt += 1;
            }
        }
        return null;
    }

    public static class GetElementFromContainerItemMappings extends ScalarFunction {
        public String eval(
                @DataTypeHint(
                                "ARRAY<ROW<entity_id VARCHAR(25), container_id STRING, variant_id"
                                        + " STRING, quantity_picked VARCHAR(25), quantity_packed"
                                        + " VARCHAR(25)>>")
                        Row[] arr,
                String key,
                Integer element) {
            return getElementFormRowArray(arr, key, element);
        }
    }

    public static class GetElementFromPickListDetails extends ScalarFunction {
        public String eval(
                @DataTypeHint(
                                "ARRAY<ROW<pick_list_item_id VARCHAR(25), item_id VARCHAR(25),"
                                        + " state VARCHAR(50), entity_vendor_id VARCHAR(25),"
                                        + " initial_quantity VARCHAR(25), required_quantity"
                                        + " VARCHAR(25), picked_quantity VARCHAR(25),"
                                        + " cancelled_quantity VARCHAR(25), item_processing_type"
                                        + " STRING, suggested_location_ids ARRAY<VARCHAR(25)>>>")
                        Row[] arr,
                String key,
                Integer element) {
            return getElementFormRowArray(arr, key, element);
        }
    }

    private static Long getEpochFromTimestamp(String utcTimestamp) {
        if (utcTimestamp == null) return null;
        // Check if the utcTimestamp is in the form of an epoch
        try {
            long epochTimestamp = Long.parseLong(utcTimestamp);
            // If the parsing succeeds, it means the timestamp is already in epoch format
            return epochTimestamp;
        } catch (NumberFormatException e) {
            // If parsing fails, it means the timestamp is in string format
            // Define two formats to handle both cases
            DateTimeFormatter formatter =
                    new DateTimeFormatterBuilder()
                            .appendPattern("yyyy-MM-dd'T'HH:mm:ss")
                            .optionalStart() // Optional start for milliseconds
                            .appendFraction(ChronoField.NANO_OF_SECOND, 0, 9, true)
                            .optionalEnd() // Optional end for milliseconds
                            .optionalStart() // Optional start for the timezone offset
                            .appendOffset("+HHMM", "Z") // Offset and 'Z' are optional
                            .optionalEnd() // Optional end for the timezone offset
                            .toFormatter();

            ZonedDateTime zonedDateTimeDate;
            try {
                // First try to parse with the formatter without milliseconds and 'Z'
                zonedDateTimeDate =
                        LocalDateTime.parse(utcTimestamp, formatter).atZone(ZoneId.of("UTC"));
                return zonedDateTimeDate.toInstant().toEpochMilli();
            } catch (java.time.format.DateTimeParseException ex) {
                return null;
            }
        }
    }

    public static void main(String[] args)
            throws JobConfigManager.InvalidEnvironmentException, IOException {

        final StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment();
        final StreamTableEnvironment tEnv = StreamTableEnvironment.create(env);

        tEnv.createTemporarySystemFunction(
                "SumOfContainerQuantityFunction", SumOfContainerQuantityFunction.class);
        tEnv.createTemporarySystemFunction(
                "SumOfItemQuantityFunction", SumOfItemQuantityFunction.class);
        tEnv.createTemporarySystemFunction(
                "SumOfCancelledItemQuantityFunction", SumOfCancelledItemQuantityFunction.class);
        tEnv.createTemporarySystemFunction("GetUnixTimeFromString", GetUnixTimeFromString.class);
        tEnv.createTemporarySystemFunction(
                "GetElementFromPickListDetails", GetElementFromPickListDetails.class);
        tEnv.createTemporarySystemFunction(
                "GetElementFromContainerItemMappings", GetElementFromContainerItemMappings.class);

        Configuration configuration = tEnv.getConfig().getConfiguration();

        configuration.setString("table.exec.source.idle-timeout", "300 s");
        configuration.setString("table.exec.state.ttl", "900 s");

        // local-global aggregation depends on mini-batch is enabled
        configuration.setString("table.exec.mini-batch.enabled", "true");
        configuration.setString("table.exec.mini-batch.allow-latency", "1 s");
        configuration.setString("table.exec.mini-batch.size", "500");

        ParameterTool params = ParameterTool.fromArgs(args);
        String userEnv = params.getRequired("environment");

        // Get job configs
        JobConfigManager.setJobConfigsPath(userEnv);
        String jobPropertiesPath = JobConfigManager.getJobConfigsPath(userEnv);
        JobConfigManager.setJobConfigs(tEnv, jobPropertiesPath);
        JobConfigManager.getJobConfigs(tEnv);

        // Checkpoint Configs
        env.enableCheckpointing(600000);
        // set mode to exactly-once (default)
        env.getCheckpointConfig().setCheckpointingMode(CheckpointingMode.EXACTLY_ONCE);
        // make sure 900000 ms of progress happen between checkpoints
        env.getCheckpointConfig().setMinPauseBetweenCheckpoints(300000);
        // checkpoints have to complete within this minute, or are discarded
        env.getCheckpointConfig().setCheckpointTimeout(300000);
        // only two consecutive checkpoint failures are tolerated
        env.getCheckpointConfig().setTolerableCheckpointFailureNumber(2);
        // allow only one checkpoint to be in progress at the same time
        env.getCheckpointConfig().setMaxConcurrentCheckpoints(1);
        // enable externalized checkpoints which are retained after job cancellation
        env.getCheckpointConfig()
                .setExternalizedCheckpointCleanup(
                        CheckpointConfig.ExternalizedCheckpointCleanup.DELETE_ON_CANCELLATION);
        // enable unaligned checkpoints
        env.getCheckpointConfig().enableUnalignedCheckpoints();
        // sets a RocksDB checkpoint storage where checkpoint snapshots will be incrementally
        // written
        env.setStateBackend(new EmbeddedRocksDBStateBackend(true));
        env.getCheckpointConfig().setCheckpointStorage(CHECKPOINTS_STORAGE_LOCATION);

        String warehouseOrderEventsCreateTable =
                String.format(
                        WAREHOUSE_ORDER_EVENTS,
                        WAREHOUSE_ORDER_EVENTS_SOURCE_TOPIC,
                        EVENTS_SOURCE_BROKERS,
                        EVENTS_SOURCE_CONSUMER_GROUP_ID);
        tEnv.executeSql(warehouseOrderEventsCreateTable);

        String warehousePickListEventsCreateTable =
                String.format(
                        WAREHOUSE_PICK_LIST_EVENTS,
                        WAREHOUSE_PICK_LIST_SOURCE_TOPIC,
                        EVENTS_SOURCE_BROKERS,
                        EVENTS_SOURCE_CONSUMER_GROUP_ID);
        tEnv.executeSql(warehousePickListEventsCreateTable);

        String warehousePackagingEventsCreateTable =
                String.format(
                        WAREHOUSE_PACKAGING_ACTIVITY_EVENTS,
                        WAREHOUSE_PACKAGING_ACTIVITY_SOURCE_TOPIC,
                        EVENTS_SOURCE_BROKERS,
                        EVENTS_SOURCE_CONSUMER_GROUP_ID);
        tEnv.executeSql(warehousePackagingEventsCreateTable);

        String warehouseOutboundConatinerEventsCreateTable =
                String.format(
                        WAREHOUSE_OUTBOUND_CONTAINER_EVENTS,
                        WAREHOUSE_OUTBOUND_CONTAINER_SOURCE_TOPIC,
                        EVENTS_SOURCE_BROKERS,
                        EVENTS_SOURCE_CONSUMER_GROUP_ID);
        tEnv.executeSql(warehouseOutboundConatinerEventsCreateTable);

        String warehouseOutboundCreateTable =
                String.format(
                        WAREHOUSE_OUTBOUND_CREATE_TABLE,
                        WAREHOUSE_OUTBOUND_SINK_TOPIC,
                        WAREHOUSE_METRICS_SINK_BROKERS);
        tEnv.executeSql(warehouseOutboundCreateTable);

        // Execute program, beginning computation.
        tEnv.executeSql(INSERT_WAREHOUSE_OUTBOUND_EVENTS);
    }
}
