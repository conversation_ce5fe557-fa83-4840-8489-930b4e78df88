<settings xmlns="http://maven.apache.org/SETTINGS/1.0.0"
          xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
          xsi:schemaLocation="http://maven.apache.org/SETTINGS/1.0.0 http://maven.apache.org/xsd/settings-1.0.0.xsd">    <servers>

    <server>
        <id>codeartifact</id>
        <username>aws</username>
        <password>${env.CODEARTIFACT_AUTH_TOKEN}</password>
    </server>
</servers>
    <profiles>
        <profile>
            <id>codeartifact</id>
            <repositories>
                <repository>
                    <id>codeartifact</id>
                    <url>https://grofers-442534439095.d.codeartifact.ap-southeast-1.amazonaws.com/maven/data-gandalf/</url>
                </repository>
            </repositories>
        </profile>
    </profiles>
    <activeProfiles>
        <activeProfile>codeartifact</activeProfile>
    </activeProfiles>
</settings>