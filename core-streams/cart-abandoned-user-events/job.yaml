name: Cart Abandoned Users
image: 442534439095.dkr.ecr.ap-southeast-1.amazonaws.com/data/flink-streams/core-streams/cart-abandoned-user-events
flink_config:
  jobmanager.memory.process.size: 1024m
  taskmanager.memory.process.size: 4096m
  taskmanager.numberOfTaskSlots: 2
  process.working-dir: cart-abandoned-user-events/process
  state.backend.local-recovery: true
  taskmanager.resource-id: TaskManager_CartAbandonedUserEvents
deployment_config:
  kubernetes.rest-service.exposed.type: NodePort
  metrics.reporters: prom
  metrics.reporter.prom.factory.class: org.apache.flink.metrics.prometheus.PrometheusReporterFactory
allow_non_restored_state: False # Set to True when changes are made to Operator
codeartifact_access: True
