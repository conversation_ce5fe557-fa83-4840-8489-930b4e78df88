
# Properties for stage environment

#Checkpoints
CHECKPOINTS_STORAGE_LOCATION = s3a://grofers-test-dse-singapore/flink-streams/core-streams/cart-abandon-jumbo-testing/checkpoints/

# Events Source
EVENTS_SOURCE_BROKERS = vpce-06233f70360f757f8-enprgsf3.vpce-svc-0dcaa5761ef958410.ap-southeast-1.vpce.amazonaws.com:9093,vpce-06233f70360f757f8-enprgsf3.vpce-svc-0dcaa5761ef958410.ap-southeast-1.vpce.amazonaws.com:9094,vpce-06233f70360f757f8-enprgsf3.vpce-svc-0dcaa5761ef958410.ap-southeast-1.vpce.amazonaws.com:9095
EVENTS_SOURCE_CONSUMER_GROUP_ID = cartabandonmetrics-jumbo-test-events-consumer_group-v8
EVENTS_IMPRESSION_CONSUMER_GROUP_ID = cartabandonmetrics-jumbo-test-events-impression-consumer_group-v1
EVENTS_CLICK_CONSUMER_GROUP_ID = cartabandonmetrics-jumbo-test-events-click-consumer_group-v1
EVENTS_SOURCE_CLICK_TOPIC = blinkit.blinkit_app_events
EVENTS_SOURCE_IMPRESSION_TOPIC = blinkit.blinkit_app_events

#PARALLELISM Configs
DEFAULT_GLOBAL_PARALLELISM = 3

#Watermark Idleness Configs
MAX_OUT_OF_ORDERNESS_IN_SECS = 60
IDLENESS_TIME_IN_SECS = 60
ALLOWED_LATENESS_IN_SECS = 0

#Window Configs
WINDOW_SIZE_IN_MINS = 10

# Events Sink
CART_ABANDON_EVENTS_SINK_BROKERS = b-1.testdatacommon.vkkz3t.c5.kafka.ap-southeast-1.amazonaws.com:9092,b-2.testdatacommon.vkkz3t.c5.kafka.ap-southeast-1.amazonaws.com:9092
CART_ABANDON_EVENTS_SINK_TOPIC = corestreams.marketing.cartabandonmetrics.stage