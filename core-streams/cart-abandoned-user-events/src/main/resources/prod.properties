# Properties for production environment

#Checkpoints
CHECKPOINTS_STORAGE_LOCATION = s3a://prod-data-flink-states/core-streams/cart-abandoned-user-events/checkpoints/

# Events Source
EVENTS_SOURCE_BROKERS = vpce-06233f70360f757f8-enprgsf3.vpce-svc-0dcaa5761ef958410.ap-southeast-1.vpce.amazonaws.com:9093,vpce-06233f70360f757f8-enprgsf3.vpce-svc-0dcaa5761ef958410.ap-southeast-1.vpce.amazonaws.com:9094,vpce-06233f70360f757f8-enprgsf3.vpce-svc-0dcaa5761ef958410.ap-southeast-1.vpce.amazonaws.com:9095
EVENTS_SOURCE_CONSUMER_GROUP_ID = corestreams.marketing.cart-abandoned-user-events.group-v1
EVENTS_IMPRESSION_CONSUMER_GROUP_ID = corestreams.marketing.cart-abandoned-impression-events.group-v1
EVENTS_CLICK_CONSUMER_GROUP_ID = corestreams.marketing.cart-abandoned-click-events.group-v1
EVENTS_SOURCE_CLICK_TOPIC = blinkit.click_events
EVENTS_SOURCE_IMPRESSION_TOPIC = blinkit.impression_events

#PARALLELISM Configs
DEFAULT_GLOBAL_PARALLELISM = 4

#Watermark Idleness Configs
MAX_OUT_OF_ORDERNESS_IN_SECS = 60
IDLENESS_TIME_IN_SECS = 60
ALLOWED_LATENESS_IN_SECS = 0

#Window Configs
WINDOW_SIZE_IN_MINS = 10

# Events Sink
CART_ABANDON_EVENTS_SINK_BROKERS = b-1.prod-data-corestr.uynfnv.c4.kafka.ap-southeast-1.amazonaws.com:9092,b-2.prod-data-corestr.uynfnv.c4.kafka.ap-southeast-1.amazonaws.com:9092,b-3.prod-data-corestr.uynfnv.c4.kafka.ap-southeast-1.amazonaws.com:9092
CART_ABANDON_EVENTS_SINK_TOPIC = corestreams.marketing.cart-abandoned-user-events
