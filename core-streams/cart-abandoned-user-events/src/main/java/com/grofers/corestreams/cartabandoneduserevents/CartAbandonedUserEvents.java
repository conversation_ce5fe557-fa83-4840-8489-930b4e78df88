/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.grofers.corestreams.cartabandoneduserevents;

import static java.time.Duration.ofSeconds;

import com.grofers.corestreams.cartabandoneduserevents.configs.JobConfig;
import com.grofers.corestreams.cartabandoneduserevents.configs.JobConfigManager;
import com.grofers.corestreams.cartabandoneduserevents.datatypes.input.CartAbandonEvent;
import com.grofers.corestreams.cartabandoneduserevents.datatypes.output.InactiveCartAbandonedUsers;
import com.grofers.corestreams.cartabandoneduserevents.processfunctions.GetCartAbandonedInactiveUserEvents;
import com.grofers.corestreams.cartabandoneduserevents.serdes.JSONValueDeserializationSchema;
import com.grofers.corestreams.cartabandoneduserevents.serdes.MobileCoreOrderingEventsSerializationSchema;
import com.grofers.corestreams.cartabandoneduserevents.util.EventsFilter;
import com.grofers.corestreams.cartabandoneduserevents.util.Constants;
import org.apache.flink.api.common.ExecutionConfig;
import org.apache.flink.api.common.JobExecutionResult;
import org.apache.flink.api.common.eventtime.WatermarkStrategy;
import org.apache.flink.api.connector.sink2.Sink;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.connector.base.DeliveryGuarantee;
import org.apache.flink.connector.kafka.sink.KafkaSink;
import org.apache.flink.connector.kafka.source.KafkaSource;
import org.apache.flink.connector.kafka.source.enumerator.initializer.OffsetsInitializer;
import org.apache.flink.contrib.streaming.state.EmbeddedRocksDBStateBackend;
import org.apache.flink.streaming.api.CheckpointingMode;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.datastream.DataStreamSource;
import org.apache.flink.streaming.api.environment.CheckpointConfig;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.streaming.api.windowing.assigners.EventTimeSessionWindows;
import org.apache.flink.streaming.api.windowing.time.Time;
import org.apache.kafka.clients.consumer.OffsetResetStrategy;

/**
 * Skeleton for a Flink DataStream Job.
 *
 * <p>For a tutorial how to write a Flink application, check the tutorials and examples on the <a
 * href="https://flink.apache.org">Flink Website</a>.
 *
 * <p>To package your application into a JAR file for execution, run 'mvn clean package' on the
 * command line.
 *
 * <p>If you change the name of the main class (with the public static void main(String[] args))
 * method, change the respective entry in the POM.xml file (simply search for 'mainClass').
 */
public class CartAbandonedUserEvents {

    private final DataStream<CartAbandonEvent> impressionEventsSource;
    private final Sink<InactiveCartAbandonedUsers> InactiveCartAbandonedUserEventsSink;

    public CartAbandonedUserEvents(
            DataStream<CartAbandonEvent> impressionEventsSource,
            Sink<InactiveCartAbandonedUsers> InactiveCartAbandonedUserEventsSink) {
        this.impressionEventsSource = impressionEventsSource;
        this.InactiveCartAbandonedUserEventsSink = InactiveCartAbandonedUserEventsSink;
    }

    public static void main(String[] args) throws Exception {
        // Sets up the execution environment, which is the main entry point
        // to building Flink applications.
        final StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment();

        // Parameters for environment
        ParameterTool params = ParameterTool.fromArgs(args);
        String userEnv = params.getRequired("environment");

        // Set PropertiesPath for userEnv
        JobConfigManager.setJobConfigsPath(userEnv);
        // Get PropertiesPath for userEnv
        String jobPropertiesPath = JobConfigManager.getJobConfigsPath(userEnv);
        // Fetch job config
        JobConfigManager.setJobConfigs(env, jobPropertiesPath);
        JobConfigManager.getJobConfigs(env);

        // Checkpoint Configs
        // start a checkpoint every 300000 ms (5 minutes)
        env.enableCheckpointing(300000);
        // set mode to exactly-once (default)
        env.getCheckpointConfig().setCheckpointingMode(CheckpointingMode.EXACTLY_ONCE);
        // make sure 120000 ms of progress happen between checkpoints
        env.getCheckpointConfig().setMinPauseBetweenCheckpoints(300000);
        // checkpoints have to complete within a minute, or are discarded
        env.getCheckpointConfig().setCheckpointTimeout(120000);
        // only two consecutive checkpoint failures are tolerated
        env.getCheckpointConfig().setTolerableCheckpointFailureNumber(2);
        // allow only one checkpoint to be in progress at the same time
        env.getCheckpointConfig().setMaxConcurrentCheckpoints(1);
        // enable externalized checkpoints which are retained after job cancellation
        env.getCheckpointConfig()
                .setExternalizedCheckpointCleanup(
                        CheckpointConfig.ExternalizedCheckpointCleanup.RETAIN_ON_CANCELLATION);
        // enables the experimental unaligned checkpoints
        env.getCheckpointConfig().enableUnalignedCheckpoints();
        // sets the checkpoint storage where checkpoint snapshots will be written
        env.setStateBackend(new EmbeddedRocksDBStateBackend(true));
        // sets the checkpoint storage where checkpoint snapshots will be written
        env.getCheckpointConfig().setCheckpointStorage(JobConfig.CHECKPOINTS_STORAGE_LOCATION);
        // Enable checkpoint compression
        env.setParallelism(JobConfig.DEFAULT_GLOBAL_PARALLELISM);
        ExecutionConfig executionConfig = new ExecutionConfig();
        executionConfig.setUseSnapshotCompression(true);

        KafkaSource<CartAbandonEvent> impressionEventsSource =
                KafkaSource.<CartAbandonEvent>builder()
                        .setBootstrapServers(JobConfig.EVENTS_SOURCE_BROKERS)
                        .setGroupId(JobConfig.EVENTS_IMPRESSION_CONSUMER_GROUP_ID)
                        .setValueOnlyDeserializer(new JSONValueDeserializationSchema())
                        .setStartingOffsets(
                                OffsetsInitializer.committedOffsets(OffsetResetStrategy.LATEST))
                        .setTopics(JobConfig.EVENTS_SOURCE_IMPRESSION_TOPIC)
                        .setProperty("partition.discovery.interval.ms", "600000")
                        .setProperty("reconnect.backoff.max.ms", "300000")
                        .setProperty("reconnect.backoff.ms", "60000")
                        .build();
        
        KafkaSource<CartAbandonEvent> clickEventsSource = 
                KafkaSource.<CartAbandonEvent>builder()
                        .setBootstrapServers(JobConfig.EVENTS_SOURCE_BROKERS)
                        .setGroupId(JobConfig.EVENTS_CLICK_CONSUMER_GROUP_ID)
                        .setValueOnlyDeserializer(new JSONValueDeserializationSchema())
                        .setStartingOffsets(
                                OffsetsInitializer.committedOffsets(OffsetResetStrategy.LATEST))
                        .setTopics(JobConfig.EVENTS_SOURCE_CLICK_TOPIC)
                        .setProperty("partition.discovery.interval.ms", "600000")
                        .setProperty("reconnect.backoff.max.ms", "300000")
                        .setProperty("reconnect.backoff.ms", "60000")
                        .build();

        WatermarkStrategy<CartAbandonEvent> cartAbandonEventsSourceWatermarkStrategy =
                WatermarkStrategy.<CartAbandonEvent>forBoundedOutOfOrderness(
                                ofSeconds(JobConfig.MAX_OUT_OF_ORDERNESS_IN_SECS))
                        .withIdleness(ofSeconds(JobConfig.IDLENESS_TIME_IN_SECS))
                        .withTimestampAssigner(
                                (cartAbandonEvent, timestamp) ->
                                        cartAbandonEvent.getEventTimestamp() * 1000);

        DataStream<CartAbandonEvent> impressionEventsStreamSource =
                env.fromSource(
                        impressionEventsSource,
                        cartAbandonEventsSourceWatermarkStrategy,
                        "impressionEventsSource")
                        .filter(new EventsFilter(Constants.CART_ABANDON_FILTER_IMPRESSION_SET));

        DataStream<CartAbandonEvent> clickEventsStreamSource =
                env.fromSource(
                        clickEventsSource,
                        cartAbandonEventsSourceWatermarkStrategy,
                        "clickEventsSource")
                        .filter(new EventsFilter(Constants.CART_ABANDON_FILTER_CLICK_SET));

        DataStream<CartAbandonEvent> unionStream = clickEventsStreamSource.union(impressionEventsStreamSource);

        KafkaSink<InactiveCartAbandonedUsers> InactiveCartAbandonedUserEventsKafkaSink =
                KafkaSink.<InactiveCartAbandonedUsers>builder()
                        .setBootstrapServers(JobConfig.CART_ABANDON_EVENTS_SINK_BROKERS)
                        .setDeliverGuarantee(DeliveryGuarantee.AT_LEAST_ONCE)
                        .setTransactionalIdPrefix("user_inactive_events-")
                        .setRecordSerializer(
                                new MobileCoreOrderingEventsSerializationSchema(
                                        JobConfig.CART_ABANDON_EVENTS_SINK_TOPIC))
                        .build();
        CartAbandonedUserEvents cartAbandonedUserEvents =
                new CartAbandonedUserEvents(
                        unionStream, InactiveCartAbandonedUserEventsKafkaSink);
        cartAbandonedUserEvents.execute(env);
    }

    public JobExecutionResult execute(StreamExecutionEnvironment environment) throws Exception {
        // environment.setParallelism(JobConfig.DEFAULT_GLOBAL_PARALLELISM);

        this.impressionEventsSource
                .keyBy(CartAbandonEvent::getDeviceId)
                .window(
                        EventTimeSessionWindows.withGap(
                                Time.minutes(JobConfig.WINDOW_SIZE_IN_MINS)))
                .allowedLateness(Time.seconds(JobConfig.ALLOWED_LATENESS_IN_SECS))
                .process(new GetCartAbandonedInactiveUserEvents())
                .name("cart-abandoned-inactive-user-event-process")
                .uid("cart-abandoned-inactive-user-event-prcess")
                .sinkTo(InactiveCartAbandonedUserEventsSink)
                .name("cart-abandoned-inactive-user-event-sink")
                .uid("cart-abandoned-inactive-user-event-sink");
        return environment.execute("cart_abandon_inactive_user_events");
    }
}
