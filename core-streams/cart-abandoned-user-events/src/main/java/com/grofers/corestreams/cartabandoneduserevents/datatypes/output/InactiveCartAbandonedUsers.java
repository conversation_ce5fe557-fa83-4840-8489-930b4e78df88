package com.grofers.corestreams.cartabandoneduserevents.datatypes.output;

import java.util.Map;
import lombok.Value;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.annotation.JsonSerialize;

@JsonSerialize
@JsonIgnoreProperties(ignoreUnknown = true)
@Value
public class InactiveCartAbandonedUsers {
    String deviceId;
    String userId;
    String cartId;
    Integer merchantId;
    String platform;
    Double latitude;
    Double longitude;
    String channel;
    Map<String, Integer> productsList;
    String lastCartEventName;
    Long lastCartEventTs;
    String lastEventName;
    Long lastEventTs;
    Long processedTs;
    String appVersion;
}
