package com.grofers.corestreams.cartabandoneduserevents.util;

import com.grofers.corestreams.cartabandoneduserevents.datatypes.input.CartAbandonEvent;
import java.util.Set;
import org.apache.flink.api.common.functions.FilterFunction;

public class EventsFilter implements FilterFunction<CartAbandonEvent> {

    private final Set<String> eventNames;

    public EventsFilter(Set<String> eventNames) {
        this.eventNames = eventNames;
    }

    public EventsFilter() {
        this(null);
    }

    @Override
    public boolean filter(CartAbandonEvent cartAbandonEvent) throws Exception {
        if(cartAbandonEvent.getDeviceId() == null || (cartAbandonEvent.getChannel() != null && !cartAbandonEvent.getChannel().equalsIgnoreCase(Constants.CHANNEL_BLINKIT))) {
            return false;
        } else if (this.eventNames == null) {
            return true;
        } else {
            return eventNames.contains(cartAbandonEvent.getEventName());
        }
    }
}
