package com.grofers.corestreams.cartabandoneduserevents.processfunctions;

import com.grofers.corestreams.cartabandoneduserevents.datatypes.input.CartAbandonEvent;
import com.grofers.corestreams.cartabandoneduserevents.datatypes.output.InactiveCartAbandonedUsers;
import com.grofers.corestreams.cartabandoneduserevents.util.Constants;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import org.apache.flink.streaming.api.functions.windowing.ProcessWindowFunction;
import org.apache.flink.streaming.api.windowing.windows.TimeWindow;
import org.apache.flink.util.Collector;

public class GetCartAbandonedInactiveUserEvents
        extends ProcessWindowFunction<
                CartAbandonEvent, InactiveCartAbandonedUsers, String, TimeWindow> {
    @Override
    public void process(
            String key,
            ProcessWindowFunction<CartAbandonEvent, InactiveCartAbandonedUsers, String, TimeWindow>
                            .Context
                    context,
            Iterable<CartAbandonEvent> iterable,
            Collector<InactiveCartAbandonedUsers> collector) {

        Map<String, Integer> productsList = new HashMap<>();
        Integer merchantId;
        merchantId = null;
        Long lastEventTs, lastCartEventTs;
        lastEventTs = lastCartEventTs = null;
        String cartId, userId, lastEventName, lastCartEventName, platform, channel, appVersion;
        cartId =
                userId = lastEventName = lastCartEventName = platform = channel = appVersion = null;
        Double longitude, latitude;
        latitude = longitude = null;

        for (CartAbandonEvent cartAbandonEvent : iterable) {

            if (cartAbandonEvent.getEventName().equalsIgnoreCase(Constants.ORDER_ACKNOWLEDGED)) {
                return;
            }
            if (userId == null) {
                userId = cartAbandonEvent.getUserId();
            }
            if (merchantId == null) {
                merchantId = cartAbandonEvent.getMerchantId();
            }
            if (platform == null) {
                platform = cartAbandonEvent.getPlatform();
            }
            if (longitude == null) {
                longitude = cartAbandonEvent.getLongitude();
            }
            if (latitude == null) {
                latitude = cartAbandonEvent.getLatitude();
            }
            if (channel == null) {
                channel = cartAbandonEvent.getChannel();
            }
            if (appVersion == null) {
                appVersion = cartAbandonEvent.getAppVersion();
            }
            if (!Objects.equals(cartAbandonEvent.getCartId(), cartId)) {
                cartId = cartAbandonEvent.getCartId();
            }
            if (cartAbandonEvent.getEventName().equalsIgnoreCase(Constants.PRODUCT_ADDED)
                    || cartAbandonEvent
                            .getEventName()
                            .equalsIgnoreCase(Constants.PRODUCT_REMOVED)) {
                if (cartAbandonEvent.getProperties().getProduct_id() != null) {
                    productsList.put(
                            cartAbandonEvent.getProperties().getProduct_id(),
                            cartAbandonEvent.getProperties().getQuantity());
                }
            } else if (cartAbandonEvent.getEventName().equalsIgnoreCase(Constants.CART_VIEWED)) {
                if (cartAbandonEvent.getProperties() != null
                        && cartAbandonEvent.getProperties().getProducts() != null) {
                    cartAbandonEvent
                            .getProperties()
                            .getProducts()
                            .forEach(
                                    e -> {
                                        if (e.getProduct_id() != null) {
                                            productsList.put(e.getProduct_id(), e.getQuantity());
                                        }
                                    });
                }
            }
            if (lastEventTs == null || lastEventTs < cartAbandonEvent.getEventTimestamp() * 1000) {
                lastEventTs = cartAbandonEvent.getEventTimestamp() * 1000;
                lastEventName = cartAbandonEvent.getEventName();

                if (Constants.CART_ABANDON_FILTER_EVENTS_SET.contains(cartAbandonEvent.getEventName())) {
                    lastCartEventName = cartAbandonEvent.getEventName();
                    lastCartEventTs = cartAbandonEvent.getEventTimestamp() * 1000;
                }
            }
        }
        // As lastCartEventTs is null, user did not perform any cart related activity yet.
        if (lastCartEventTs == null) {
            return;
        }
        if (TimeUnit.MILLISECONDS.toMinutes(lastEventTs - lastCartEventTs) < 60) {
            collector.collect(
                    new InactiveCartAbandonedUsers(
                            key,
                            userId,
                            cartId,
                            merchantId,
                            platform,
                            latitude,
                            longitude,
                            channel,
                            productsList,
                            lastCartEventName,
                            lastCartEventTs,
                            lastEventName,
                            lastEventTs,
                            context.currentProcessingTime(),
                            appVersion));
        }
    }
}
