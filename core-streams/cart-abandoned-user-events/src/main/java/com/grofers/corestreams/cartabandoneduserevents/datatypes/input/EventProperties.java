package com.grofers.corestreams.cartabandoneduserevents.datatypes.input;

import java.util.List;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Value;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.annotation.JsonProperty;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.annotation.JsonSerialize;

@JsonSerialize
@JsonIgnoreProperties(ignoreUnknown = true)
@Value
@NoArgsConstructor(force = true)
@Getter
public class EventProperties {
    @JsonProperty("product_id")
    String product_id;

    @JsonProperty("quantity")
    Integer quantity;

    @JsonProperty("products")
    List<PropertiesProducts> products;
}
