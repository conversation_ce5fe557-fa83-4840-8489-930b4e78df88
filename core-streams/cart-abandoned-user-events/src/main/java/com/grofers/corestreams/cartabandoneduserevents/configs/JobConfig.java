package com.grofers.corestreams.cartabandoneduserevents.configs;

public final class JobConfig {
    public static String CHECKPOINTS_STORAGE_LOCATION;
    public static String EVENTS_SOURCE_BROKERS;
    public static String EVENTS_IMPRESSION_CONSUMER_GROUP_ID;
    public static String EVENTS_CLICK_CONSUMER_GROUP_ID;
    public static String[] EVENTS_SOURCE_CLICK_TOPIC;
    public static String[] EVENTS_SOURCE_IMPRESSION_TOPIC;
    public static int IDLENESS_TIME_IN_SECS;
    public static int ALLOWED_LATENESS_IN_SECS;
    public static int WINDOW_SIZE_IN_MINS;
    public static int MAX_OUT_OF_ORDERNESS_IN_SECS;
    public static int DEFAULT_GLOBAL_PARALLELISM;
    public static String CART_ABANDON_EVENTS_SINK_BROKERS;
    public static String CART_ABANDON_EVENTS_SINK_TOPIC;

    public JobConfig(
            String CHECKPOINTS_STORAGE_LOCATION,
            String EVENTS_SOURCE_BROKERS,
            String EVENTS_IMPRESSION_CONSUMER_GROUP_ID,
            String EVENTS_CLICK_CONSUMER_GROUP_ID,
            String EVENTS_SOURCE_CLICK_TOPIC,
            String EVENTS_SOURCE_IMPRESSION_TOPIC,
            int IDLENESS_TIME_IN_SECS,
            int ALLOWED_LATENESS_IN_SECS,
            int WINDOW_SIZE_IN_MINS,
            int MAX_OUT_OF_ORDERNESS_IN_SECS,
            int DEFAULT_GLOBAL_PARALLELISM,
            String CART_ABANDON_EVENTS_SINK_BROKERS,
            String CART_ABANDON_EVENTS_SINK_TOPIC) {

        JobConfig.CHECKPOINTS_STORAGE_LOCATION = CHECKPOINTS_STORAGE_LOCATION;
        JobConfig.EVENTS_SOURCE_BROKERS = EVENTS_SOURCE_BROKERS;
        JobConfig.EVENTS_IMPRESSION_CONSUMER_GROUP_ID = EVENTS_IMPRESSION_CONSUMER_GROUP_ID;
        JobConfig.EVENTS_CLICK_CONSUMER_GROUP_ID = EVENTS_CLICK_CONSUMER_GROUP_ID;
        JobConfig.EVENTS_SOURCE_CLICK_TOPIC = EVENTS_SOURCE_CLICK_TOPIC.split(",");
        JobConfig.EVENTS_SOURCE_IMPRESSION_TOPIC = EVENTS_SOURCE_IMPRESSION_TOPIC.split(",");
        JobConfig.IDLENESS_TIME_IN_SECS = IDLENESS_TIME_IN_SECS;
        JobConfig.ALLOWED_LATENESS_IN_SECS = ALLOWED_LATENESS_IN_SECS;
        JobConfig.DEFAULT_GLOBAL_PARALLELISM = DEFAULT_GLOBAL_PARALLELISM;
        JobConfig.MAX_OUT_OF_ORDERNESS_IN_SECS = MAX_OUT_OF_ORDERNESS_IN_SECS;
        JobConfig.WINDOW_SIZE_IN_MINS = WINDOW_SIZE_IN_MINS;
        JobConfig.CART_ABANDON_EVENTS_SINK_BROKERS = CART_ABANDON_EVENTS_SINK_BROKERS;
        JobConfig.CART_ABANDON_EVENTS_SINK_TOPIC = CART_ABANDON_EVENTS_SINK_TOPIC;
    }
}
