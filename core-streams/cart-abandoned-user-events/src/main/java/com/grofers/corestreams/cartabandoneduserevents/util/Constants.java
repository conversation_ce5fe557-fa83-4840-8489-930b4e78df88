package com.grofers.corestreams.cartabandoneduserevents.util;

import java.util.Set;
import org.apache.commons.compress.utils.Sets;

public class Constants {
    public static final String ORDER_ACKNOWLEDGED = "Order Acknowledged";
    public static final String PRODUCT_ADDED = "Product Added";
    public static final String PRODUCT_REMOVED = "Product Removed";
    public static final String CART_VIEWED = "Cart Viewed";

    public static final Set<String> CART_ABANDON_FILTER_EVENTS_SET =
            Sets.newHashSet(
                    "Order Acknowledged",
                    "Coupon Widget Shown",
                    "Cart Footer Strip Clicked",
                    "Product Added",
                    "Product Removed",
                    "Cart Viewed",
                    "Checkout Strip Widget Shown",
                    "Cart Reloaded",
                    "Shipment Widget Shown",
                    "Feeding India Widget Shown");

    public static final Set<String> CART_ABANDON_FILTER_CLICK_SET =
            Sets.newHashSet(
                    "App Launch",
                    "App Launch Traits",
                    "Homepage Visit",
                    "Order Acknowledged",
                    "Cart Footer Strip Clicked",
                    "Product Added",
                    "Product Removed",
                    "Cart Viewed");
    
    public static final Set<String> CART_ABANDON_FILTER_IMPRESSION_SET =
            Sets.newHashSet(
                "Cart Reloaded",
                "Coupon Widget Shown",
                "Checkout Strip Widget Shown",
                "Shipment Widget Shown",
                "Feeding India Widget Shown");

    public static final String ANDROID = "Android";
    public static final String IOS = "iOS";
    public static final String CHANNEL_BLINKIT = "BLINKIT";
}
