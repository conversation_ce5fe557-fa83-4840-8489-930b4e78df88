package com.grofers.corestreams.cartabandoneduserevents.datatypes.input;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class CartAbandonEvent {
    String eventName;
    String deviceId;
    String userId;
    String cartId;
    Integer merchantId;
    String platform;
    Double latitude;
    Double longitude;
    String channel;
    String appVersion;
    Long eventTimestamp;
    EventProperties properties;

    public CartAbandonEvent(JumboCartAbandonEvent jumboCartAbandonEvent) {
        this.eventName = jumboCartAbandonEvent.getEventName();
        this.deviceId = jumboCartAbandonEvent.getDeviceId();
        this.userId = jumboCartAbandonEvent.getUserId();
        this.cartId = jumboCartAbandonEvent.getTraits().getCartId();
        this.merchantId = jumboCartAbandonEvent.getTraits().getMerchantId();
        this.platform = jumboCartAbandonEvent.getSource();
        this.latitude = jumboCartAbandonEvent.getTraits().getLatitude();
        this.longitude = jumboCartAbandonEvent.getTraits().getLongitude();
        this.channel = jumboCartAbandonEvent.getTraits().getChannel();
        this.appVersion = jumboCartAbandonEvent.getTraits().getVersion();
        this.eventTimestamp = jumboCartAbandonEvent.getEventTimestamp();
        this.properties = jumboCartAbandonEvent.getProperties();
    }
}
