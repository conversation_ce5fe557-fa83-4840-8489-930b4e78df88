package com.grofers.corestreams.cartabandoneduserevents.serdes;

import com.grofers.corestreams.cartabandoneduserevents.datatypes.input.CartAbandonEvent;
import com.grofers.corestreams.cartabandoneduserevents.datatypes.input.JumboCartAbandonEvent;
import java.io.IOException;
import org.apache.flink.api.common.serialization.DeserializationSchema;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class JSONValueDeserializationSchema implements DeserializationSchema<CartAbandonEvent> {
    private static final long serialVersionUID = 1L;
    private static final Logger LOG = LoggerFactory.getLogger(JSONValueDeserializationSchema.class);

    private static final ObjectMapper objectMapper = new ObjectMapper();

    @Override
    public CartAbandonEvent deserialize(byte[] message) throws IOException {
        try {
            CartAbandonEvent cartAbandonEvent =
                    new CartAbandonEvent(
                            objectMapper.readValue(message, JumboCartAbandonEvent.class));
            return cartAbandonEvent;
        } catch (IOException exception) {
            LOG.info("Failed to deserialize message: {}", new String(message));
            return null;
        }
    }

    @Override
    public boolean isEndOfStream(CartAbandonEvent cartAbandonEvent) {
        return false;
    }

    @Override
    public TypeInformation<CartAbandonEvent> getProducedType() {
        return TypeInformation.of(CartAbandonEvent.class);
    }
}
