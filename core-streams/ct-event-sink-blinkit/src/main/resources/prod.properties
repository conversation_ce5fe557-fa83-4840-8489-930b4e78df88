# Properties for production environment

#Checkpoints
CHECKPOINTS_STORAGE_LOCATION = s3a://prod-data-flink-states/core-streams/ct-event-sink-blinkit/checkpoints/

# Events Source
EVENTS_SOURCE_BROKERS = vpce-06233f70360f757f8-enprgsf3.vpce-svc-0dcaa5761ef958410.ap-southeast-1.vpce.amazonaws.com:9093,vpce-06233f70360f757f8-enprgsf3.vpce-svc-0dcaa5761ef958410.ap-southeast-1.vpce.amazonaws.com:9094,vpce-06233f70360f757f8-enprgsf3.vpce-svc-0dcaa5761ef958410.ap-southeast-1.vpce.amazonaws.com:9095
EVENTS_SOURCE_CONSUMER_GROUP_ID = flink-corestreams-ct-event-sink-blinkit.group-v1
CLICK_EVENTS_SOURCE_TOPIC = jumbo_transformed.blinkit.click_events

# Events Sink
SINK_BROKERS = vpce-06233f70360f757f8-enprgsf3.vpce-svc-0dcaa5761ef958410.ap-southeast-1.vpce.amazonaws.com:9093,vpce-06233f70360f757f8-enprgsf3.vpce-svc-0dcaa5761ef958410.ap-southeast-1.vpce.amazonaws.com:9094,vpce-06233f70360f757f8-enprgsf3.vpce-svc-0dcaa5761ef958410.ap-southeast-1.vpce.amazonaws.com:9095
CT_SINK_TOPIC = blinkit.ols.flash_gateway
