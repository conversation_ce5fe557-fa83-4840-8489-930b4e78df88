/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.grofers.corestreams.cteventsinkblinkit;

import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.contrib.streaming.state.EmbeddedRocksDBStateBackend;
import org.apache.flink.streaming.api.CheckpointingMode;
import org.apache.flink.streaming.api.environment.CheckpointConfig;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.table.api.bridge.java.StreamTableEnvironment;

import com.grofers.corestreams.cteventsinkblinkit.configs.JobConfigManager;

import java.io.IOException;
import java.util.*;

import static com.grofers.corestreams.cteventsinkblinkit.Queries.*;
import static com.grofers.corestreams.cteventsinkblinkit.configs.JobConfig.*;

/**
 * CT Event Sink Blinkit
 *
 * <p>For a tutorial how to write a Flink application, check the tutorials and examples on the <a
 * href="https://flink.apache.org">Flink Website</a>.
 *
 * <p>To package your application into a JAR file for execution, run 'mvn clean package' on the
 * command line.
 *
 * <p>If you change the name of the main class (with the public static void main(String[] args))
 * method, change the respective entry in the POM.xml file (simply search for 'mainClass').
 */
public class CTEventSinkBlinkit {

    public static void main(String[] args)
            throws JobConfigManager.InvalidEnvironmentException, IOException {

        final StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment();
        final StreamTableEnvironment tEnv = StreamTableEnvironment.create(env);

        Configuration configuration = tEnv.getConfig().getConfiguration();

        configuration.setString("table.exec.source.idle-timeout", "300 s");

        // local-global aggregation depends on mini-batch is enabled
        configuration.setString("table.exec.mini-batch.enabled", "true");
        configuration.setString("table.exec.mini-batch.allow-latency", "1 s");
        configuration.setString("table.exec.mini-batch.size", "500");

        ParameterTool params = ParameterTool.fromArgs(args);
        String userEnv = params.getRequired("environment");

        // Get job configs
        JobConfigManager.setJobConfigsPath(userEnv);
        String jobPropertiesPath = JobConfigManager.getJobConfigsPath(userEnv);
        JobConfigManager.setJobConfigs(tEnv, jobPropertiesPath);
        JobConfigManager.getJobConfigs(tEnv);

        // Checkpoint Configs
        env.enableCheckpointing(600000);
        // set mode to exactly-once (default)
        env.getCheckpointConfig().setCheckpointingMode(CheckpointingMode.EXACTLY_ONCE);
        // make sure 900000 ms of progress happen between checkpoints
        env.getCheckpointConfig().setMinPauseBetweenCheckpoints(300000);
        // checkpoints have to complete within this minute, or are discarded
        env.getCheckpointConfig().setCheckpointTimeout(300000);
        // only two consecutive checkpoint failures are tolerated
        env.getCheckpointConfig().setTolerableCheckpointFailureNumber(2);
        // allow only one checkpoint to be in progress at the same time
        env.getCheckpointConfig().setMaxConcurrentCheckpoints(1);
        // enable externalized checkpoints which are retained after job cancellation
        env.getCheckpointConfig()
                .setExternalizedCheckpointCleanup(
                        CheckpointConfig.ExternalizedCheckpointCleanup.DELETE_ON_CANCELLATION);
        // enable unaligned checkpoints
        env.getCheckpointConfig().enableUnalignedCheckpoints();
        // sets a RocksDB checkpoint storage where checkpoint snapshots will be incrementally
        // written
        env.setStateBackend(new EmbeddedRocksDBStateBackend(true));
        env.getCheckpointConfig().setCheckpointStorage(CHECKPOINTS_STORAGE_LOCATION);

        String blinkitClickEvents =
                String.format(
                        BLINKIT_CLICK_EVENTS,
                        CLICK_EVENTS_SOURCE_TOPIC,
                        EVENTS_SOURCE_BROKERS,
                        EVENTS_SOURCE_CONSUMER_GROUP_ID);
        tEnv.executeSql(blinkitClickEvents);

        String ctSinkCreateTable = String.format(CT_SINK_CREATE_TABLE, CT_SINK_TOPIC, SINK_BROKERS);
        tEnv.executeSql(ctSinkCreateTable);

        // Execute program, beginning computation.
        tEnv.executeSql(INSERT_CT_SINK);
    }
}
