package com.grofers.corestreams.cteventsinkblinkit;

public class Queries {
    public static final String BLINKIT_CLICK_EVENTS =
            "CREATE TABLE IF NOT EXISTS blinkit_click_events (event_name STRING,"
                + " ingestion_timestamp BIGINT, user_id STRING, properties ROW(enabled BOOLEAN,"
                + " page_name STRING)) WITH ('connector' = 'kafka', 'format' = 'json', 'topic' ="
                + " '%s', 'properties.bootstrap.servers' = '%s', 'properties.group.id' = '%s',"
                + " 'scan.startup.mode' = 'group-offsets', 'json.timestamp-format.standard' ="
                + " 'ISO-8601', 'properties.auto.offset.reset' = 'latest')";

    public static final String CT_SINK_CREATE_TABLE =
            "CREATE TABLE IF NOT EXISTS ct_event_sink (anonymousId STRING, type VARCHAR(20),"
                + " context ROW<traits ROW<is_vday_single_toggle BOOLEAN>>, userId  STRING)"
                + " WITH ('connector' = 'kafka', 'key.format' = 'json', 'key.fields' = 'userId',"
                + " 'value.format' = 'json', 'topic' = '%s', 'properties.bootstrap.servers' ="
                + " '%s', 'properties.request.timeout.ms' = '300000')";

    public static final String INSERT_CT_SINK =
            "INSERT INTO ct_event_sink SELECT uuid() AS anonymousId, 'identify' AS type,"
                + " ROW(ROW(properties.enabled)) AS context, user_id AS userId FROM"
                + " blinkit_click_events WHERE event_name IN ('Toggle Button Clicked') AND"
                + " TRY_CAST(user_id AS BIGINT) IS NOT NULL AND properties.page_name = 'feed' AND"
                + " properties.enabled IS NOT NULL";
}
