package com.grofers.corestreams.cteventsinkblinkit.configs;

public class JobConfig {
    public static String EVENTS_SOURCE_BROKERS;
    public static String EVENTS_SOURCE_CONSUMER_GROUP_ID;
    public static String CLICK_EVENTS_SOURCE_TOPIC;
    public static String SINK_BROKERS;
    public static String CT_SINK_TOPIC;
    public static String CHECKPOINTS_STORAGE_LOCATION;

    public JobConfig(
            String EVENTS_SOURCE_BROKERS,
            String EVENTS_SOURCE_CONSUMER_GROUP_ID,
            String CLICK_EVENTS_SOURCE_TOPIC,
            String SINK_BROKERS,
            String CT_SINK_TOPIC,
            String CHECKPOINTS_STORAGE_LOCATION) {
        JobConfig.EVENTS_SOURCE_BROKERS = EVENTS_SOURCE_BROKERS;
        JobConfig.EVENTS_SOURCE_CONSUMER_GROUP_ID = EVENTS_SOURCE_CONSUMER_GROUP_ID;
        JobConfig.CLICK_EVENTS_SOURCE_TOPIC = CLICK_EVENTS_SOURCE_TOPIC;
        JobConfig.SINK_BROKERS = SINK_BROKERS;
        JobConfig.CT_SINK_TOPIC = CT_SINK_TOPIC;
        JobConfig.CHECKPOINTS_STORAGE_LOCATION = CHECKPOINTS_STORAGE_LOCATION;
    }
}
