name: Store Performance Events
image: 442534439095.dkr.ecr.ap-southeast-1.amazonaws.com/data/flink-streams/core-streams/store-performance-events
flink_config:
  taskmanager.memory.process.size: 2048m
  jobmanager.memory.process.size: 1024m
  taskmanager.memory.managed.size: 0
  taskmanager.numberOfTaskSlots: 2
  parallelism.default: 2
  process.working-dir: store-performance-events/process
  state.backend.local-recovery: true
  taskmanager.resource-id: TaskManager_StorePerformanceEvents
deployment_config:
  kubernetes.rest-service.exposed.type: NodePort
  metrics.reporters: prom
  metrics.reporter.prom.factory.class: org.apache.flink.metrics.prometheus.PrometheusReporterFactory
allow_non_restored_state: False # Set to True when changes are made to Operator
codeartifact_access: True