package com.grofers.corestreams.storeperformanceevents;

public class Queries {
    public static final String BLINKIT_STOREOPS_TASK_UPDATES =
            "CREATE TABLE IF NOT EXISTS storeops_task_updates (`record_ts` TIMESTAMP(3) METADATA"
                + " FROM 'timestamp', activity_id BIGINT,employee_id STRING,site_id"
                + " STRING,start_time STRING,end_time STRING,`type` STRING,sub_type STRING,event"
                + " STRING,meta ROW(item_data ARRAY<ROW<item_id STRING, `type` STRING, quantity"
                + " INT, processed_qty INT, tags ARRAY<STRING>>>), event_timeline"
                + " ARRAY<ROW<event_name STRING, `timestamp` STRING>>) WITH ('connector' ="
                + " 'kafka', 'format' = 'json', 'topic' = '%s', 'properties.bootstrap.servers' ="
                + " '%s', 'properties.group.id' = '%s', 'scan.startup.mode' = 'group-offsets',"
                + " 'json.timestamp-format.standard' = 'ISO-8601', 'properties.auto.offset.reset'"
                + " = 'latest')";

    public static final String BLINKIT_STOREOPS_ORDER_COMPLAINTS =
            "CREATE TABLE IF NOT EXISTS storeops_order_compaints (`record_ts` TIMESTAMP(3)"
                + " METADATA FROM 'timestamp', `headers` MAP<STRING, STRING> METADATA VIRTUAL,"
                + " picker_details ARRAY<ROW<employee_id STRING, activity_completed_time BIGINT>>,"
                + " product_tags ARRAY<STRING>, store_id STRING, product_id STRING, quantity INT)"
                + " WITH ('connector' = 'kafka', 'format' = 'json', 'topic' ="
                + " '%s','properties.bootstrap.servers' = '%s', 'properties.group.id' ="
                + " '%s','scan.startup.mode' = 'group-offsets', 'properties.auto.offset.reset' ="
                + " 'latest')";

    public static final String BLINKIT_STOREOPS_SHIFT_DETAILS =
            "CREATE TABLE IF NOT EXISTS storeops_shift_details (`record_ts` TIMESTAMP(3) METADATA"
                + " FROM 'timestamp', shift_details ROW(id STRING,  employee_id STRING, site_id"
                + " STRING, shift_start_time STRING, shift_end_time STRING, shift_duration DOUBLE,"
                + " shift_duration_excluding_break DOUBLE)) WITH ('connector' = 'kafka', 'format'"
                + " = 'json', 'topic' = '%s', 'properties.bootstrap.servers' = '%s',"
                + " 'properties.group.id' = '%s', 'scan.startup.mode' = 'group-offsets',"
                + " 'json.timestamp-format.standard' = 'ISO-8601', 'properties.auto.offset.reset'"
                + " = 'latest')";

    public static final String STORE_PERFORMANCE_CREATE_TABLE =
            "CREATE TABLE IF NOT EXISTS store_performance_metrics (employee_id STRING,site_id"
                    + " STRING,event_ts BIGINT,entity_id STRING,entity_type STRING,metric_type"
                    + " STRING,metric_value DOUBLE, insert_ts BIGINT, PRIMARY KEY (employee_id,"
                    + " metric_type, entity_id, event_ts) NOT ENFORCED ) WITH ('connector' ="
                    + " 'upsert-kafka', 'key.format' = 'json', 'value.format' = 'json', 'topic' ="
                    + " '%s', 'properties.bootstrap.servers' = '%s')";

    public static final String INSERT_STORE_PERFORMANCE_METRICS =
            //  REGULAR_ITEMS_QTY_PICKED Quatity of Non-Print Items Picked
            "INSERT INTO store_performance_metrics SELECT employee_id, site_id,"
                + " GetTimestampFromPickingTimeline(event_timeline, 'ASSIGNED') AS event_ts,"
                + " CAST(activity_id AS STRING) AS entity_id, 'ACTIVITY' AS entity_type,"
                + " 'REGULAR_ITEMS_QTY_PICKED' AS metric_type,"
                + " CAST(GetPickedItemsQuatity(meta.item_data, false) AS DOUBLE) AS metric_value,"
                + " (1000 * UNIX_TIMESTAMP(CAST(record_ts AS STRING)) + EXTRACT(MILLISECOND FROM"
                + " record_ts)) AS insert_ts FROM storeops_task_updates WHERE employee_id IS NOT"
                + " NULL AND event IN ('ACTIVITY_COMPLETED', 'ACTIVITY_CANCELLED') AND `type` ="
                + " 'PICKING' AND sub_type ='CUSTOMER_ORDER' AND"
                + " GetPickedItemsQuatity(meta.item_data,false) > 0"
                    // UNIQUE_PAAS_ITEMS_PICKED Number of Print Items Picked
                    + " UNION ALL SELECT employee_id, site_id,"
                    + " GetTimestampFromPickingTimeline(event_timeline, 'ASSIGNED') AS event_ts,"
                    + " CAST(activity_id AS STRING) AS entity_id, 'ACTIVITY' AS entity_type,"
                    + " 'UNIQUE_PAAS_ITEMS_PICKED' AS metric_type,"
                    + " CAST(GetPickedItemsQuatity(meta.item_data, true) AS DOUBLE) AS"
                    + " metric_value, (1000 * UNIX_TIMESTAMP(CAST(record_ts AS STRING)) +"
                    + " EXTRACT(MILLISECOND FROM record_ts)) AS insert_ts FROM"
                    + " storeops_task_updates WHERE employee_id IS NOT NULL AND event IN"
                    + " ('ACTIVITY_COMPLETED', 'ACTIVITY_CANCELLED') AND `type` = 'PICKING' AND"
                    + " sub_type = 'CUSTOMER_ORDER' AND GetPickedItemsQuatity(meta.item_data,"
                    + " true) > 0 UNION ALL"
                    //  PICKING_TIME Total Picking time in miliseconds
                    + " SELECT employee_id, site_id,"
                    + " GetTimestampFromPickingTimeline(event_timeline, 'ASSIGNED') AS event_ts,"
                    + " CAST(activity_id AS STRING) AS entity_id, 'ACTIVITY' AS entity_type,"
                    + " 'PICKING_TIME' AS metric_type,"
                    + " CAST(GetTimestampFromPickingTimeline(event_timeline, 'COMPLETED') -"
                    + " GetTimestampFromPickingTimeline(event_timeline, 'ASSIGNED') AS DOUBLE) AS"
                    + " metric_value, (1000 * UNIX_TIMESTAMP(CAST(record_ts AS STRING)) +"
                    + " EXTRACT(MILLISECOND FROM record_ts)) AS insert_ts FROM"
                    + " storeops_task_updates WHERE employee_id IS NOT NULL AND event IN"
                    + " ('ACTIVITY_COMPLETED', 'ACTIVITY_CANCELLED') AND `type` = 'PICKING' AND"
                    + " sub_type = 'CUSTOMER_ORDER' UNION ALL"
                    //  LOGIN_TIME Employee Shift duration
                    + " SELECT shift_details.employee_id AS employee_id, shift_details.site_id AS"
                    + " site_id, GetUnixTimeFromString(shift_details.shift_start_time) AS"
                    + " event_ts, CAST(shift_details.id AS STRING) AS entity_id, 'SHIFT' AS"
                    + " entity_type, 'LOGIN_TIME' AS metric_type,"
                    + " CAST(shift_details.shift_duration AS DOUBLE) AS metric_value, (1000 *"
                    + " UNIX_TIMESTAMP(CAST(record_ts AS STRING)) + EXTRACT(MILLISECOND FROM"
                    + " record_ts)) AS insert_ts FROM storeops_shift_details WHERE shift_details.employee_id IS NOT NULL UNION ALL"
                    //  ACTIVE_TIME Employee Shift duration excluding break
                    + " SELECT shift_details.employee_id AS employee_id, shift_details.site_id AS"
                    + " site_id, GetUnixTimeFromString(shift_details.shift_start_time) AS"
                    + " event_ts, CAST(shift_details.id AS STRING) AS entity_id, 'SHIFT' AS"
                    + " entity_type, 'ACTIVE_TIME' AS metric_type,"
                    + " CAST(shift_details.shift_duration_excluding_break AS DOUBLE) AS"
                    + " metric_value, (1000 * UNIX_TIMESTAMP(CAST(record_ts AS STRING)) +"
                    + " EXTRACT(MILLISECOND FROM record_ts)) AS insert_ts FROM"
                    + " storeops_shift_details WHERE shift_details.employee_id IS NOT NULL UNION ALL"
                    //  COMPLAINT_REGULAR_PRODUCT_QTY Quatity Items in Picker Complaints
                    + " SELECT picker_details_array.employee_id AS employee_id, store_id AS"
                    + " site_id, picker_details_array.activity_completed_time AS event_ts,"
                    + " product_id AS entity_id, 'PRODUCT_ID' AS entity_type,"
                    + " 'COMPLAINT_REGULAR_PRODUCT_QTY' AS metric_type, CAST(quantity AS DOUBLE)"
                    + " AS metric_value, (1000 * UNIX_TIMESTAMP(CAST(record_ts AS STRING)) +"
                    + " EXTRACT(MILLISECOND FROM record_ts)) AS insert_ts FROM"
                    + " storeops_order_compaints CROSS JOIN"
                    + " UNNEST(storeops_order_compaints.picker_details) AS picker_details_array"
                    + " (employee_id, activity_completed_time) WHERE `headers`['event_name'] ="
                    + " 'COMPLAINT_CREATED' AND `headers`['version'] = 'v2' AND NOT"
                    + " ARRAY_CONTAINS(product_tags, 'PRINT') AND NOT ARRAY_CONTAINS(product_tags,"
                    + " 'PRINT_CARD') AND picker_details_array.employee_id IS NOT NULL  UNION ALL"
                    //  COMPLAINT_UNIQUE_PAAS_PRODUCT Print Order Picker Complaints
                    + " SELECT picker_details_array.employee_id AS employee_id, store_id AS"
                    + " site_id, picker_details_array.activity_completed_time AS event_ts,"
                    + " product_id AS entity_id, 'PRODUCT_ID' AS entity_type,"
                    + " 'COMPLAINT_UNIQUE_PAAS_PRODUCT' AS metric_type, 1 AS metric_value, (1000 *"
                    + " UNIX_TIMESTAMP(CAST(record_ts AS STRING)) + EXTRACT(MILLISECOND FROM"
                    + " record_ts)) AS insert_ts FROM storeops_order_compaints CROSS JOIN"
                    + " UNNEST(storeops_order_compaints.picker_details) AS picker_details_array"
                    + " (employee_id, activity_completed_time) WHERE `headers`['event_name'] ="
                    + " 'COMPLAINT_CREATED' AND picker_details_array.employee_id IS NOT NULL AND `headers`['version'] = 'v2' AND"
                    + " (ARRAY_CONTAINS(product_tags, 'PRINT') OR ARRAY_CONTAINS(product_tags,"
                    + " 'PRINT_CARD'))";
}
