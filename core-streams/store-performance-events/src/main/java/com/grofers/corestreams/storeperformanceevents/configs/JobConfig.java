package com.grofers.corestreams.storeperformanceevents.configs;

public class JobConfig {
    public static String EVENTS_SOURCE_BROKERS;
    public static String EVENTS_SOURCE_CONSUMER_GROUP_ID;
    public static String TASK_UPDATES_SOURCE_TOPIC;
    public static String OPS_SHIFT_SOURCE_TOPIC;
    public static String COMPLAINTS_SOURCE_TOPIC;
    public static String STORE_PERFORMANCE_EVENTS_SINK_BROKERS;
    public static String STORE_PERFORMANCE_EVENTS_SINK_TOPIC;
    public static String CHECKPOINTS_STORAGE_LOCATION;

    public JobConfig(
            String EVENTS_SOURCE_BROKERS,
            String EVENTS_SOURCE_CONSUMER_GROUP_ID,
            String TASK_UPDATES_SOURCE_TOPIC,
            String OPS_SHIFT_SOURCE_TOPIC,
            String COMPLAINTS_SOURCE_TOPIC,
            String STORE_PERFORMANCE_EVENTS_SINK_BROKERS,
            String STORE_PERFORMANCE_EVENTS_SINK_TOPIC,
            String CHECKPOINTS_STORAGE_LOCATION) {
        JobConfig.EVENTS_SOURCE_BROKERS = EVENTS_SOURCE_BROKERS;
        JobConfig.EVENTS_SOURCE_CONSUMER_GROUP_ID = EVENTS_SOURCE_CONSUMER_GROUP_ID;
        JobConfig.TASK_UPDATES_SOURCE_TOPIC = TASK_UPDATES_SOURCE_TOPIC;
        JobConfig.OPS_SHIFT_SOURCE_TOPIC = OPS_SHIFT_SOURCE_TOPIC;
        JobConfig.COMPLAINTS_SOURCE_TOPIC = COMPLAINTS_SOURCE_TOPIC;
        JobConfig.STORE_PERFORMANCE_EVENTS_SINK_BROKERS = STORE_PERFORMANCE_EVENTS_SINK_BROKERS;
        JobConfig.STORE_PERFORMANCE_EVENTS_SINK_TOPIC = STORE_PERFORMANCE_EVENTS_SINK_TOPIC;
        JobConfig.CHECKPOINTS_STORAGE_LOCATION = CHECKPOINTS_STORAGE_LOCATION;
    }
}
