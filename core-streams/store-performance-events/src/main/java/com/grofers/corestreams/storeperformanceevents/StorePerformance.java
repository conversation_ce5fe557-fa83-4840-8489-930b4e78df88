/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.grofers.corestreams.storeperformanceevents;

import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.contrib.streaming.state.EmbeddedRocksDBStateBackend;
import org.apache.flink.streaming.api.CheckpointingMode;
import org.apache.flink.streaming.api.environment.CheckpointConfig;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.table.annotation.DataTypeHint;
import org.apache.flink.table.api.bridge.java.StreamTableEnvironment;
import org.apache.flink.table.functions.ScalarFunction;
import org.apache.flink.types.Row;

import com.grofers.corestreams.storeperformanceevents.configs.JobConfigManager;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeFormatterBuilder;
import java.time.temporal.ChronoField;
import java.util.Arrays;
import java.util.List;

import static com.grofers.corestreams.storeperformanceevents.Queries.*;
import static com.grofers.corestreams.storeperformanceevents.configs.JobConfig.*;

/**
 * Store Performance Metrics
 *
 * <p>For a tutorial how to write a Flink application, check the tutorials and examples on the <a
 * href="https://flink.apache.org">Flink Website</a>.
 *
 * <p>To package your application into a JAR file for execution, run 'mvn clean package' on the
 * command line.
 *
 * <p>If you change the name of the main class (with the public static void main(String[] args))
 * method, change the respective entry in the POM.xml file (simply search for 'mainClass').
 */
public class StorePerformance {

    public static class GetUnixTimeFromString extends ScalarFunction {
        public Long eval(String s) {
            return getEpochFromTimestamp(s);
        }
    }

    public static class GetTimestampFromPickingTimeline extends ScalarFunction {
        public Long eval(
                @DataTypeHint("ARRAY <ROW <event_name STRING ,`timestamp` STRING>>") Row[] arr,
                String event_name) {
            Long timestamp = null;
            if (arr != null) {
                for (Row i : arr) {
                    if (event_name.equals(i.getFieldAs("event_name").toString())
                            && i.getFieldAs("timestamp") != null) {
                        timestamp = getEpochFromTimestamp(i.getFieldAs("timestamp").toString());
                        break;
                    }
                }
            }
            return timestamp;
        }
    }

    public static class GetPickedItemsQuatity extends ScalarFunction {
        public int eval(
                @DataTypeHint(
                                "ARRAY<ROW <item_id STRING, `type` STRING,"
                                        + " quantity INT, processed_qty INT, tags ARRAY<STRING>>>")
                        Row[] arr,
                boolean is_print) {
            int regular_quatity = 0;
            int print_quatity = 0;
            if (arr != null) {
                for (Row i : arr) {
                    List<String> tags = Arrays.asList((String[]) i.getFieldAs("tags"));
                    if (tags != null && (tags.contains("PRINT") || tags.contains("PRINT_CARD"))) {
                        ++print_quatity;
                    } else if (i.getFieldAs("processed_qty") != null) {
                        regular_quatity =
                                regular_quatity
                                        + Integer.parseInt(
                                                i.getFieldAs("processed_qty").toString());
                    }
                }
            }
            return is_print ? print_quatity : regular_quatity;
        }
    }

    private static Long getEpochFromTimestamp(String utcTimestamp) {
        if (utcTimestamp == null) return null;
        // Check if the utcTimestamp is in the form of an epoch
        try {
            long epochTimestamp = Long.parseLong(utcTimestamp);
            // If the parsing succeeds, it means the timestamp is already in epoch format
            return epochTimestamp;
        } catch (NumberFormatException e) {
            // If parsing fails, it means the timestamp is in string format
            // Define two formats to handle both cases
            DateTimeFormatter formatter =
                    new DateTimeFormatterBuilder()
                            .appendPattern("yyyy-MM-dd'T'HH:mm:ss")
                            .optionalStart() // Optional start for milliseconds
                            .appendFraction(ChronoField.MICRO_OF_SECOND, 0, 6, true)
                            .optionalEnd() // Optional end for milliseconds
                            .optionalStart() // Optional start for the timezone offset
                            .appendOffset("+HHMM", "Z") // Offset and 'Z' are optional
                            .optionalEnd() // Optional end for the timezone offset
                            .toFormatter();

            ZonedDateTime zonedDateTimeDate;
            try {
                // First try to parse with the formatter without milliseconds and 'Z'
                zonedDateTimeDate =
                        LocalDateTime.parse(utcTimestamp, formatter).atZone(ZoneId.of("UTC"));
                return zonedDateTimeDate.toInstant().toEpochMilli();
            } catch (java.time.format.DateTimeParseException ex) {
                return null;
            }
        }
    }

    public static void main(String[] args) throws Exception {
        // Sets up the execution environment, which is the main entry point
        // to building Flink applications.
        final StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment();
        final StreamTableEnvironment tEnv = StreamTableEnvironment.create(env);

        tEnv.createTemporarySystemFunction("GetPickedItemsQuatity", GetPickedItemsQuatity.class);
        tEnv.createTemporarySystemFunction(
                "GetTimestampFromPickingTimeline", GetTimestampFromPickingTimeline.class);
        tEnv.createTemporarySystemFunction("GetUnixTimeFromString", GetUnixTimeFromString.class);

        Configuration configuration = tEnv.getConfig().getConfiguration();

        configuration.setString("table.exec.source.idle-timeout", "300 s");
        configuration.setString("table.exec.state.ttl", "900 s");

        // local-global aggregation depends on mini-batch is enabled
        configuration.setString("table.exec.mini-batch.enabled", "true");
        configuration.setString("table.exec.mini-batch.allow-latency", "1 s");
        configuration.setString("table.exec.mini-batch.size", "500");

        ParameterTool params = ParameterTool.fromArgs(args);
        String userEnv = params.getRequired("environment");

        // Get job configs
        JobConfigManager.setJobConfigsPath(userEnv);
        String jobPropertiesPath = JobConfigManager.getJobConfigsPath(userEnv);
        JobConfigManager.setJobConfigs(tEnv, jobPropertiesPath);
        JobConfigManager.getJobConfigs(tEnv);

        // Checkpoint Configs
        env.enableCheckpointing(600000);
        // set mode to exactly-once (default)
        env.getCheckpointConfig().setCheckpointingMode(CheckpointingMode.EXACTLY_ONCE);
        // make sure 900000 ms of progress happen between checkpoints
        env.getCheckpointConfig().setMinPauseBetweenCheckpoints(300000);
        // checkpoints have to complete within this minute, or are discarded
        env.getCheckpointConfig().setCheckpointTimeout(300000);
        // only two consecutive checkpoint failures are tolerated
        env.getCheckpointConfig().setTolerableCheckpointFailureNumber(2);
        // allow only one checkpoint to be in progress at the same time
        env.getCheckpointConfig().setMaxConcurrentCheckpoints(1);
        // enable externalized checkpoints which are retained after job cancellation
        env.getCheckpointConfig()
                .setExternalizedCheckpointCleanup(
                        CheckpointConfig.ExternalizedCheckpointCleanup.DELETE_ON_CANCELLATION);
        // enable unaligned checkpoints
        env.getCheckpointConfig().enableUnalignedCheckpoints();
        // sets a RocksDB checkpoint storage where checkpoint snapshots will be incrementally
        // written
        env.setStateBackend(new EmbeddedRocksDBStateBackend(true));
        env.getCheckpointConfig().setCheckpointStorage(CHECKPOINTS_STORAGE_LOCATION);

        String taskUpdatesCreateTable =
                String.format(
                        BLINKIT_STOREOPS_TASK_UPDATES,
                        TASK_UPDATES_SOURCE_TOPIC,
                        EVENTS_SOURCE_BROKERS,
                        EVENTS_SOURCE_CONSUMER_GROUP_ID);
        tEnv.executeSql(taskUpdatesCreateTable);

        String orderComplaintsCreateTable =
                String.format(
                        BLINKIT_STOREOPS_ORDER_COMPLAINTS,
                        COMPLAINTS_SOURCE_TOPIC,
                        EVENTS_SOURCE_BROKERS,
                        EVENTS_SOURCE_CONSUMER_GROUP_ID);
        tEnv.executeSql(orderComplaintsCreateTable);

        String shiftDetailsCreateTable =
                String.format(
                        BLINKIT_STOREOPS_SHIFT_DETAILS,
                        OPS_SHIFT_SOURCE_TOPIC,
                        EVENTS_SOURCE_BROKERS,
                        EVENTS_SOURCE_CONSUMER_GROUP_ID);
        tEnv.executeSql(shiftDetailsCreateTable);

        String storePerformanceCreateTable =
                String.format(
                        STORE_PERFORMANCE_CREATE_TABLE,
                        STORE_PERFORMANCE_EVENTS_SINK_TOPIC,
                        STORE_PERFORMANCE_EVENTS_SINK_BROKERS);
        tEnv.executeSql(storePerformanceCreateTable);

        // Execute program, beginning computation.
        tEnv.executeSql(INSERT_STORE_PERFORMANCE_METRICS);
    }
}
