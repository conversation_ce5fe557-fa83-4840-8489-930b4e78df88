# Properties for production environment

#Checkpoints
CHECKPOINTS_STORAGE_LOCATION = s3a://grofers-test-dse-singapore/flink-streams/core-streams/store-performance-events/checkpoints/

# Events Source
EVENTS_SOURCE_BROKERS = ****************************************.groupblinkit-agent.kafka.discoveryv2.prod-y.ap-southeast-1.warpstream.com:9092
EVENTS_SOURCE_CONSUMER_GROUP_ID = corestreams.storeops.test.store-performance-events.group-v1
TASK_UPDATES_SOURCE_TOPIC = blinkit.storeops.task-updates
COMPLAINTS_SOURCE_TOPIC = blinkit.storeops.order-complaint
OPS_SHIFT_SOURCE_TOPIC = blinkit.ops-management.shift-details

# Events Sink
STORE_PERFORMANCE_EVENTS_SINK_BROKERS = b-1.testdatacommon.vkkz3t.c5.kafka.ap-southeast-1.amazonaws.com:9092,b-2.testdatacommon.vkkz3t.c5.kafka.ap-southeast-1.amazonaws.com:9092
STORE_PERFORMANCE_EVENTS_SINK_TOPIC = corestreams.storeops.store-performance-events
