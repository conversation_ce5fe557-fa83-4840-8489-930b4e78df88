name: StoreOps Order Metrics
image: 442534439095.dkr.ecr.ap-southeast-1.amazonaws.com/data/flink-streams/observability/storeops-order-metrics
flink_config:
  taskmanager.memory.process.size: 4096m
  jobmanager.memory.process.size: 1024m
  taskmanager.memory.managed.size: 0
  taskmanager.numberOfTaskSlots: 1
  parallelism.default: 2
  process.working-dir: storeops-order-metrics/process
  state.backend.local-recovery: true
  taskmanager.resource-id: TaskManager_StoreOpsOrderMetrics
deployment_config:
  kubernetes.rest-service.exposed.type: NodePort
  metrics.reporters: prom
  metrics.reporter.prom.class: org.apache.flink.metrics.prometheus.PrometheusReporter
allow_non_restored_state: False # Set to True when changes are made to Operator
secrets:
  master:
    LOGISTICS_HOSTNAME: "dse/postgres/logistics_server/logistics_server_reader_redash:host"
    LOGISTICS_PORT: "dse/postgres/logistics_server/logistics_server_reader_redash:port"
    LOGISTICS_DB: "dse/postgres/logistics_server/logistics_server_reader_redash:dbname"
    LOGISTICS_DB_USER: "dse/postgres/logistics_server/logistics_server_reader_redash:user"
    LOGISTICS_DB_PASSWORD: "dse/postgres/logistics_server/logistics_server_reader_redash:password"
codeartifact_access: True
