ORDER_LIFECYCLE_EVENTS_SOURCE_BROKERS = ****************************************.groupblinkit-agent.kafka.discoveryv2.prod-y.ap-southeast-1.warpstream.com:9092
ORDER_LIFECYCLE_EVENTS_SOURCE_CONSUMER_GROUP_ID = blinkit.data.flink-storeops-order-metrics
ORDER_LIFECYCLE_EVENTS_SOURCE_TOPIC = blinkit.storeops.order-lifecycle
LOGISTICS_EVENTS_SOURCE_BROKERS = b-1.proddatasrp.wv3mhc.c5.kafka.ap-southeast-1.amazonaws.com:9092,b-2.proddatasrp.wv3mhc.c5.kafka.ap-southeast-1.amazonaws.com:9092,b-3.proddatasrp.wv3mhc.c5.kafka.ap-southeast-1.amazonaws.com:9092
DARK_STORE_PROJECTIONS_TOPIC = postgres.logistics_server.public.logistics_dark_store_projection
DARK_STORE_PROJECTIONS_CONSUMER_GROUP = observability.storeops_order_metrics.logistics_dark_store_projection.group
LOGISTICS_EVENTS_SCHEMA_REGISTRY = http://source-replication-common-schema-registry-external-data.prod-sgp-k8s.grofer.io:8081
ORDER_METRICS_TOPIC = observability.metrics.storeops-order-metrics
ORDER_METRICS_BROKERS = b-1.prod-data-corestr.uynfnv.c4.kafka.ap-southeast-1.amazonaws.com:9092,b-2.prod-data-corestr.uynfnv.c4.kafka.ap-southeast-1.amazonaws.com:9092,b-3.prod-data-corestr.uynfnv.c4.kafka.ap-southeast-1.amazonaws.com:9092
STATE_BACKEND_LOCATION = s3a://prod-data-flink-states/flink-streams/observability/storeops-order-metrics/checkpoints/
