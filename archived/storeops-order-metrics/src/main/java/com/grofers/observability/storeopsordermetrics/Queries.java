package com.grofers.observability.storeopsordermetrics;

public class Queries {

    public static final String STOREOPS_ORDER_LIFECYCLE =
            "CREATE TABLE IF NOT EXISTS order_lifecycle_events (assign_time BIGINT, "
                + " auditor_employee_id STRING, auditor_name STRING, created_at BIGINT, updated_at"
                + " BIGINT, current_state STRING, drop_zone STRING,  order_id STRING, order_type"
                + " STRING, pick_completion_time BIGINT, picker_id STRING, picker_name STRING,"
                + " picking_start_time BIGINT, putter_employee_id STRING, putter_name STRING,"
                + " site_id STRING, sub_order_id STRING, audit_reason STRING, customer_return_type"
                + " STRING, flink_event_time_epoch AS TO_TIMESTAMP_LTZ(created_at,3), WATERMARK"
                + " FOR flink_event_time_epoch AS flink_event_time_epoch - INTERVAL '90' seconds )"
                + " WITH (  'connector' = 'kafka', 'format' = 'json', 'topic' = '%s',"
                + " 'properties.bootstrap.servers' = '%s', 'properties.group.id' = '%s',"
                + " 'scan.startup.mode' = 'latest-offset'   );";

    public static final String DARK_STORE_PROJECTIONS =
            "CREATE TABLE IF NOT EXISTS logistics_dark_store_projection ( order_id STRING,"
                + " update_ts STRING, fill_rate DOUBLE, delivery_fe_id STRING,"
                + " trip_start_timestamp STRING, delivery_timestamp STRING, current_state STRING,"
                + " update_ts_epoch AS UNIX_TIMESTAMP(DATE_FORMAT(stringToTimestamp(update_ts),"
                + " 'yyyy-MM-dd HH:mm:ss')) * 1000 , flink_update_ts AS"
                + " stringToTimestamp(update_ts)  ) WITH ( 'connector' = 'kafka', 'topic' = '%s',"
                + " 'properties.bootstrap.servers' = '%s', 'properties.group.id' = '%s', 'format'"
                + " = 'debezium-avro-confluent', 'debezium-avro-confluent.url' ="
                + " '%s','scan.startup.mode' = 'latest-offset' )";

    public static final String STOREOPS_ORDER_METRICS =
            "CREATE TABLE IF NOT EXISTS storeops_order_metrics ( order_id STRING, delivery_fe_id"
                + " STRING, delivery_fe_name STRING, fill_rate DOUBLE, current_state STRING,"
                + " current_delivery_start BIGINT, current_delivery_end BIGINT, assign_time"
                + " BIGINT, auditor_employee_id STRING, auditor_name STRING, created_at BIGINT,"
                + " updated_at BIGINT, drop_zone STRING,  order_type STRING, picker_employee_id"
                + " STRING, picker_name STRING, picking_start_time BIGINT, pick_completion_time"
                + " BIGINT, putter_employee_id STRING, putter_name STRING, store_id STRING,"
                + " sub_order_id STRING, audit_reason STRING, customer_return_type STRING, PRIMARY"
                + " KEY (sub_order_id) NOT ENFORCED ) WITH (    'connector' = 'upsert-kafka',   "
                + " 'topic' = '%s',    'properties.bootstrap.servers' = '%s',    'key.format' ="
                + " 'json',    'value.format' = 'json' ); ";
}
