package com.grofers.corestreams.storemanpowerutilisation;

import org.apache.flink.runtime.testutils.MiniClusterResourceConfiguration;
import org.apache.flink.test.util.MiniClusterWithClientResource;

import com.grofers.corestreams.storemanpowerutilisation.flows.StoreManpowerUtilisationIntegrationTest;
import com.grofers.corestreams.storemanpowerutilisation.jobconfigs.StoreManpowerUtilisationConfigsTest;
import com.grofers.corestreams.storemanpowerutilisation.jobconfigs.StoreManpowerUtilisationEnvironmentTest;
import org.junit.BeforeClass;
import org.junit.runner.RunWith;
import org.junit.runners.Suite;

// Test Suite
@RunWith(Suite.class)
@Suite.SuiteClasses({
    StoreManpowerUtilisationConfigsTest.class, // JobConfigs Test
    StoreManpowerUtilisationEnvironmentTest.class, // Environment test
    StoreManpowerUtilisationIntegrationTest.class, // Integration test
})
public class StoreManpowerUtilisationTest {
    @BeforeClass
    public static void setUpFlinkCluster() {
        MiniClusterWithClientResource flinkTestCluster =
                new MiniClusterWithClientResource(
                        new MiniClusterResourceConfiguration.Builder()
                                .setNumberSlotsPerTaskManager(2)
                                .setNumberTaskManagers(1)
                                .build());
    }
}
