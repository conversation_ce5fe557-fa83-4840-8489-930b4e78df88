package com.grofers.corestreams.storemanpowerutilisation.jobconfigs;

import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;

import com.grofers.corestreams.storemanpowerutilisation.configs.JobConfigs;
import com.grofers.corestreams.storemanpowerutilisation.configs.JobConfigsManager;
import org.junit.Test;
import org.junit.jupiter.api.Assertions;

import java.io.FileInputStream;
import java.lang.reflect.Field;
import java.util.Properties;

public class StoreManpowerUtilisationConfigsTest {
    private static Field[] jobConfigsFields;
    private static String jobPropertiesPath;

    public static String getJobPropertiesPath() throws Exception {
        String userEnv = "stage";
        // Set PropertiesPath for userEnv
        JobConfigsManager.setJobConfigsPath(userEnv);
        // Get PropertiesPath for userEnv
        String jobPropertiesPath = JobConfigsManager.getJobConfigsPath(userEnv);
        return jobPropertiesPath;
    }

    public static Field[] getJobConfigsFields() throws Exception {
        StreamExecutionEnvironment streamExecutionTestEnvironment;
        // Initialise stream environment
        streamExecutionTestEnvironment = StreamExecutionEnvironment.getExecutionEnvironment();
        streamExecutionTestEnvironment.setParallelism(1);
        jobConfigsFields = JobConfigs.class.getDeclaredFields();
        jobPropertiesPath = getJobPropertiesPath();
        // Get Global Job Properties
        JobConfigsManager.setJobConfigs(streamExecutionTestEnvironment, jobPropertiesPath);
        // Get Global Job Properties
        JobConfigsManager.getJobConfigs(streamExecutionTestEnvironment);
        return jobConfigsFields;
    }

    @Test
    public void jobConfigsValuesSetUpTest() throws Exception {

        // Get values from jobConfigs
        jobConfigsFields = getJobConfigsFields();
        // Get values from properties file
        jobPropertiesPath = getJobPropertiesPath();
        FileInputStream fis = new FileInputStream(jobPropertiesPath);
        Properties properties = new Properties();
        properties.load(fis);

        for (Field field : jobConfigsFields) {
            // Get properties value based on jobConfigs
            String propertiesValue = properties.getProperty(field.getName());

            // Get values from jobConfigs
            Object jobConfigsValue = field.get(jobConfigsFields);

            if (field.getType().toString().toLowerCase().contains("string")) {
                // Directly compare values
                Assertions.assertEquals(propertiesValue, jobConfigsValue);
            } else if (field.getType().toString().toLowerCase().contains("float")) {
                // Convert the both values to float
                Assertions.assertEquals(Float.parseFloat(propertiesValue), jobConfigsValue);
            } else if (field.getType().toString().toLowerCase().contains("double")) {
                // Convert both values to double
                Assertions.assertEquals(Double.parseDouble(propertiesValue), jobConfigsValue);
            }
        }
    }

    @Test
    public void floatComparisonTest() throws Exception {
        jobConfigsFields = getJobConfigsFields();
        float DISRUPTION_INDEX_PICKING = (float) jobConfigsFields[10].get(jobConfigsFields);
        // Non-negative
        Assertions.assertEquals(Float.compare(5f, DISRUPTION_INDEX_PICKING), 1);
        // Negative
        Assertions.assertEquals(Float.compare(0.05f, DISRUPTION_INDEX_PICKING), -1);
        // Equals
        Assertions.assertEquals(Float.compare(2f, DISRUPTION_INDEX_PICKING), 0);
    }
}
