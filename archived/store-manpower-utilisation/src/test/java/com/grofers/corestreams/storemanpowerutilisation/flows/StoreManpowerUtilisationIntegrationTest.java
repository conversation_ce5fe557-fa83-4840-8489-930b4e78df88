package com.grofers.corestreams.storemanpowerutilisation.flows;

import org.apache.flink.api.common.JobExecutionResult;
import org.apache.flink.streaming.api.datastream.DataStreamSource;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.streaming.api.functions.sink.SinkFunction;
import org.apache.flink.streaming.api.functions.source.SourceFunction;
import org.apache.flink.streaming.api.watermark.Watermark;
import org.apache.flink.streaming.util.*;

import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.node.ObjectNode;

import com.grofers.corestreams.storemanpowerutilisation.StoreManpowerUtilisation;
import com.grofers.corestreams.storemanpowerutilisation.configs.JobConfigs;
import com.grofers.corestreams.storemanpowerutilisation.configs.JobConfigsManager;
import com.grofers.corestreams.storemanpowerutilisation.datatypes.StoreAverageDisruption;
import com.grofers.corestreams.storemanpowerutilisation.utils.DataGenerator;
import org.junit.Before;
import org.junit.Test;
import org.junit.jupiter.api.Assertions;

import java.time.Instant;
import java.util.*;
import java.util.stream.Collectors;

public class StoreManpowerUtilisationIntegrationTest {
    private static StreamExecutionEnvironment streamExecutionTestEnvironment;
    private float DISRUPTION_INDEX_DELIVERY;
    private float DISRUPTION_INDEX_PICKING;

    @Before
    public void setUpJobConfigs() throws Exception {
        // Initialise stream environment
        streamExecutionTestEnvironment = StreamExecutionEnvironment.getExecutionEnvironment();
        streamExecutionTestEnvironment.setParallelism(1);
        String userEnv = "stage";
        // Set PropertiesPath for userEnv
        JobConfigsManager.setJobConfigsPath(userEnv);
        // Get PropertiesPath for userEnv
        String jobPropertiesPath = JobConfigsManager.getJobConfigsPath(userEnv);
        // Get Global Job Properties
        JobConfigsManager.setJobConfigs(streamExecutionTestEnvironment, jobPropertiesPath);
        // Get Global Job Properties
        JobConfigs globalJobConfigs =
                JobConfigsManager.getJobConfigs(streamExecutionTestEnvironment);
        // Get jon configs global map
        Map<String, String> globalJobConfigsMap =
                streamExecutionTestEnvironment.getConfig().getGlobalJobParameters().toMap();
        // Get the threshold values from global job configs
        this.DISRUPTION_INDEX_DELIVERY =
                Float.parseFloat(globalJobConfigsMap.get("DISRUPTION_INDEX_DELIVERY"));
        this.DISRUPTION_INDEX_PICKING =
                Float.parseFloat(globalJobConfigsMap.get("DISRUPTION_INDEX_PICKING"));
    }

    /** Source Function replicating kafka source behavior */
    public static class StoreDisruptionStateEvent implements SourceFunction<ObjectNode> {

        private final ObjectNode[] events;

        public StoreDisruptionStateEvent(ObjectNode[] events) {
            this.events = events;
        }

        @Override
        public void run(SourceContext<ObjectNode> ctx) throws Exception {
            for (ObjectNode event : events) {
                ctx.collectWithTimestamp(
                        event,
                        Instant.parse(event.get("value").get("modified_ts").asText())
                                .toEpochMilli());
                ctx.emitWatermark(
                        new Watermark(
                                Instant.parse(event.get("value").get("modified_ts").asText())
                                        .toEpochMilli()));
            }
        }

        @Override
        public void cancel() {}
    }

    /** Flink Sink to collect values for testing */
    public static class CollectStoreDisruptionStateSink
            implements SinkFunction<StoreAverageDisruption> {
        // Used CopyOnWriteArrayList for parallelism
        public static final List<StoreAverageDisruption> values = new ArrayList<>();

        @Override
        public synchronized void invoke(StoreAverageDisruption value, Context context)
                throws Exception {
            values.add(value);
        }
    }

    @Test
    public void storeDisruptionStateIntegrationTest() throws Exception {

        List<ObjectNode> storeDisruptionStateEventList =
                DataGenerator.getStoreDisruptionStateEventList();
        DataStreamSource<ObjectNode> storeDisruptionStateSource =
                streamExecutionTestEnvironment.addSource(
                        new StoreDisruptionStateEvent(
                                storeDisruptionStateEventList.toArray(new ObjectNode[0])));
        // Sink to store all disruption states
        CollectStoreDisruptionStateSink collectStoreDisruptionStateSink =
                new CollectStoreDisruptionStateSink();

        JobExecutionResult jobExecutionResult =
                new StoreManpowerUtilisation(
                                storeDisruptionStateSource, collectStoreDisruptionStateSink)
                        .execute(streamExecutionTestEnvironment);

        // Verify Calculated Disruption Index Delivery values
        List<Float> expectedAvgDIDeliveryValues =
                Arrays.asList(
                        // Calculated averages with the window
                        0.0f, // 0 First value
                        0.2835f, // (0 + 0.567) / 2
                        1.8556666f, // (0.0 + 0.567 + 5) / 3
                        2.7835f, // (0.567 + 5) / 2
                        5.0f // 5 Last value
                        );
        List<Float> actualAvgDIDeliveryValues =
                CollectStoreDisruptionStateSink.values.stream()
                        .filter(e -> e.getStoreID() == 123)
                        .map(StoreAverageDisruption::getAverageDisruptionIndexDelivery)
                        .distinct()
                        .collect(Collectors.toList());
        Assertions.assertEquals(expectedAvgDIDeliveryValues, actualAvgDIDeliveryValues);

        // Verify Calculated Disruption Index Picking values
        List<Float> expectedAvgDIPickingValues =
                Arrays.asList(
                        // Calculated averages with the window
                        0.0f, // 0 First value
                        0.117f, // (0 + 0.234) / 2
                        0.341f, // (0.0 + 0.234 + 0.789) / 3
                        0.5115f, // (0.234 + 0.789) / 2
                        0.789f // 0.789 Last value
                        );
        List<Float> actualAvgDIPickingValues =
                CollectStoreDisruptionStateSink.values.stream()
                        .filter(e -> e.getStoreID() == 123)
                        .map(StoreAverageDisruption::getAverageDisruptionIndexPicking)
                        .distinct()
                        .collect(Collectors.toList());
        Assertions.assertEquals(expectedAvgDIPickingValues, actualAvgDIPickingValues);

        // Zero DI
        Assertions.assertEquals(
                121,
                CollectStoreDisruptionStateSink.values.stream()
                        .filter(
                                e ->
                                        (Float.compare(e.getAverageDisruptionIndexPicking(), 0f)
                                                        == 0
                                                || Float.compare(
                                                                e
                                                                        .getAverageDisruptionIndexDelivery(),
                                                                0f)
                                                        == 0))
                        .toArray()
                        .length);

        // Checks if the all events have correct boolean value assigned for isStoreDisrupted
        Assertions.assertArrayEquals(
                CollectStoreDisruptionStateSink.values.stream()
                        .filter(
                                e ->
                                        (
                                                // For disrupted Values
                                                (Float.compare(
                                                                                e
                                                                                        .getAverageDisruptionIndexDelivery(),
                                                                                this
                                                                                        .DISRUPTION_INDEX_DELIVERY)
                                                                        > 0
                                                                || Float.compare(
                                                                                e
                                                                                        .getAverageDisruptionIndexPicking(),
                                                                                this
                                                                                        .DISRUPTION_INDEX_PICKING)
                                                                        > 0)
                                                        && e.getIsStoreDisrupted())
                                                || (
                                                // For Non-disrupted Values
                                                (Float.compare(
                                                                                e
                                                                                        .getAverageDisruptionIndexDelivery(),
                                                                                this
                                                                                        .DISRUPTION_INDEX_DELIVERY)
                                                                        < 0
                                                                || Float.compare(
                                                                                e
                                                                                        .getAverageDisruptionIndexPicking(),
                                                                                this
                                                                                        .DISRUPTION_INDEX_PICKING)
                                                                        < 0)
                                                        && !e.getIsStoreDisrupted()))
                        .toArray(),
                CollectStoreDisruptionStateSink.values.toArray());
    }
}
