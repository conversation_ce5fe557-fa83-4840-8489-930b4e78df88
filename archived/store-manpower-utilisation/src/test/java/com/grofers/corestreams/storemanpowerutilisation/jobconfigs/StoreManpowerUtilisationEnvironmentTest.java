package com.grofers.corestreams.storemanpowerutilisation.jobconfigs;

import org.apache.flink.annotation.VisibleForTesting;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;

import com.grofers.corestreams.storemanpowerutilisation.configs.JobConfigsManager;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.NullAndEmptySource;
import org.junit.jupiter.params.provider.ValueSource;

import java.util.HashMap;
import java.util.Map;

public class StoreManpowerUtilisationEnvironmentTest {
    private static StreamExecutionEnvironment streamExecutionTestEnvironment;

    @AfterEach
    @VisibleForTesting
    /** Function to clear jobPropertiesPath */
    public void clearJobPropertiesPath() {
        JobConfigsManager.jobConfigsPath = null;
    }

    @ParameterizedTest(name = "#{index} - Run test with args={0}")
    @NullAndEmptySource
    /** Function to test null and empty environments */
    public void testEnvironmentPropertiesForNullEnvirons(String userEnv) {
        // Throws an exception while setting null environments
        Assertions.assertTrue(
                Assertions.assertThrows(
                                JobConfigsManager.InvalidEnvironmentException.class,
                                () -> JobConfigsManager.setJobConfigsPath(userEnv))
                        .getMessage()
                        .contains("USER ENVIRONMENT IS INVALID"));
    }

    @ParameterizedTest(name = "#{index} - Run test with args={0}")
    @ValueSource(strings = {"prod", "stage", "xyz", "123", "@#$78"})
    /** Function to check whether variables are passed according to environment * */
    public void testEnvironmentPropertiesForNonNullEnvirons(String userEnv)
            throws JobConfigsManager.InvalidEnvironmentException {

        // Adding elements to env HashMap
        Map<String, String> envMap = new HashMap<>();
        envMap.put("prod", "src/main/resources/prod.properties");
        envMap.put("stage", "src/main/resources/stage.properties");
        envMap.put("xyz", "src/main/resources/stage.properties");
        envMap.put("123", "src/main/resources/stage.properties");
        envMap.put("@#$78", "src/main/resources/stage.properties");

        // Check whether properties are set before calling setter for JobPropertiesManager
        Assertions.assertNull(JobConfigsManager.getJobConfigsPath(userEnv));
        // Setting jobProperties
        JobConfigsManager.setJobConfigsPath(userEnv);
        Assertions.assertEquals(envMap.get(userEnv), JobConfigsManager.getJobConfigsPath(userEnv));
    }
}
