package com.grofers.corestreams.storemanpowerutilisation.serdes;

import org.apache.flink.api.common.serialization.SerializationSchema;
import org.apache.flink.streaming.connectors.kafka.KafkaSerializationSchema;

import com.grofers.corestreams.storemanpowerutilisation.datatypes.StoreAverageDisruption;
import org.apache.kafka.clients.producer.ProducerRecord;

import javax.annotation.Nullable;

import java.nio.charset.StandardCharsets;

public class StoreAverageDisruptionStateSerializationSchema
        implements KafkaSerializationSchema<StoreAverageDisruption> {

    private static final long serialVersionUID = -1L;
    private final String topic;

    public StoreAverageDisruptionStateSerializationSchema(String topic) {
        this.topic = topic;
    }

    @Override
    public void open(SerializationSchema.InitializationContext context) throws Exception {
        KafkaSerializationSchema.super.open(context);
    }

    @Override
    public ProducerRecord<byte[], byte[]> serialize(
            StoreAverageDisruption storeAverageDisruption, @Nullable Long aLong) {
        return new ProducerRecord<>(
                topic,
                storeAverageDisruption.getStoreID().toString().getBytes(StandardCharsets.UTF_8),
                storeAverageDisruption.toString().getBytes(StandardCharsets.UTF_8));
    }
}
