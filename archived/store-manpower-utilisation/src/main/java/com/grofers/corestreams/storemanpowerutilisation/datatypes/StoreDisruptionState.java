package com.grofers.corestreams.storemanpowerutilisation.datatypes;

public class StoreDisruptionState {
    private final Long storeID;
    private final String storeName;
    private final Integer batchSize;
    private final Float disruptionIndexDelivery;
    private final Float disruptionIndexPicking;
    private final Integer availableFieldExecutives;
    private final Integer availablePickers;
    private final Integer busyFieldExecutives;
    private final Integer busyPickers;
    private final Integer fieldExecutivesOrderBacklog;
    private final Integer pickerOrderBacklog;
    private final Long eventTimestamp;

    public StoreDisruptionState(
            Long storeID,
            String storeName,
            Integer batchSize,
            Float disruptionIndexDelivery,
            Float disruptionIndexPicking,
            Integer availableFieldExecutives,
            Integer availablePickers,
            Integer busyFieldExecutives,
            Integer busyPickers,
            Integer fieldExecutivesOrderBacklog,
            Integer pickerOrderBacklog,
            Long eventTimestamp) {
        this.storeID = storeID;
        this.storeName = storeName;
        this.batchSize = batchSize;
        this.disruptionIndexDelivery = disruptionIndexDelivery;
        this.disruptionIndexPicking = disruptionIndexPicking;
        this.availableFieldExecutives = availableFieldExecutives;
        this.availablePickers = availablePickers;
        this.busyFieldExecutives = busyFieldExecutives;
        this.busyPickers = busyPickers;
        this.fieldExecutivesOrderBacklog = fieldExecutivesOrderBacklog;
        this.pickerOrderBacklog = pickerOrderBacklog;
        this.eventTimestamp = eventTimestamp;
    }

    public Long getStoreID() {
        return storeID;
    }

    public String getStoreName() {
        return storeName;
    }

    public Integer getBatchSize() {
        return batchSize;
    }

    public Long getEventTimestamp() {
        return eventTimestamp;
    }

    public Float getDisruptionIndexDelivery() {
        return disruptionIndexDelivery;
    }

    public Integer getAvailableFieldExecutives() {
        return availableFieldExecutives;
    }

    public Integer getAvailablePickers() {
        return availablePickers;
    }

    public Integer getBusyFieldExecutives() {
        return busyFieldExecutives;
    }

    public Integer getBusyPickers() {
        return busyPickers;
    }

    public Integer getFieldExecutivesOrderBacklog() {
        return fieldExecutivesOrderBacklog;
    }

    public Integer getPickerOrderBacklog() {
        return pickerOrderBacklog;
    }

    public Float getDisruptionIndexPicking() {
        return disruptionIndexPicking;
    }
}
