package com.grofers.corestreams.storemanpowerutilisation.configs;

public final class JobConfigs {
    public static String CHECKPOINTS_STORAGE_LOCATION;
    public static String EVENTS_SOURCE_BROKERS;
    public static String EVENTS_SOURCE_CONSUMER_GROUP_ID;
    public static String EVENTS_SOURCE_TOPIC;
    public static String STORE_MANPOWER_UTILISATION_SINK_BROKERS;
    public static String STORE_MANPOWER_UTILISATION_SINK_TOPIC;
    public static int IDLENESS_TIME_IN_SECS;
    public static int SLIDING_WINDOW_SIZE_IN_MINS;
    public static int SLIDING_WINDOW_SLIDE_IN_SECS;
    public static float DISRUPTION_INDEX_DELIVERY;
    public static float DISRUPTION_INDEX_PICKING;

    public JobConfigs(
            String CHECKPOINTS_STORAGE_LOCATION,
            String EVENTS_SOURCE_BROKERS,
            String EVENTS_SOURCE_CONSUMER_GROUP_ID,
            String EVENTS_SOURCE_TOPIC,
            String STORE_MANPOWER_UTILISATION_SINK_BROKERS,
            String STORE_MANPOWER_UTILISATION_SINK_TOPIC,
            int IDLENESS_TIME_IN_SECS,
            int SLIDING_WINDOW_SIZE_IN_MINS,
            int SLIDING_WINDOW_SLIDE_IN_SECS,
            float DISRUPTION_INDEX_DELIVERY,
            float DISRUPTION_INDEX_PICKING) {

        JobConfigs.CHECKPOINTS_STORAGE_LOCATION = CHECKPOINTS_STORAGE_LOCATION;
        JobConfigs.EVENTS_SOURCE_BROKERS = EVENTS_SOURCE_BROKERS;
        JobConfigs.EVENTS_SOURCE_CONSUMER_GROUP_ID = EVENTS_SOURCE_CONSUMER_GROUP_ID;
        JobConfigs.EVENTS_SOURCE_TOPIC = EVENTS_SOURCE_TOPIC;
        JobConfigs.STORE_MANPOWER_UTILISATION_SINK_BROKERS =
                STORE_MANPOWER_UTILISATION_SINK_BROKERS;
        JobConfigs.STORE_MANPOWER_UTILISATION_SINK_TOPIC = STORE_MANPOWER_UTILISATION_SINK_TOPIC;
        JobConfigs.IDLENESS_TIME_IN_SECS = IDLENESS_TIME_IN_SECS;
        JobConfigs.SLIDING_WINDOW_SIZE_IN_MINS = SLIDING_WINDOW_SIZE_IN_MINS;
        JobConfigs.SLIDING_WINDOW_SLIDE_IN_SECS = SLIDING_WINDOW_SLIDE_IN_SECS;
        JobConfigs.DISRUPTION_INDEX_DELIVERY = DISRUPTION_INDEX_DELIVERY;
        JobConfigs.DISRUPTION_INDEX_PICKING = DISRUPTION_INDEX_PICKING;
    }
}
