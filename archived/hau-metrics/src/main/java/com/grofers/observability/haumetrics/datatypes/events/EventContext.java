package com.grofers.observability.haumetrics.datatypes.events;

import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.annotation.JsonProperty;

@JsonIgnoreProperties(ignoreUnknown = true)
public class EventContext {

    private final ContextDevice device;
    private final ContextOs os;
    private final ContextTraits traits;

    public EventContext(
            @JsonProperty(value = "device", required = true) ContextDevice device,
            @JsonProperty(value = "os", required = true) ContextOs os,
            @JsonProperty(value = "traits", required = true) ContextTraits traits) {
        this.device = device;
        this.os = os;
        this.traits = traits;
    }

    public ContextDevice accessDevice() {
        return this.device;
    }

    public ContextOs accessOs() {
        return this.os;
    }

    public ContextTraits accessTraits() {
        return this.traits;
    }
}
