/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.grofers.observability.haumetrics;

import org.apache.flink.api.common.ExecutionConfig;
import org.apache.flink.api.common.JobExecutionResult;
import org.apache.flink.api.common.eventtime.WatermarkStrategy;
import org.apache.flink.api.common.functions.FilterFunction;
import org.apache.flink.api.connector.sink.Sink;
import org.apache.flink.api.java.tuple.Tuple2;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.connector.base.DeliveryGuarantee;
import org.apache.flink.connector.kafka.sink.KafkaSink;
import org.apache.flink.connector.kafka.source.KafkaSource;
import org.apache.flink.connector.kafka.source.enumerator.initializer.OffsetsInitializer;
import org.apache.flink.streaming.api.CheckpointingMode;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.datastream.DataStreamSource;
import org.apache.flink.streaming.api.environment.CheckpointConfig;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.streaming.api.functions.windowing.ProcessWindowFunction;
import org.apache.flink.streaming.api.windowing.assigners.TumblingEventTimeWindows;
import org.apache.flink.streaming.api.windowing.time.Time;
import org.apache.flink.streaming.api.windowing.windows.TimeWindow;
import org.apache.flink.util.Collector;

import com.grofers.observability.haumetrics.configs.JobConfig;
import com.grofers.observability.haumetrics.configs.JobConfigManager;
import com.grofers.observability.haumetrics.datatypes.CityHAUMetric;
import com.grofers.observability.haumetrics.datatypes.MerchantHAUMetric;
import com.grofers.observability.haumetrics.datatypes.events.Event;
import com.grofers.observability.haumetrics.serdes.CityHAUMetricRecordSerializationSchema;
import com.grofers.observability.haumetrics.serdes.JsonDeserializationSchema;
import com.grofers.observability.haumetrics.serdes.MerchantHAUMetricRecordSerializationSchema;
import org.apache.kafka.clients.consumer.OffsetResetStrategy;

import java.util.HashSet;
import java.util.Set;

import static java.time.Duration.ofSeconds;

/** Flink Streaming Job for calculating Hourly Active Users */
public class HAUMetrics {

    private final DataStreamSource<Event> impressionEventsSource;
    private final Sink<MerchantHAUMetric, ?, ?, ?> merchantHauMetricSink;
    private final Sink<CityHAUMetric, ?, ?, ?> cityHauMetricSink;

    public HAUMetrics(
            DataStreamSource<Event> impressionEventsSource,
            Sink<MerchantHAUMetric, ?, ?, ?> merchantHauMetricSink,
            Sink<CityHAUMetric, ?, ?, ?> cityHauMetricSink) {
        this.impressionEventsSource = impressionEventsSource;
        this.merchantHauMetricSink = merchantHauMetricSink;
        this.cityHauMetricSink = cityHauMetricSink;
    }

    public static void main(String[] args) throws Exception {
        // set up the streaming execution environment
        final StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment();

        // Parameters for environment
        ParameterTool params = ParameterTool.fromArgs(args);
        String userEnv = params.getRequired("environment");

        // Set PropertiesPath for userEnv
        JobConfigManager.setJobConfigsPath(userEnv);
        // Get PropertiesPath for userEnv
        String jobPropertiesPath = JobConfigManager.getJobConfigsPath(userEnv);
        // Fetch job config
        JobConfigManager.setJobConfigs(env, jobPropertiesPath);
        JobConfigManager.getJobConfigs(env);

        // Checkpoint Configs
        // start a checkpoint every 240000 ms
        env.enableCheckpointing(240000);
        // set mode to exactly-once (default)
        env.getCheckpointConfig().setCheckpointingMode(CheckpointingMode.EXACTLY_ONCE);
        // make sure 120000 ms of progress happen between checkpoints
        env.getCheckpointConfig().setMinPauseBetweenCheckpoints(240000);
        // checkpoints have to complete within a minute, or are discarded
        env.getCheckpointConfig().setCheckpointTimeout(210000);
        // only two consecutive checkpoint failures are tolerated
        env.getCheckpointConfig().setTolerableCheckpointFailureNumber(2);
        // allow only one checkpoint to be in progress at the same time
        env.getCheckpointConfig().setMaxConcurrentCheckpoints(1);
        // enable externalized checkpoints which are retained after job cancellation
        env.getCheckpointConfig()
                .setExternalizedCheckpointCleanup(
                        CheckpointConfig.ExternalizedCheckpointCleanup.RETAIN_ON_CANCELLATION);
        // enables the experimental unaligned checkpoints
        env.getCheckpointConfig().enableUnalignedCheckpoints();
        // sets the checkpoint storage where checkpoint snapshots will be written
        env.getCheckpointConfig().setCheckpointStorage(JobConfig.CHECKPOINTS_STORAGE_LOCATION);
        // Enable checkpoint compression
        ExecutionConfig executionConfig = new ExecutionConfig();
        executionConfig.setUseSnapshotCompression(true);

        KafkaSource<Event> impressionEventsSource =
                KafkaSource.<Event>builder()
                        .setBootstrapServers(JobConfig.EVENTS_SOURCE_BROKERS)
                        .setGroupId(JobConfig.EVENTS_SOURCE_CONSUMER_GROUP_ID)
                        .setClientIdPrefix("hau_metrics-")
                        .setValueOnlyDeserializer(new JsonDeserializationSchema())
                        .setStartingOffsets(
                                OffsetsInitializer.committedOffsets(OffsetResetStrategy.LATEST))
                        .setTopics(JobConfig.EVENTS_SOURCE_TOPIC)
                        .setProperty("partition.discovery.interval.ms", "600000")
                        .setProperty("reconnect.backoff.max.ms", "300000")
                        .setProperty("reconnect.backoff.ms", "60000")
                        .build();

        WatermarkStrategy<Event> hauEventsSourceWatermarkStrategy =
                WatermarkStrategy.<Event>forBoundedOutOfOrderness(ofSeconds(300))
                        .withIdleness(ofSeconds(JobConfig.IDLENESS_TIME_IN_SECS))
                        .withTimestampAssigner(
                                (event, timestamp) -> event.accessEventTimestampEpochMilli());

        DataStreamSource<Event> impressionEventsStreamSource =
                env.fromSource(
                        impressionEventsSource,
                        hauEventsSourceWatermarkStrategy,
                        "impressionEventsSource");

        KafkaSink<CityHAUMetric> cityHauMetricsKafkaSink =
                KafkaSink.<CityHAUMetric>builder()
                        .setBootstrapServers(JobConfig.CITY_HAU_METRICS_SINK_BROKERS)
                        .setDeliverGuarantee(DeliveryGuarantee.AT_LEAST_ONCE)
                        .setTransactionalIdPrefix("city_hau_metrics-")
                        .setRecordSerializer(
                                new CityHAUMetricRecordSerializationSchema(
                                        JobConfig.CITY_HAU_METRICS_SINK_TOPIC))
                        .build();

        KafkaSink<MerchantHAUMetric> merchantHauMetricsKafkaSink =
                KafkaSink.<MerchantHAUMetric>builder()
                        .setBootstrapServers(JobConfig.MERCHANT_HAU_METRICS_SINK_BROKERS)
                        .setDeliverGuarantee(DeliveryGuarantee.AT_LEAST_ONCE)
                        .setTransactionalIdPrefix("merchant_hau_metrics-")
                        .setRecordSerializer(
                                new MerchantHAUMetricRecordSerializationSchema(
                                        JobConfig.MERCHANT_HAU_METRICS_SINK_TOPIC))
                        .build();

        HAUMetrics hauMetrics =
                new HAUMetrics(
                        impressionEventsStreamSource,
                        merchantHauMetricsKafkaSink,
                        cityHauMetricsKafkaSink);
        hauMetrics.execute(env);
    }

    private FilterFunction<Event> getFilterEventFunction() {
        return new FilterFunction<Event>() {
            private final String[] FILTER_EVENTS = {
                "App Launch",
                "Checkout Step Viewed",
                "Order Acknowledged",
                "Cart Viewed",
                "Search Results Viewed",
                "Product Added",
                "Homepage Visit"
            };

            @Override
            public boolean filter(Event event) throws Exception {
                if (event == null) {
                    return false;
                }
                if (event.accessContext().accessTraits().accessCity() == null) {
                    return false;
                }
                for (String allowedEvent : FILTER_EVENTS) {
                    if (allowedEvent.equalsIgnoreCase(event.accessEventName())) {
                        return true;
                    }
                }
                return false;
            }
        };
    }

    public JobExecutionResult execute(StreamExecutionEnvironment environment) throws Exception {

        DataStream<Event> filteredEvents =
                this.impressionEventsSource
                        .name("impression-event-source")
                        .uid("impression-events-source")
                        .setParallelism(3)
                        .filter(getFilterEventFunction())
                        .uid("filter-null-city-dau-event")
                        .name("filter-null-city-dau-event")
                        .setParallelism(3);

        filteredEvents
                .keyBy(e -> e.accessContext().accessTraits().accessCity())
                .window(TumblingEventTimeWindows.of(Time.hours(1), Time.minutes(-30)))
                .allowedLateness(Time.seconds(JobConfig.IDLENESS_TIME_IN_SECS))
                .trigger(EventPeriodicTimeTrigger.create(60000L))
                .process(new CityHAUMetricsProcessFunction())
                .setParallelism(3)
                .name("city-hau-metrics-window")
                .uid("city-hau-metrics-window")
                .sinkTo(cityHauMetricSink)
                .setParallelism(1)
                .name("city-hau-metrics-sink")
                .uid("city-hau-metrics-sink");

        filteredEvents
                .filter(event -> event.accessContext().accessTraits().accessMerchantId() != null)
                .setParallelism(3)
                .uid("filter-null-merchant-hau-event")
                .name("filter-null-merchant-hau-event")
                .keyBy(Event::accessMerchantHAUKey)
                .window(TumblingEventTimeWindows.of(Time.hours(1), Time.minutes(-30)))
                .allowedLateness(Time.seconds(JobConfig.IDLENESS_TIME_IN_SECS))
                .trigger(EventPeriodicTimeTrigger.create(60000L))
                .process(new MerchantHAUMetricsProcessFunction())
                .setParallelism(3)
                .name("merchant-hau-metrics-window")
                .uid("merchant-hau-metrics-window")
                .sinkTo(merchantHauMetricSink)
                .setParallelism(1)
                .name("merchant-hau-metrics-sink")
                .uid("merchant-hau-metrics-sink");

        return environment.execute("observability.hau-metrics");
    }

    public static class CityHAUMetricsProcessFunction
            extends ProcessWindowFunction<Event, CityHAUMetric, String, TimeWindow> {

        @Override
        public void process(
                String key,
                ProcessWindowFunction<Event, CityHAUMetric, String, TimeWindow>.Context context,
                Iterable<Event> iterable,
                Collector<CityHAUMetric> collector) {
            Set<String> deviceIDs = new HashSet<>();
            for (Event e : iterable) {
                deviceIDs.add(
                        e.accessContext()
                                .accessDevice()
                                .accessUniqueId(e.accessContext().accessOs().accessOsName()));
            }

            collector.collect(
                    new CityHAUMetric(
                            key,
                            context.window().getStart(),
                            context.currentWatermark(),
                            deviceIDs.size()));
        }
    }

    public static class MerchantHAUMetricsProcessFunction
            extends ProcessWindowFunction<
                    Event, MerchantHAUMetric, Tuple2<String, Integer>, TimeWindow> {

        @Override
        public void process(
                Tuple2<String, Integer> key,
                ProcessWindowFunction<Event, MerchantHAUMetric, Tuple2<String, Integer>, TimeWindow>
                                .Context
                        context,
                Iterable<Event> iterable,
                Collector<MerchantHAUMetric> collector) {
            Set<String> deviceIDs = new HashSet<>();
            for (Event e : iterable) {
                deviceIDs.add(
                        e.accessContext()
                                .accessDevice()
                                .accessUniqueId(e.accessContext().accessOs().accessOsName()));
            }

            collector.collect(
                    new MerchantHAUMetric(
                            key.f0,
                            key.f1,
                            context.window().getStart(),
                            context.currentWatermark(),
                            deviceIDs.size()));
        }
    }
}
