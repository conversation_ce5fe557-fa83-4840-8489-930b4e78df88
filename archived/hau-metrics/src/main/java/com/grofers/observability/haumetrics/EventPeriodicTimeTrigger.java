package com.grofers.observability.haumetrics;

import org.apache.flink.api.common.functions.ReduceFunction;
import org.apache.flink.api.common.state.ReducingState;
import org.apache.flink.api.common.state.ReducingStateDescriptor;
import org.apache.flink.api.common.typeutils.base.LongSerializer;
import org.apache.flink.streaming.api.windowing.triggers.Trigger;
import org.apache.flink.streaming.api.windowing.triggers.TriggerResult;
import org.apache.flink.streaming.api.windowing.windows.TimeWindow;

public class EventPeriodicTimeTrigger extends Trigger<Object, TimeWindow> {
    private static final long serialVersionUID = 1L;
    private final long intervalMs;
    private final ReducingStateDescriptor<Long> stateDesc;

    private EventPeriodicTimeTrigger(Long intervalMs) {
        this.stateDesc =
                new ReducingStateDescriptor<>(
                        "intervalCount", new IncrementInterval(), LongSerializer.INSTANCE);
        this.intervalMs = intervalMs;
    }

    public TriggerResult onElement(
            Object element, long timestamp, TimeWindow window, TriggerContext ctx)
            throws Exception {
        long overallIntervalCount = (window.getEnd() - window.getStart()) / this.intervalMs;
        long currentReqIntervalCount =
                (ctx.getCurrentWatermark() - window.getStart()) / this.intervalMs;
        ReducingState<Long> intervalCount = ctx.getPartitionedState(this.stateDesc);
        if (intervalCount.get() == null) {
            intervalCount.add(1L);
        }
        if (intervalCount.get() < currentReqIntervalCount
                && currentReqIntervalCount < overallIntervalCount) {
            intervalCount.add(1L);
            return TriggerResult.FIRE;
        } else {
            ctx.registerEventTimeTimer(window.maxTimestamp());
            return TriggerResult.CONTINUE;
        }
    }

    public TriggerResult onEventTime(long time, TimeWindow window, TriggerContext ctx) {
        return time == window.maxTimestamp() ? TriggerResult.FIRE : TriggerResult.CONTINUE;
    }

    public TriggerResult onProcessingTime(long time, TimeWindow window, TriggerContext ctx) {
        return TriggerResult.CONTINUE;
    }

    public void clear(TimeWindow window, TriggerContext ctx) {
        ctx.deleteEventTimeTimer(window.maxTimestamp());
    }

    public String toString() {
        return "EventPeriodicTimeTrigger()";
    }

    public static EventPeriodicTimeTrigger create(Long intervalMs) {
        return new EventPeriodicTimeTrigger(intervalMs);
    }

    private static class IncrementInterval implements ReduceFunction<Long> {
        private static final long serialVersionUID = 1L;

        private IncrementInterval() {}

        public Long reduce(Long value1, Long value2) {
            return value1 + value2;
        }
    }
}
