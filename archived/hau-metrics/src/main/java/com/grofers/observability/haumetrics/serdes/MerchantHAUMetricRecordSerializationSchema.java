package com.grofers.observability.haumetrics.serdes;

import org.apache.flink.api.common.serialization.SerializationSchema;
import org.apache.flink.connector.kafka.sink.KafkaRecordSerializationSchema;

import com.grofers.observability.haumetrics.datatypes.MerchantHAUMetric;
import org.apache.kafka.clients.producer.ProducerRecord;

import java.nio.charset.StandardCharsets;

public class MerchantHAUMetricRecordSerializationSchema
        implements KafkaRecordSerializationSchema<MerchantHAUMetric> {

    private static final long serialVersionUID = 1L;
    private final String topic;

    public MerchantHAUMetricRecordSerializationSchema(String topic) {
        this.topic = topic;
    }

    @Override
    public void open(
            SerializationSchema.InitializationContext context, KafkaSinkContext sinkContext)
            throws Exception {
        KafkaRecordSerializationSchema.super.open(context, sinkContext);
    }

    @Override
    public ProducerRecord<byte[], byte[]> serialize(
            MerchantHAUMetric metric, KafkaSinkContext kafkaSinkContext, Long aLong) {
        return new ProducerRecord<>(this.topic, metric.toString().getBytes(StandardCharsets.UTF_8));
    }
}
