package com.grofers.observability.haumetrics.configs;

import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;

import java.io.IOException;
import java.util.Map;

public class JobConfigManager {
    public static String jobConfigsPath;

    public static class InvalidEnvironmentException extends Exception {
        public InvalidEnvironmentException(String errorMsg) {
            super(errorMsg);
        }
    }

    public static void setJobConfigsPath(String userEnv) throws InvalidEnvironmentException {

        if (userEnv != null && !userEnv.isEmpty()) {
            if (userEnv.equalsIgnoreCase("prod")) {
                jobConfigsPath = "src/main/resources/prod.properties";
            } else {
                jobConfigsPath = "src/main/resources/stage.properties";
            }
        } else {
            throw new InvalidEnvironmentException("USER ENVIRONMENT IS INVALID");
        }
    }

    public static String getJobConfigsPath(String userEnv) throws InvalidEnvironmentException {
        if (userEnv != null && !userEnv.isEmpty()) {
            return jobConfigsPath;
        }
        throw new InvalidEnvironmentException("USER ENVIRONMENT IS INVALID");
    }

    public static void setJobConfigs(
            StreamExecutionEnvironment streamExecutionEnvironment, String jobConfigsPath)
            throws IOException {
        ParameterTool params = ParameterTool.fromPropertiesFile(jobConfigsPath);
        streamExecutionEnvironment.getConfig().setGlobalJobParameters(params);
    }

    public static JobConfig getJobConfigs(StreamExecutionEnvironment streamExecutionEnvironment) {
        Map<String, String> globalJobConfigsMap =
                streamExecutionEnvironment.getConfig().getGlobalJobParameters().toMap();
        return new JobConfig(
                globalJobConfigsMap.get("CHECKPOINTS_STORAGE_LOCATION"),
                globalJobConfigsMap.get("EVENTS_SOURCE_BROKERS"),
                globalJobConfigsMap.get("EVENTS_SOURCE_CONSUMER_GROUP_ID"),
                globalJobConfigsMap.get("EVENTS_SOURCE_TOPIC"),
                Integer.parseInt(globalJobConfigsMap.get("IDLENESS_TIME_IN_SECS")),
                Integer.parseInt(globalJobConfigsMap.get("ALLOWED_LATENESS_IN_SECS")),
                globalJobConfigsMap.get("MERCHANT_HAU_METRICS_SINK_BROKERS"),
                globalJobConfigsMap.get("MERCHANT_HAU_METRICS_SINK_TOPIC"),
                globalJobConfigsMap.get("CITY_HAU_METRICS_SINK_BROKERS"),
                globalJobConfigsMap.get("CITY_HAU_METRICS_SINK_TOPIC"));
    }
}
