package com.grofers.observability.haumetrics.datatypes.events;

import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.annotation.JsonProperty;

@JsonIgnoreProperties(ignoreUnknown = true)
public class ContextTraits {

    private final String city;
    private final Integer merchantId;

    public ContextTraits(
            @JsonProperty(value = "city_name", defaultValue = "null") String city,
            @JsonProperty(value = "merchant_id", defaultValue = "null") Integer merchantId) {
        this.city = city;
        this.merchantId = merchantId;
    }

    public String accessCity() {
        return this.city;
    }

    public Integer accessMerchantId() {
        return this.merchantId;
    }
}
