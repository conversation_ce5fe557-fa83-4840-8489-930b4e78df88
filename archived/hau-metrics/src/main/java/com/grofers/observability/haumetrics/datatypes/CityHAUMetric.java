package com.grofers.observability.haumetrics.datatypes;

public class CityHAUMetric {
    private final String city;
    private final Long windowStart;
    private final Long windowEnd;
    private final Integer uniqueDeviceCount;

    public CityHAUMetric(String city, Long windowStart, Long windowEnd, Integer uniqueDeviceCount) {
        this.city = city;
        this.windowStart = windowStart;
        this.windowEnd = windowEnd;
        this.uniqueDeviceCount = uniqueDeviceCount;
    }

    @Override
    public String toString() {
        return "{"
                + "\"city\":\""
                + city
                + '\"'
                + ", \"uniqueDeviceCount\":\""
                + uniqueDeviceCount
                + '\"'
                + ", \"windowStart\":"
                + windowStart
                + ", \"windowEnd\":"
                + windowEnd
                + "}";
    }
}
