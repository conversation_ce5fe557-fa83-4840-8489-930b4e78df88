ORDER_LIFECYCLE_EVENTS_SOURCE_BROKERS = ****************************************.groupblinkit-agent.kafka.discoveryv2.prod-y.ap-southeast-1.warpstream.com:9092
ORDER_LIFECYCLE_EVENTS_SOURCE_CONSUMER_GROUP_ID = blinkit.data.test.flink-fact-order-details
ORDER_LIFECYCLE_EVENTS_SOURCE_TOPIC = blinkit.order.lifecycle-events
ORDER_FACT_SINK_BROKERS = b-2.testdatacommon.vkkz3t.c5.kafka.ap-southeast-1.amazonaws.com:9092,b-1.testdatacommon.vkkz3t.c5.kafka.ap-southeast-1.amazonaws.com:9092
ORDER_FACT_SINK_TOPIC = stage.observability.metrics.fact-order-details
FIRST_ORDER_COMPLETED_SOURCE_TOPIC = cdp.track.backend.ordercompleted
DATA_EVENTS_SOURCE_BROKERS = b-2.prod-data-events.771kbr.c4.kafka.ap-southeast-1.amazonaws.com:9092,b-1.prod-data-events.771kbr.c4.kafka.ap-southeast-1.amazonaws.com:9092,b-3.prod-data-events.771kbr.c4.kafka.ap-southeast-1.amazonaws.com:9092
FIRST_ORDER_COMPLETED_SOURCE_CONSUMER_GROUP_ID = blinkit.data.test.flink-fact-order-details