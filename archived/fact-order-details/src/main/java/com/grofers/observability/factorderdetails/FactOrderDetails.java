/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.grofers.observability.factorderdetails;

import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.table.annotation.DataTypeHint;
import org.apache.flink.table.api.EnvironmentSettings;
import org.apache.flink.table.api.TableEnvironment;
import org.apache.flink.table.functions.ScalarFunction;
import org.apache.flink.types.Row;

import com.grofers.observability.factorderdetails.configs.JobConfigManager;

import java.io.IOException;

import static com.grofers.observability.factorderdetails.Queries.*;
import static com.grofers.observability.factorderdetails.configs.JobConfig.*;

/**
 * Order Metrics Job
 *
 * <p>For a tutorial how to write a Flink streaming application, check the tutorials and examples on
 * the <a href="https://flink.apache.org/docs/stable/">Flink Website</a>.
 *
 * <p>To package your application into a JAR file for execution, run 'mvn clean package' on the
 * command line.
 *
 * <p>If you change the name of the main class (with the public static void main(String[] args))
 * method, change the respective entry in the POM.xml file (simply search for 'mainClass').
 */
public class FactOrderDetails {
    public static class SumOfItemQuantityFunction extends ScalarFunction {
        public Integer eval(
                @DataTypeHint("ARRAY<ROW<`product_id` INT,`quantity` INT>>") Row[] arr,
                String key) {
            if (arr != null) {
                int sum = 0;
                for (Row i : arr) {
                    sum += (int) i.getFieldAs(key);
                }
                return sum;
            }
            return null;
        }
    }

    public static class GetTypeOfOrderFunction extends ScalarFunction {
        public String eval(
                @DataTypeHint("ARRAY< ROW <`id` INT, `type` VARCHAR(63) >>") Row[] arr,
                Integer key) {
            if (arr != null) {
                for (Row i : arr) {
                    if ((int) i.getFieldAs("id") == key) {
                        return i.getFieldAs("type").toString();
                    }
                }
            }
            return null;
        }
    }

    public static void main(String[] args)
            throws JobConfigManager.InvalidEnvironmentException, IOException {
        EnvironmentSettings settings = EnvironmentSettings.newInstance().inStreamingMode().build();

        TableEnvironment tEnv = TableEnvironment.create(settings);
        tEnv.createTemporarySystemFunction(
                "SumOfItemQuantityFunction", SumOfItemQuantityFunction.class);
        tEnv.createTemporarySystemFunction("GetTypeOfOrderFunction", GetTypeOfOrderFunction.class);
        Configuration configuration = tEnv.getConfig().getConfiguration();
        configuration.setString("table.exec.source.idle-timeout", "1200 s");
        configuration.setString("table.exec.state.ttl", "86400 s");

        // local-global aggregation depends on mini-batch is enabled
        configuration.setString("table.exec.mini-batch.enabled", "true");
        configuration.setString("table.exec.mini-batch.allow-latency", "1 s");
        configuration.setString("table.exec.mini-batch.size", "500");

        configuration.setString("table.optimizer.agg-phase-strategy", "TWO_PHASE");
        configuration.setString("table.optimizer.distinct-agg.split.enabled", "true");

        ParameterTool params = ParameterTool.fromArgs(args);
        String userEnv = params.getRequired("environment");

        // Get job configs
        JobConfigManager.setJobConfigsPath(userEnv);
        String jobPropertiesPath = JobConfigManager.getJobConfigsPath(userEnv);
        JobConfigManager.setJobConfigs(tEnv, jobPropertiesPath);
        JobConfigManager.getJobConfigs(tEnv);

        String omsOrderCreateTable =
                String.format(
                        ORDER_LIFECYCLE_EVENTS,
                        ORDER_LIFECYCLE_EVENTS_SOURCE_TOPIC,
                        ORDER_LIFECYCLE_EVENTS_SOURCE_BROKERS,
                        ORDER_LIFECYCLE_EVENTS_SOURCE_CONSUMER_GROUP_ID);
        tEnv.executeSql(omsOrderCreateTable);

        String omsFirstOrderCreateTable =
                String.format(
                        OMS_FIRST_ORDER_COMPLETED_CREATE_SQL,
                        FIRST_ORDER_COMPLETED_SOURCE_TOPIC,
                        DATA_EVENTS_SOURCE_BROKERS,
                        FIRST_ORDER_COMPLETED_SOURCE_CONSUMER_GROUP_ID);
        tEnv.executeSql(omsFirstOrderCreateTable);

        String orderMetricsCreateTable =
                String.format(
                        ORDER_FACT_CREATE_TABLE, ORDER_FACT_SINK_TOPIC, ORDER_FACT_SINK_BROKERS);
        tEnv.executeSql(orderMetricsCreateTable);

        tEnv.executeSql(ORDER_FACT_METRIC);
    }
}
