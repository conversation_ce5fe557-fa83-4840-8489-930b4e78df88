package com.grofers.observability.factorderdetails;

public class Queries {
    public static final String ORDER_LIFECYCLE_EVENTS =
            "CREATE TABLE IF NOT EXISTS order_lifecycle_events ( order_id INT,  `order`"
                + " ROW(datetime_created BIGINT, datetime_updated BIGINT, current_state_name"
                + " VARCHAR(50), actual_merchant_id INT, cart_id BIGINT, total_cost DOUBLE,"
                + " delivery_cost DOUBLE, discount_value DOUBLE, net_cost DOUBLE,"
                + " procurement_amount DOUBLE, wallet_amount DOUBLE, items ARRAY<ROW (`product_id`"
                + " INT, `quantity` INT ) >, slot_properties ROW(`slot_charge` DOUBLE,"
                + " `checkout_properties` ROW(`slot_charge` DOUBLE)), additional_charges_amount"
                + " DOUBLE, `city` STRING, checkout_merchant_name STRING, merchant ROW(`city_id`"
                + " INT), customer ROW( `id` INT), cart ROW(`orders` ARRAY< ROW (`id` INT, `type`"
                + " VARCHAR(63) )>), org_channel_id VARCHAR(10)), reason_code"
                + " VARCHAR(50),flink_event_time_epoch AS"
                + " TO_TIMESTAMP_LTZ(`order`.datetime_updated, 0),WATERMARK FOR"
                + " flink_event_time_epoch AS flink_event_time_epoch - INTERVAL '60' seconds) WITH"
                + " (  'connector' = 'kafka', 'format' = 'json', 'topic' = '%s',"
                + " 'properties.bootstrap.servers' = '%s', 'properties.group.id' = '%s',"
                + " 'scan.startup.mode' = 'earliest-offset'   )";

    public static final String OMS_FIRST_ORDER_COMPLETED_CREATE_SQL =
            "CREATE TABLE IF NOT EXISTS oms_first_order_completed_details ( order_id INT,"
                + " is_first_order_completed BOOLEAN, is_first_order_delivered BOOLEAN,"
                + " event_time_epoch BIGINT, flink_event_time_epoch AS"
                + " TO_TIMESTAMP_LTZ(event_time_epoch, 0), WATERMARK FOR flink_event_time_epoch AS"
                + " flink_event_time_epoch - INTERVAL '60' seconds ) WITH ( 'connector' = 'kafka',"
                + " 'format' = 'json', 'topic' = '%s',  'properties.bootstrap.servers' = '%s',"
                + " 'properties.group.id' = '%s',  'scan.startup.mode' = 'earliest-offset'  )";

    public static final String ORDER_FACT_METRIC =
            "INSERT INTO fact_order_details SELECT o.order_id, o.`order`.datetime_created*1000 as"
                + " insert_timestamp_epoch,      o.`order`.datetime_updated*1000  as"
                + " update_timestamp_epoch,        o.`order`.`customer`.id,       "
                + " o.`order`.current_state_name,        o.`order`.total_cost,       "
                + " o.`order`.delivery_cost,        o.`order`.discount_value,       "
                + " o.`order`.net_cost,        o.`order`.procurement_amount,       "
                + " GetTypeOfOrderFunction(o.`order`.cart.orders, o.order_id) as type,"
                + " o.`order`.cart_id,        o.`order`.actual_merchant_id as merchant_id,       "
                + " o.`order`.wallet_amount,        o.`order`.additional_charges_amount,       "
                + " o.`order`.merchant.city_id as city_id,        o.`order`.city as city_name,"
                + " o.`order`.checkout_merchant_name as merchant_name,"
                + " CARDINALITY(o.`order`.items) as item_count,"
                + " SumOfItemQuantityFunction(o.`order`.items, 'quantity') as"
                + " total_items_quantity,  o.`order`.slot_properties.slot_charge as"
                + " slot_charge,o.`order`.slot_properties.checkout_properties.slot_charge as"
                + " checkout_slot_charge, ofo.is_first_order_completed,"
                + " ofo.is_first_order_delivered, o.`order`.org_channel_id, o.reason_code FROM"
                + " order_lifecycle_events as o LEFT JOIN oms_first_order_completed_details as ofo"
                + " on o.order_id = ofo.order_id WHERE"
                + " (GetTypeOfOrderFunction(o.`order`.cart.orders, o.order_id) is null or"
                + " GetTypeOfOrderFunction(o.`order`.cart.orders, o.order_id) in"
                + " ('RetailForwardOrder')) and o.`order`.city not in ('Not in service"
                + " area','Hapur','test1207898732') and o.`order`.city not like '%B2B%'";

    public static final String ORDER_FACT_CREATE_TABLE =
            "CREATE TABLE IF NOT EXISTS fact_order_details ( "
                    + "    id INT PRIMARY KEY, "
                    + "    insert_timestamp BIGINT, "
                    + "    update_timestamp BIGINT, "
                    + "    customer_id INT, "
                    + "    current_status VARCHAR(50), "
                    + "    total_cost DOUBLE, "
                    + "    delivery_cost DOUBLE, "
                    + "    discount DOUBLE, "
                    + "    net_cost DOUBLE, "
                    + "    procurement_amount DOUBLE, "
                    + "    type VARCHAR(63), "
                    + "    cart_id BIGINT, "
                    + "    merchant_id BIGINT, "
                    + "    wallet_amount DOUBLE, "
                    + "    additional_charges_amount DOUBLE, "
                    + "    city_id INT, "
                    + "    city_name STRING, "
                    + "    merchant_name STRING, "
                    + "    item_count BIGINT, "
                    + "    total_items_quantity BIGINT, "
                    + "    slot_charge DOUBLE, "
                    + "    checkout_slot_charge DOUBLE, "
                    + "    is_first_order_completed BOOLEAN, "
                    + "    is_first_order_delivered BOOLEAN, "
                    + "    org_channel_id VARCHAR(10), "
                    + "    reason_code VARCHAR(50) "
                    + ") WITH ( "
                    + "   'connector' = 'upsert-kafka', "
                    + "   'topic' = '%s', "
                    + "   'properties.bootstrap.servers' = '%s', "
                    + "   'key.format' = 'json', "
                    + "   'value.format' = 'json' "
                    + ")";
}
