package com.grofers.observability.factorderdetails.configs;

public class JobConfig {
    public static String ORDER_LIFECYCLE_EVENTS_SOURCE_BROKERS;
    public static String ORDER_LIFECYCLE_EVENTS_SOURCE_CONSUMER_GROUP_ID;
    public static String ORDER_LIFECYCLE_EVENTS_SOURCE_TOPIC;
    public static String ORDER_FACT_SINK_BROKERS;
    public static String ORDER_FACT_SINK_TOPIC;
    public static String FIRST_ORDER_COMPLETED_SOURCE_TOPIC;
    public static String DATA_EVENTS_SOURCE_BROKERS;
    public static String FIRST_ORDER_COMPLETED_SOURCE_CONSUMER_GROUP_ID;

    public JobConfig(
            String ORDER_LIFECYCLE_EVENTS_SOURCE_BROKERS,
            String ORDER_LIFECYCLE_EVENTS_SOURCE_CONSUMER_GROUP_ID,
            String ORDER_LIFECYCLE_EVENTS_SOURCE_TOPIC,
            String ORDER_FACT_SINK_BROKERS,
            String ORDER_FACT_SINK_TOPIC,
            String FIRST_ORDER_COMPLETED_SOURCE_TOPIC,
            String DATA_EVENTS_SOURCE_BROKERS,
            String FIRST_ORDER_COMPLETED_SOURCE_CONSUMER_GROUP_ID) {
        JobConfig.ORDER_LIFECYCLE_EVENTS_SOURCE_BROKERS = ORDER_LIFECYCLE_EVENTS_SOURCE_BROKERS;
        JobConfig.ORDER_LIFECYCLE_EVENTS_SOURCE_CONSUMER_GROUP_ID =
                ORDER_LIFECYCLE_EVENTS_SOURCE_CONSUMER_GROUP_ID;
        JobConfig.ORDER_LIFECYCLE_EVENTS_SOURCE_TOPIC = ORDER_LIFECYCLE_EVENTS_SOURCE_TOPIC;
        JobConfig.ORDER_FACT_SINK_BROKERS = ORDER_FACT_SINK_BROKERS;
        JobConfig.ORDER_FACT_SINK_TOPIC = ORDER_FACT_SINK_TOPIC;
        JobConfig.FIRST_ORDER_COMPLETED_SOURCE_TOPIC = FIRST_ORDER_COMPLETED_SOURCE_TOPIC;
        JobConfig.DATA_EVENTS_SOURCE_BROKERS = DATA_EVENTS_SOURCE_BROKERS;
        JobConfig.FIRST_ORDER_COMPLETED_SOURCE_CONSUMER_GROUP_ID =
                FIRST_ORDER_COMPLETED_SOURCE_CONSUMER_GROUP_ID;
    }
}
