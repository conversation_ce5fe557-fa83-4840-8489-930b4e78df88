name: Supply Order Metrics
image: 442534439095.dkr.ecr.ap-southeast-1.amazonaws.com/data/flink-streams/observability/supply-order-metrics
flink_config:
  taskmanager.memory.process.size: 24576m
  jobmanager.memory.process.size: 2048m
  taskmanager.memory.managed.size: 0
  taskmanager.numberOfTaskSlots: 2
  parallelism.default: 4
  process.working-dir: supply-order-metrics/process
  state.backend.local-recovery: true
  taskmanager.resource-id: TaskManager_SupplyOrderMetrics
deployment_config:
  kubernetes.rest-service.exposed.type: NodePort
  metrics.reporters: prom
  metrics.reporter.prom.class: org.apache.flink.metrics.prometheus.PrometheusReporter
allow_non_restored_state: False # Set to True when changes are made to Operator
secrets:
  master:
    LOGISTICS_HOSTNAME: "dse/postgres/logistics_server/logistics_server_reader_redash:host"
    LOGISTICS_PORT: "dse/postgres/logistics_server/logistics_server_reader_redash:port"
    LOGISTICS_DB: "dse/postgres/logistics_server/logistics_server_reader_redash:dbname"
    LOGISTICS_DB_USER: "dse/postgres/logistics_server/logistics_server_reader_redash:user"
    LOGISTICS_DB_PASSWORD: "dse/postgres/logistics_server/logistics_server_reader_redash:password"
codeartifact_access: True
