ORDER_LIFECYCLE_EVENTS_SOURCE_BROKERS = ****************************************.groupblinkit-agent.kafka.discoveryv2.prod-y.ap-southeast-1.warpstream.com:9092
ORDER_LIFECYCLE_EVENTS_SOURCE_CONSUMER_GROUP_ID = blinkit.data.test.flink-supply-order-metrics
ORDER_LIFECYCLE_EVENTS_SOURCE_TOPIC = blinkit.storeops.order-lifecycle
LOGISTICS_EVENTS_SOURCE_BROKERS = b-1.proddatasrp.wv3mhc.c5.kafka.ap-southeast-1.amazonaws.com:9092,b-2.proddatasrp.wv3mhc.c5.kafka.ap-southeast-1.amazonaws.com:9092,b-3.proddatasrp.wv3mhc.c5.kafka.ap-southeast-1.amazonaws.com:9092
DARK_STORE_PROJECTIONS_TOPIC = postgres.logistics_server.public.logistics_dark_store_projection
DARK_STORE_PROJECTIONS_CONSUMER_GROUP = test-supply-order-metrics-consumer-group-logistics_dark_store_projection
LOGISTICS_EVENTS_SCHEMA_REGISTRY = http://source-replication-common-schema-registry-external-data.prod-sgp-k8s.grofer.io:8081
ORDER_METRICS_TOPIC = stage.observability.metrics.supply-order-metrics
ORDER_METRICS_BROKERS = b-2.testdatacommon.vkkz3t.c5.kafka.ap-southeast-1.amazonaws.com:9092,b-1.testdatacommon.vkkz3t.c5.kafka.ap-southeast-1.amazonaws.com:9092
STATE_BACKEND_LOCATION = s3a://grofers-test-dse-singapore/flink-streams/observability/supply-order-metrics/checkpoints/
