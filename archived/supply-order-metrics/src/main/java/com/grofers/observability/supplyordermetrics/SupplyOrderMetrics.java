/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.grofers.observability.supplyordermetrics;

import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.contrib.streaming.state.EmbeddedRocksDBStateBackend;
import org.apache.flink.streaming.api.CheckpointingMode;
import org.apache.flink.streaming.api.environment.CheckpointConfig;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.table.api.bridge.java.StreamTableEnvironment;

import com.grofers.gandalf.core.udfs.IsoDateTimeStringToTimestamp3;
import com.grofers.observability.supplyordermetrics.configs.JobConfigManager;

import java.io.IOException;

import static com.grofers.observability.supplyordermetrics.Queries.*;
import static com.grofers.observability.supplyordermetrics.configs.JobConfig.*;

/**
 * Order Metrics Job
 *
 * <p>For a tutorial how to write a Flink streaming application, check the tutorials and examples on
 * the <a href="https://flink.apache.org/docs/stable/">Flink Website</a>.
 *
 * <p>To package your application into a JAR file for execution, run 'mvn clean package' on the
 * command line.
 *
 * <p>If you change the name of the main class (with the public static void main(String[] args))
 * method, change the respective entry in the POM.xml file (simply search for 'mainClass').
 */
public class SupplyOrderMetrics {

    public static void main(String[] args)
            throws JobConfigManager.InvalidEnvironmentException, IOException {
        final StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment();
        final StreamTableEnvironment tEnv = StreamTableEnvironment.create(env);
        tEnv.createTemporarySystemFunction(
                "stringToTimestamp", IsoDateTimeStringToTimestamp3.class);
        Configuration configuration = tEnv.getConfig().getConfiguration();
        configuration.setString("table.exec.source.idle-timeout", "1200 s");
        configuration.setString("table.exec.state.ttl", "9000 s");

        // local-global aggregation depends on mini-batch is enabled
        configuration.setString("table.exec.mini-batch.enabled", "true");
        configuration.setString("table.exec.mini-batch.allow-latency", "1 s");
        configuration.setString("table.exec.mini-batch.size", "500");

        configuration.setString("table.optimizer.agg-phase-strategy", "TWO_PHASE");
        configuration.setString("table.optimizer.distinct-agg.split.enabled", "true");

        ParameterTool params = ParameterTool.fromArgs(args);
        String userEnv = params.getRequired("environment");

        // Get job configs
        JobConfigManager.setJobConfigsPath(userEnv);
        String jobPropertiesPath = JobConfigManager.getJobConfigsPath(userEnv);
        JobConfigManager.setJobConfigs(tEnv, jobPropertiesPath);
        JobConfigManager.getJobConfigs(tEnv);

        // Checkpoint Configs
        env.enableCheckpointing(600000);
        // set mode to exactly-once (default)
        env.getCheckpointConfig().setCheckpointingMode(CheckpointingMode.EXACTLY_ONCE);
        // make sure 900000 ms of progress happen between checkpoints
        env.getCheckpointConfig().setMinPauseBetweenCheckpoints(300000);
        // checkpoints have to complete within this minute, or are discarded
        env.getCheckpointConfig().setCheckpointTimeout(180000);
        // only two consecutive checkpoint failures are tolerated
        env.getCheckpointConfig().setTolerableCheckpointFailureNumber(3);
        // allow only one checkpoint to be in progress at the same time
        env.getCheckpointConfig().setMaxConcurrentCheckpoints(1);
        // enable externalized checkpoints which are retained after job cancellation
        env.getCheckpointConfig()
                .setExternalizedCheckpointCleanup(
                        CheckpointConfig.ExternalizedCheckpointCleanup.RETAIN_ON_CANCELLATION);
        // enable unaligned checkpoints
        env.getCheckpointConfig().enableUnalignedCheckpoints();
        // sets a RocksDB checkpoint storage where checkpoint snapshots will be written
        env.setStateBackend(new EmbeddedRocksDBStateBackend(true));
        env.getCheckpointConfig().setCheckpointStorage(STATE_BACKEND_LOCATION);

        String darkStoreProjectionCreateTable =
                String.format(
                        DARK_STORE_PROJECTIONS,
                        DARK_STORE_PROJECTIONS_TOPIC,
                        LOGISTICS_EVENTS_SOURCE_BROKERS,
                        DARK_STORE_PROJECTIONS_CONSUMER_GROUP,
                        LOGISTICS_EVENTS_SCHEMA_REGISTRY);
        tEnv.executeSql(darkStoreProjectionCreateTable);

        String supplyOrderLifecycle =
                String.format(
                        STOREOPS_ORDER_LIFECYCLE,
                        ORDER_LIFECYCLE_EVENTS_SOURCE_TOPIC,
                        ORDER_LIFECYCLE_EVENTS_SOURCE_BROKERS,
                        ORDER_LIFECYCLE_EVENTS_SOURCE_CONSUMER_GROUP_ID);
        tEnv.executeSql(supplyOrderLifecycle);

        String supplyOrderMetrics =
                String.format(SUPPLY_ORDER_METRICS, ORDER_METRICS_TOPIC, ORDER_METRICS_BROKERS);
        tEnv.executeSql(supplyOrderMetrics);

        String insertSupplyOrderMetrics =
                "INSERT INTO supply_order_metrics SELECT ole.order_id, ldp.delivery_fe_id as"
                    + " delivery_fe_id, ldp.fill_rate, (case when ldp.current_state not in"
                    + " ('CREATED','ALLOCATION_PENDING', 'ALLOCATION_COMPLETE') then"
                    + " ldp.current_state else ole.current_state end ) as current_state, "
                    + " UNIX_TIMESTAMP(DATE_FORMAT(stringToTimestamp(ldp.install_ts), 'yyyy-MM-dd"
                    + " HH:mm:ss')) * 1000 AS logistics_install_ts,"
                    + " UNIX_TIMESTAMP(DATE_FORMAT(stringToTimestamp(ldp.billing_timestamp),"
                    + " 'yyyy-MM-dd HH:mm:ss')) * 1000 AS billing_timestamp,"
                    + " UNIX_TIMESTAMP(DATE_FORMAT(stringToTimestamp(ldp.current_delivery_start),"
                    + " 'yyyy-MM-dd HH:mm:ss')) * 1000 AS current_delivery_start,"
                    + " UNIX_TIMESTAMP(DATE_FORMAT(stringToTimestamp(ldp.current_delivery_end),"
                    + " 'yyyy-MM-dd HH:mm:ss')) * 1000 AS current_delivery_end,"
                    + " UNIX_TIMESTAMP(DATE_FORMAT(stringToTimestamp(ldp.delivery_timestamp),"
                    + " 'yyyy-MM-dd HH:mm:ss')) * 1000 AS delivery_timestamp,"
                    + " UNIX_TIMESTAMP(DATE_FORMAT(stringToTimestamp(ldp.trip_start_timestamp),"
                    + " 'yyyy-MM-dd HH:mm:ss')) * 1000 AS trip_start_timestamp, ole.assign_time,"
                    + " ole.auditor_employee_id, ole.auditor_name, ole.created_at, (case when"
                    + " ldp.update_ts_epoch is not NULL and ldp.update_ts_epoch > ole.updated_at"
                    + " then ldp.update_ts_epoch else ole.updated_at end) as updated_at,"
                    + " ole.drop_zone, ole.order_type, ole.picker_id as picker_employee_id,"
                    + " ole.picker_name, ole.picking_start_time, ole.pick_completion_time,"
                    + " ole.putter_employee_id, ole.putter_name, ole.site_id as store_id,"
                    + " ole.sub_order_id, ole.audit_reason, ole.customer_return_type FROM"
                    + " order_lifecycle_events ole LEFT JOIN logistics_dark_store_projection AS"
                    + " ldp ON ole.order_id = ldp.order_id;";

        tEnv.executeSql(insertSupplyOrderMetrics);
    }
}
