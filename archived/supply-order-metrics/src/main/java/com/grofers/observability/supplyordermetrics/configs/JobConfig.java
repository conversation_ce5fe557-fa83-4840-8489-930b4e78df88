package com.grofers.observability.supplyordermetrics.configs;

public class JobConfig {
    public static String ORDER_LIFECYCLE_EVENTS_SOURCE_BROKERS;
    public static String ORDER_LIFECYCLE_EVENTS_SOURCE_CONSUMER_GROUP_ID;
    public static String ORDER_LIFECYCLE_EVENTS_SOURCE_TOPIC;
    public static String LOGISTICS_EVENTS_SOURCE_BROKERS;
    public static String DARK_STORE_PROJECTIONS_TOPIC;
    public static String DARK_STORE_PROJECTIONS_CONSUMER_GROUP;
    public static String LOGISTICS_EVENTS_SCHEMA_REGISTRY;
    public static String ORDER_METRICS_TOPIC;
    public static String ORDER_METRICS_BROKERS;
    public static String STATE_BACKEND_LOCATION;

    public JobConfig(
            String ORDER_LIFECYCLE_EVENTS_SOURCE_BROKERS,
            String ORDER_LIFECYCLE_EVENTS_SOURCE_CONSUMER_GROUP_ID,
            String ORDER_LIFECYCLE_EVENTS_SOURCE_TOPIC,
            String LOGISTICS_EVENTS_SOURCE_BROKERS,
            String DARK_STORE_PROJECTIONS_TOPIC,
            String LOGISTICS_EVENTS_SCHEMA_REGISTRY,
            String DARK_STORE_PROJECTIONS_CONSUMER_GROUP,
            String ORDER_METRICS_TOPIC,
            String ORDER_METRICS_BROKERS,
            String STATE_BACKEND_LOCATION) {
        JobConfig.ORDER_LIFECYCLE_EVENTS_SOURCE_BROKERS = ORDER_LIFECYCLE_EVENTS_SOURCE_BROKERS;
        JobConfig.ORDER_LIFECYCLE_EVENTS_SOURCE_CONSUMER_GROUP_ID =
                ORDER_LIFECYCLE_EVENTS_SOURCE_CONSUMER_GROUP_ID;
        JobConfig.ORDER_LIFECYCLE_EVENTS_SOURCE_TOPIC = ORDER_LIFECYCLE_EVENTS_SOURCE_TOPIC;
        JobConfig.LOGISTICS_EVENTS_SOURCE_BROKERS = LOGISTICS_EVENTS_SOURCE_BROKERS;
        JobConfig.DARK_STORE_PROJECTIONS_TOPIC = DARK_STORE_PROJECTIONS_TOPIC;
        JobConfig.DARK_STORE_PROJECTIONS_CONSUMER_GROUP = DARK_STORE_PROJECTIONS_CONSUMER_GROUP;
        JobConfig.LOGISTICS_EVENTS_SCHEMA_REGISTRY = LOGISTICS_EVENTS_SCHEMA_REGISTRY;
        JobConfig.ORDER_METRICS_TOPIC = ORDER_METRICS_TOPIC;
        JobConfig.ORDER_METRICS_BROKERS = ORDER_METRICS_BROKERS;
        JobConfig.STATE_BACKEND_LOCATION = STATE_BACKEND_LOCATION;
    }
}
