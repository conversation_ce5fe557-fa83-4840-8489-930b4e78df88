package com.grofers.corestreams.merchantdemand.serdes;

import org.apache.flink.api.common.serialization.SerializationSchema;
import org.apache.flink.streaming.connectors.kafka.KafkaSerializationSchema;

import com.grofers.corestreams.merchantdemand.datatypes.MerchantDemandConversion;
import org.apache.kafka.clients.producer.ProducerRecord;

import javax.annotation.Nullable;

import java.nio.charset.StandardCharsets;

public class MerchantDemandConversionSerializationSchema
        implements KafkaSerializationSchema<MerchantDemandConversion> {

    private static final long serialVersionUID = -1L;
    private final String topic;

    public MerchantDemandConversionSerializationSchema(String topic) {
        this.topic = topic;
    }

    @Override
    public void open(SerializationSchema.InitializationContext context) throws Exception {
        KafkaSerializationSchema.super.open(context);
    }

    @Override
    public ProducerRecord<byte[], byte[]> serialize(
            MerchantDemandConversion merchantDemandSurge, @Nullable Long aLong) {
        return new ProducerRecord<>(
                topic,
                merchantDemandSurge.getMerchantId().toString().getBytes(StandardCharsets.UTF_8),
                merchantDemandSurge.toString().getBytes(StandardCharsets.UTF_8));
    }
}
