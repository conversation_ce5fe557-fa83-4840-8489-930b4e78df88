/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.grofers.corestreams.merchantdemand;

import org.apache.flink.api.common.ExecutionConfig;
import org.apache.flink.api.common.JobExecutionResult;
import org.apache.flink.api.common.eventtime.WatermarkStrategy;
import org.apache.flink.api.common.functions.MapFunction;
import org.apache.flink.api.common.state.StateTtlConfig;
import org.apache.flink.api.common.state.ValueState;
import org.apache.flink.api.common.state.ValueStateDescriptor;
import org.apache.flink.api.java.functions.KeySelector;
import org.apache.flink.api.java.io.TextInputFormat;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.connector.kafka.source.KafkaSource;
import org.apache.flink.connector.kafka.source.enumerator.initializer.OffsetsInitializer;
import org.apache.flink.core.fs.Path;
import org.apache.flink.streaming.api.CheckpointingMode;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.datastream.DataStreamSource;
import org.apache.flink.streaming.api.datastream.KeyedStream;
import org.apache.flink.streaming.api.environment.CheckpointConfig;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.streaming.api.functions.co.RichCoFlatMapFunction;
import org.apache.flink.streaming.api.functions.sink.SinkFunction;
import org.apache.flink.streaming.api.functions.source.FileProcessingMode;
import org.apache.flink.streaming.api.functions.windowing.ProcessWindowFunction;
import org.apache.flink.streaming.api.windowing.assigners.SlidingEventTimeWindows;
import org.apache.flink.streaming.api.windowing.time.Time;
import org.apache.flink.streaming.api.windowing.windows.TimeWindow;
import org.apache.flink.streaming.connectors.kafka.FlinkKafkaProducer;
import org.apache.flink.util.Collector;

import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.node.ObjectNode;

import com.grofers.corestreams.merchantdemand.configs.JobConfigs;
import com.grofers.corestreams.merchantdemand.configs.JobConfigsManager;
import com.grofers.corestreams.merchantdemand.datatypes.DeviceMerchantMap;
import com.grofers.corestreams.merchantdemand.datatypes.MerchantConversion;
import com.grofers.corestreams.merchantdemand.datatypes.MerchantDemandConversion;
import com.grofers.corestreams.merchantdemand.datatypes.MerchantOnlineDevices;
import com.grofers.corestreams.merchantdemand.serdes.JSONValueDeserializationSchema;
import com.grofers.corestreams.merchantdemand.serdes.MerchantDemandConversionSerializationSchema;

import java.time.*;
import java.util.*;

import static com.grofers.corestreams.merchantdemand.configs.JobConfigs.*;

/**
 * Flink Streaming Job for merchant demand.
 *
 * <p>For more information around use case refer <a
 * href="https://grofers.atlassian.net/wiki/spaces/DATA/pages/3341025301/1+-+Serviceability+-+Surge+Management">Inventory
 * Doc</a>.
 *
 * <p>To package your application into a JAR file for execution, run 'mvn clean package' on the
 * command line.
 *
 * <p>If you change the name of the job (with the public static void main(String[] args)) method,
 * change the respective entry in the POM.xml file (simply search for 'mainClass').
 */
public class MerchantDemand {
    private final DataStreamSource<String> merchantConversionsSource;
    private final DataStreamSource<ObjectNode> impressionEvents;
    private final SinkFunction<MerchantDemandConversion> merchantDemandConversionsSink;

    public MerchantDemand(
            DataStreamSource<String> merchantConversionsSource,
            DataStreamSource<ObjectNode> impressionEventsSource,
            SinkFunction<MerchantDemandConversion> merchantDemandConversionsSink) {
        this.merchantConversionsSource = merchantConversionsSource;
        this.impressionEvents = impressionEventsSource;
        this.merchantDemandConversionsSink = merchantDemandConversionsSink;
    }

    /**
     * Creates a windowed merchant demand and generate enriched output from conversions
     *
     * @param env StreamExecutionEnvironment
     * @return {JobExecutionResult}
     * @throws Exception which occurs during job execution.
     */
    public JobExecutionResult execute(StreamExecutionEnvironment env) throws Exception {

        WatermarkStrategy<MerchantConversion> conversionsWatermarkStrategy =
                WatermarkStrategy.<MerchantConversion>forBoundedOutOfOrderness(
                                Duration.ofSeconds(10))
                        .withIdleness(Duration.ofSeconds(IDLENESS_TIME_IN_SECS))
                        .withTimestampAssigner(
                                (event, timestamp) -> event.getEventTimestamp() * 1000);

        KeyedStream<MerchantConversion, Long> merchantConversionStream =
                this.merchantConversionsSource
                        .name("merchant-conversion-file-source")
                        .uid("merchant-conversion-file-source")
                        .filter(
                                line ->
                                        !line.contains(
                                                "merchant_id,hourly_dau,hourly_checkouts,hourly_conversion"))
                        .name("mechant-conversion-source-header-filter")
                        .uid("mechant-conversion-source-header-filter")
                        .map(
                                (line) -> {
                                    String[] cells = line.split(",");
                                    return new MerchantConversion(
                                            Long.parseLong(cells[0]),
                                            Integer.parseInt(cells[1]),
                                            Integer.parseInt(cells[2]),
                                            Float.parseFloat(cells[3]),
                                            Long.parseLong(cells[4]));
                                })
                        .name("merchant-conversion-source-map")
                        .uid("merchant-conversion-source-map")
                        .assignTimestampsAndWatermarks(conversionsWatermarkStrategy)
                        .keyBy(
                                (KeySelector<MerchantConversion, Long>)
                                        MerchantConversion::getMerchantId);

        DataStream<MerchantOnlineDevices> merchantDemandStream =
                this.impressionEvents
                        .name("impressions-event-source")
                        .uid("impressions-event-source")
                        .filter(
                                t ->
                                        (t.get("value")
                                                        .get("context")
                                                        .get("traits")
                                                        .has("merchant_id")
                                                &&
                                                // Adding filter for forward events which might come
                                                // in due to device settings
                                                Instant.parse(
                                                                        t.get("value")
                                                                                .get("timestamp")
                                                                                .asText())
                                                                .toEpochMilli()
                                                        < Instant.now().toEpochMilli()))
                        .uid("merchant-id-non-null-filter")
                        .name("merchant-id-non-null-filter")
                        .map(
                                (MapFunction<ObjectNode, DeviceMerchantMap>)
                                        jsonNodes ->
                                                new DeviceMerchantMap(
                                                        jsonNodes
                                                                .get("value")
                                                                .get("context")
                                                                .get("traits")
                                                                .get("merchant_id")
                                                                .asLong(),
                                                        jsonNodes
                                                                .get("value")
                                                                .get("context")
                                                                .get("device")
                                                                .get("id")
                                                                .asText()))
                        .uid("merchant-demand-map")
                        .name("merchant-demand-map")
                        .keyBy(DeviceMerchantMap::getMerchantId)
                        .window(
                                SlidingEventTimeWindows.of(
                                        Time.minutes(SLIDING_WINDOW_SIZE_IN_MINS),
                                        Time.seconds(SLIDING_WINDOW_SLIDE_IN_SECS)))
                        .process(new ProcessImpressionEventsFunction())
                        .name("merchant-demand-sliding-window")
                        .uid("merchant-demand-sliding-window")
                        .keyBy(MerchantOnlineDevices::getMerchantId);

        DataStream<MerchantDemandConversion> merchantDemandConversion =
                merchantDemandStream
                        .connect(merchantConversionStream)
                        .flatMap(new ConversionEnrichmentFunction())
                        .name("merchant-demand-conversion-enrichment")
                        .uid("merchant-demand-conversion-enrichment");

        merchantDemandConversion
                .addSink(this.merchantDemandConversionsSink)
                .name("merchant-demand-conversions-kafka-sink")
                .uid("merchant-demand-conversions-kafka-sink");

        // execute program
        return env.execute("serviceability.merchant-demand");
    }

    public static void main(String[] args) throws Exception {
        // set up the streaming execution environment
        final StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment();

        // Parameters for environ
        ParameterTool params = ParameterTool.fromArgs(args);
        String userEnv = params.getRequired("environment");

        // Set PropertiesPath for userEnv
        JobConfigsManager.setJobConfigsPath(userEnv);
        // Get PropertiesPath for userEnv
        String jobPropertiesPath = JobConfigsManager.getJobConfigsPath(userEnv);
        // Get Global Job Properties
        ParameterTool flinkGlobalProperties =
                JobConfigsManager.setGlobalJobConfigs(env, jobPropertiesPath);

        // Create a JobConfigs object to access globally set flink properties
        JobConfigs jobConfigs = new JobConfigs(flinkGlobalProperties);

        // Checkpoint Configs
        // start a checkpoint every 300000 ms
        env.enableCheckpointing(300000);
        // set mode to exactly-once (default)
        env.getCheckpointConfig().setCheckpointingMode(CheckpointingMode.EXACTLY_ONCE);
        // make sure 300000 ms of progress happen between checkpoints
        env.getCheckpointConfig().setMinPauseBetweenCheckpoints(300000);
        // checkpoints have to complete within two minutes, or are discarded
        env.getCheckpointConfig().setCheckpointTimeout(120000);
        // only two consecutive checkpoint failures are tolerated
        env.getCheckpointConfig().setTolerableCheckpointFailureNumber(2);
        // allow only one checkpoint to be in progress at the same time
        env.getCheckpointConfig().setMaxConcurrentCheckpoints(1);
        // enable externalized checkpoints which are retained after job cancellation
        env.getCheckpointConfig()
                .enableExternalizedCheckpoints(
                        CheckpointConfig.ExternalizedCheckpointCleanup.RETAIN_ON_CANCELLATION);
        // enables the experimental unaligned checkpoints
        env.getCheckpointConfig().enableUnalignedCheckpoints();
        // sets the checkpoint storage where checkpoint snapshots will be written
        env.getCheckpointConfig().setCheckpointStorage(CHECKPOINTS_STORAGE_LOCATION);

        ExecutionConfig executionConfig = new ExecutionConfig();
        executionConfig.setUseSnapshotCompression(true);

        TextInputFormat merchantConversionInputFormat =
                new TextInputFormat(new Path(MERCHANT_DEMAND_CONVERSION_FILE_SOURCE));
        merchantConversionInputFormat.setNestedFileEnumeration(true);

        DataStreamSource<String> merchantConversionsSource =
                env.readFile(
                        merchantConversionInputFormat,
                        MERCHANT_DEMAND_CONVERSION_FILE_SOURCE,
                        FileProcessingMode.PROCESS_CONTINUOUSLY,
                        10000);

        KafkaSource<ObjectNode> eventsSource =
                KafkaSource.<ObjectNode>builder()
                        .setBootstrapServers(EVENTS_SOURCE_BROKERS)
                        .setGroupId(EVENTS_SOURCE_CONSUMER_GROUP_ID)
                        .setClientIdPrefix("merchant-demand")
                        .setValueOnlyDeserializer(new JSONValueDeserializationSchema())
                        .setStartingOffsets(
                                OffsetsInitializer.timestamp(
                                        Instant.now().toEpochMilli()
                                                - Duration.ofMinutes(SLIDING_WINDOW_SIZE_IN_MINS)
                                                        .toMillis()))
                        .setTopics(EVENTS_SOURCE_TOPIC)
                        .setProperty("partition.discovery.interval.ms", "600000")
                        .build();

        WatermarkStrategy<ObjectNode> impressionsWatermarkStrategy =
                WatermarkStrategy.<ObjectNode>forBoundedOutOfOrderness(Duration.ofSeconds(300))
                        .withIdleness(Duration.ofSeconds(IDLENESS_TIME_IN_SECS))
                        .withTimestampAssigner(
                                (event, timestamp) ->
                                        Instant.parse(event.get("value").get("timestamp").asText())
                                                .toEpochMilli());

        DataStreamSource<ObjectNode> impressionEventsSource =
                env.fromSource(eventsSource, impressionsWatermarkStrategy, "impressions");

        Properties properties = new Properties();
        properties.setProperty("bootstrap.servers", MERCHANT_DEMAND_SINK_BROKERS);
        // Set timeout to deal with Semantic.EXACTLY_ONCE semantics
        properties.setProperty("transaction.timeout.ms", "900000");

        FlinkKafkaProducer<MerchantDemandConversion> merchantDemandConversionFlinkKafkaProducer =
                new FlinkKafkaProducer<>(
                        MERCHANT_DEMAND_SURGE_SINK_TOPIC, // target topic
                        new MerchantDemandConversionSerializationSchema(
                                MERCHANT_DEMAND_SURGE_SINK_TOPIC), // serialization schema
                        properties, // producer config
                        FlinkKafkaProducer.Semantic.EXACTLY_ONCE); // fault-tolerance

        MerchantDemand merchantDemand =
                new MerchantDemand(
                        merchantConversionsSource,
                        impressionEventsSource,
                        merchantDemandConversionFlinkKafkaProducer);

        merchantDemand.execute(env);
    }

    public static class ConversionEnrichmentFunction
            extends RichCoFlatMapFunction<
                    MerchantOnlineDevices, MerchantConversion, MerchantDemandConversion> {

        private transient ValueState<MerchantOnlineDevices> merchantDemandValueState;
        private transient ValueState<MerchantConversion> merchantConversionValueState;

        StateTtlConfig ttlConfig =
                StateTtlConfig.newBuilder(org.apache.flink.api.common.time.Time.hours(1))
                        .setUpdateType(StateTtlConfig.UpdateType.OnCreateAndWrite)
                        .setStateVisibility(StateTtlConfig.StateVisibility.NeverReturnExpired)
                        .build();

        @Override
        public void open(Configuration config) {
            ValueStateDescriptor<MerchantOnlineDevices> merchantDemandValueStateDescriptor =
                    new ValueStateDescriptor<>(
                            "saved merchant demand", MerchantOnlineDevices.class);
            merchantDemandValueStateDescriptor.enableTimeToLive(ttlConfig);
            merchantDemandValueState =
                    getRuntimeContext().getState(merchantDemandValueStateDescriptor);
            ValueStateDescriptor<MerchantConversion> merchantConversionValueStateDescriptor =
                    new ValueStateDescriptor<>(
                            "saved merchant conversion", MerchantConversion.class);
            merchantConversionValueStateDescriptor.enableTimeToLive(ttlConfig);
            merchantConversionValueState =
                    getRuntimeContext().getState(merchantConversionValueStateDescriptor);
        }

        @Override
        public void flatMap1(
                MerchantOnlineDevices merchantDemand, Collector<MerchantDemandConversion> collector)
                throws Exception {
            MerchantConversion merchantConversionValue = merchantConversionValueState.value();
            if (merchantConversionValue != null) {
                collector.collect(
                        new MerchantDemandConversion(merchantDemand, merchantConversionValue));
            } else {
                collector.collect(
                        new MerchantDemandConversion(
                                merchantDemand,
                                new MerchantConversion(
                                        merchantDemand.getMerchantId(),
                                        0,
                                        0,
                                        0.0f,
                                        Instant.now().getEpochSecond())));
            }
            merchantDemandValueState.update(merchantDemand);
        }

        @Override
        public void flatMap2(
                MerchantConversion merchantConversion,
                Collector<MerchantDemandConversion> collector)
                throws Exception {
            MerchantOnlineDevices merchantDemandValue = merchantDemandValueState.value();
            if (merchantDemandValue != null) {
                collector.collect(
                        new MerchantDemandConversion(merchantDemandValue, merchantConversion));
            } else {
                collector.collect(
                        new MerchantDemandConversion(
                                new MerchantOnlineDevices(
                                        merchantConversion.getMerchantId(),
                                        0,
                                        Instant.now().toEpochMilli()),
                                merchantConversion));
            }
            merchantConversionValueState.update(merchantConversion);
        }
    }

    /**
     * Processing Window for device id - merchant map
     *
     * <p>Each device id - merchant is keyed as merchant window, de duplicated and finally count of
     * unique device ids in the window is collected
     */
    public static class ProcessImpressionEventsFunction
            extends ProcessWindowFunction<
                    DeviceMerchantMap, MerchantOnlineDevices, Long, TimeWindow> {

        @Override
        public void process(
                Long merchantId,
                Context context,
                Iterable<DeviceMerchantMap> iterable,
                Collector<MerchantOnlineDevices> collector) {
            Set<String> seenDeviceIds = new HashSet<>();
            for (DeviceMerchantMap in : iterable) {
                String deviceId = in.getDeviceId();
                seenDeviceIds.add(deviceId);
            }

            collector.collect(
                    new MerchantOnlineDevices(
                            merchantId, seenDeviceIds.size(), context.window().getEnd()));
        }
    }
}
