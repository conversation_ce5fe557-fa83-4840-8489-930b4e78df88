package com.grofers.corestreams.merchantdemand.datatypes;

public class DeviceMerchantMap {

    private final Long merchantId;
    private final String deviceId;

    public DeviceMerchantMap(Long merchantId, String deviceId) {
        this.merchantId = merchantId;
        this.deviceId = deviceId;
    }

    public Long getMerchantId() {
        return merchantId;
    }

    public String getDeviceId() {
        return deviceId;
    }
}
