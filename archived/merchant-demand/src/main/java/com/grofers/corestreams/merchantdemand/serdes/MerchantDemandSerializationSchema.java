package com.grofers.corestreams.merchantdemand.serdes;

import org.apache.flink.api.common.serialization.SerializationSchema;
import org.apache.flink.streaming.connectors.kafka.KafkaSerializationSchema;

import com.grofers.corestreams.merchantdemand.datatypes.MerchantOnlineDevices;
import org.apache.kafka.clients.producer.ProducerRecord;

import javax.annotation.Nullable;

import java.nio.charset.StandardCharsets;

public class MerchantDemandSerializationSchema
        implements KafkaSerializationSchema<MerchantOnlineDevices> {

    private static final long serialVersionUID = -1L;
    private final String topic;

    public MerchantDemandSerializationSchema(String topic) {
        this.topic = topic;
    }

    @Override
    public void open(SerializationSchema.InitializationContext context) throws Exception {
        KafkaSerializationSchema.super.open(context);
    }

    @Override
    public ProducerRecord<byte[], byte[]> serialize(
            MerchantOnlineDevices merchantDemand, @Nullable Long aLong) {
        return new ProducerRecord<>(
                topic,
                merchantDemand.getMerchantId().toString().getBytes(StandardCharsets.UTF_8),
                merchantDemand.toString().getBytes(StandardCharsets.UTF_8));
    }
}
