package com.grofers.corestreams.merchantdemand.datatypes;

public class MerchantOnlineDevices {
    private final Long merchantId;
    private final Integer onlineDevices;
    private final Long eventTimestamp;

    public MerchantOnlineDevices(Long merchantId, Integer onlineDevices, Long eventTimestamp) {
        this.merchantId = merchantId;
        this.onlineDevices = onlineDevices;
        this.eventTimestamp = eventTimestamp;
    }

    public Long getMerchantId() {
        return merchantId;
    }

    public Integer getOnlineDevices() {
        return onlineDevices;
    }

    public Long getEventTimestamp() {
        return eventTimestamp;
    }

    @Override
    public String toString() {
        return "{"
                + "\"merchantId\": "
                + merchantId
                + ", \"onlineDevices\": "
                + onlineDevices
                + ", \"eventTimestamp\": "
                + eventTimestamp
                + '}';
    }
}
