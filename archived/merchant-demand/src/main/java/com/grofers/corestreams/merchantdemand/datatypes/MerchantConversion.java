package com.grofers.corestreams.merchantdemand.datatypes;

public class MerchantConversion {
    private Long merchantId;
    private Integer hourlyVisits;
    private Integer hourlyCheckouts;
    private Float conversionPercent;
    private Long eventTimestamp;

    public MerchantConversion(
            Long merchantId,
            Integer customersCount,
            Integer hourlyCheckouts,
            Float conversionPercent,
            Long eventTimestamp) {
        this.merchantId = merchantId;
        this.hourlyVisits = customersCount;
        this.hourlyCheckouts = hourlyCheckouts;
        this.conversionPercent = conversionPercent;
        this.eventTimestamp = eventTimestamp;
    }

    public MerchantConversion() {}

    public Long getMerchantId() {
        return merchantId;
    }

    public void setMerchantId(Long merchantId) {
        this.merchantId = merchantId;
    }

    public Integer getHourlyVisits() {
        return hourlyVisits;
    }

    public void setHourlyVisits(Integer hourlyVisits) {
        this.hourlyVisits = hourlyVisits;
    }

    public Integer getHourlyCheckouts() {
        return hourlyCheckouts;
    }

    public void setHourlyCheckouts(Integer hourlyCheckouts) {
        this.hourlyCheckouts = hourlyCheckouts;
    }

    public Float getConversionPercent() {
        return conversionPercent;
    }

    public void setConversionPercent(Float conversionPercent) {
        this.conversionPercent = conversionPercent;
    }

    public Long getEventTimestamp() {
        return eventTimestamp;
    }

    public void setEventTimestamp(Long eventTimestamp) {
        this.eventTimestamp = eventTimestamp;
    }
}
