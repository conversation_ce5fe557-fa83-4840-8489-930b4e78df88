package com.grofers.corestreams.merchantdemand.configs;

import org.apache.flink.api.java.utils.ParameterTool;

public final class JobConfigs {
    public static String CHECKPOINTS_STORAGE_LOCATION;
    public static String MERCHANT_DEMAND_CONVERSION_FILE_SOURCE;
    public static String EVENTS_SOURCE_BROKERS;
    public static String EVENTS_SOURCE_CONSUMER_GROUP_ID;
    public static String EVENTS_SOURCE_TOPIC;
    public static String MERCHANT_DEMAND_SINK_BROKERS;
    public static String MERCHANT_DEMAND_SURGE_SINK_TOPIC;
    public static int IDLENESS_TIME_IN_SECS;
    public static int SLIDING_WINDOW_SIZE_IN_MINS;
    public static int SLIDING_WINDOW_SLIDE_IN_SECS;

    public JobConfigs(ParameterTool parameterTool) {
        this.CHECKPOINTS_STORAGE_LOCATION = parameterTool.get("CHECKPOINTS_STORAGE_LOCATION");
        this.MERCHANT_DEMAND_CONVERSION_FILE_SOURCE =
                parameterTool.get("MERCHANT_DEMAND_CONVERSION_FILE_SOURCE");
        this.EVENTS_SOURCE_BROKERS = parameterTool.get("EVENTS_SOURCE_BROKERS");
        this.EVENTS_SOURCE_CONSUMER_GROUP_ID = parameterTool.get("EVENTS_SOURCE_CONSUMER_GROUP_ID");
        this.EVENTS_SOURCE_TOPIC = parameterTool.get("EVENTS_SOURCE_TOPIC");
        this.MERCHANT_DEMAND_SINK_BROKERS = parameterTool.get("MERCHANT_DEMAND_SINK_BROKERS");
        this.MERCHANT_DEMAND_SURGE_SINK_TOPIC =
                parameterTool.get("MERCHANT_DEMAND_SURGE_SINK_TOPIC");
        this.IDLENESS_TIME_IN_SECS = parameterTool.getInt("IDLENESS_TIME_IN_SECS");
        this.SLIDING_WINDOW_SIZE_IN_MINS = parameterTool.getInt("SLIDING_WINDOW_SIZE_IN_MINS");
        this.SLIDING_WINDOW_SLIDE_IN_SECS = parameterTool.getInt("SLIDING_WINDOW_SLIDE_IN_SECS");
    }
}
