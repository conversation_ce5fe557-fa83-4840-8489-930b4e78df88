package com.grofers.corestreams.merchantdemand.datatypes;

public class MerchantDemandConversion {
    private final MerchantOnlineDevices merchantDemand;
    private final MerchantConversion merchantConversion;

    public Long getMerchantId() {
        return merchantId;
    }

    private final Long merchantId;

    public MerchantDemandConversion(
            MerchantOnlineDevices merchantDemand, MerchantConversion merchantConversion) {
        this.merchantDemand = merchantDemand;
        this.merchantConversion = merchantConversion;
        this.merchantId =
                merchantDemand.getMerchantId() != null
                        ? merchantDemand.getMerchantId()
                        : merchantConversion.getMerchantId();
    }

    public Integer getOnlineDevices() {
        return this.merchantDemand.getOnlineDevices();
    }

    @Override
    public String toString() {
        return "{"
                + "\"merchantId\": "
                + merchantId
                + ", \"onlineDevices\": "
                + merchantDemand.getOnlineDevices()
                + ", \"estimatedConversionPercent\": "
                + merchantConversion.getConversionPercent()
                + ", \"eventTimestamp\": "
                + merchantDemand.getEventTimestamp()
                + '}';
    }
}
