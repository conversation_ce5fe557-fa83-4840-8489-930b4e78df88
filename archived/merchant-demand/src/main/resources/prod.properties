
# Properties for prod environment

#Checkpoints
CHECKPOINTS_STORAGE_LOCATION = s3a://prod-data-flink-states/core-streams/merchant-demand/checkpoints/

# Merchant historical conversion file source
MERCHANT_DEMAND_CONVERSION_FILE_SOURCE = s3a://grofers-business-intelligence/merchant_demand_conversions/

# Events Source
EVENTS_SOURCE_BROKERS = b-1.prod-data-events.771kbr.c4.kafka.ap-southeast-1.amazonaws.com:9092,b-2.prod-data-events.771kbr.c4.kafka.ap-southeast-1.amazonaws.com:9092,b-3.prod-data-events.771kbr.c4.kafka.ap-southeast-1.amazonaws.com:9092
EVENTS_SOURCE_CONSUMER_GROUP_ID = corestreams.serviceability.merchant-demand.group
EVENTS_SOURCE_TOPIC = rudder.track.mobile.homepage_visit

# Kafka Sink
MERCHANT_DEMAND_SINK_BROKERS = b-1.prod-data-corestr.uynfnv.c4.kafka.ap-southeast-1.amazonaws.com:9092,b-2.prod-data-corestr.uynfnv.c4.kafka.ap-southeast-1.amazonaws.com:9092,b-3.prod-data-corestr.uynfnv.c4.kafka.ap-southeast-1.amazonaws.com:9092
MERCHANT_DEMAND_SURGE_SINK_TOPIC =  corestreams.serviceability.merchant-demand

#Watermark Idleness Configs
IDLENESS_TIME_IN_SECS = 10

# Window Configs
SLIDING_WINDOW_SIZE_IN_MINS = 15
SLIDING_WINDOW_SLIDE_IN_SECS = 10
