
# Properties for stage environment

#Checkpoints
CHECKPOINTS_STORAGE_LOCATION = s3a://grofers-test-dse-singapore/merchant-demand

# Merchant historical conversion file source
MERCHANT_DEMAND_CONVERSION_FILE_SOURCE = s3a://grofers-business-intelligence/merchant_demand_conversions/

# Events Source
EVENTS_SOURCE_BROKERS =  b-1.prod-data-events.771kbr.c4.kafka.ap-southeast-1.amazonaws.com:9092,b-2.prod-data-events.771kbr.c4.kafka.ap-southeast-1.amazonaws.com:9092,b-3.prod-data-events.771kbr.c4.kafka.ap-southeast-1.amazonaws.com:9092
EVENTS_SOURCE_CONSUMER_GROUP_ID = test-merchant-demand-group-v1
EVENTS_SOURCE_TOPIC = rudder.track.mobile.homepage_visit

# Kafka Sink
MERCHANT_DEMAND_SINK_BROKERS = b-1.test-dse-common.iosmgv.c4.kafka.ap-southeast-1.amazonaws.com:9092,b-2.test-dse-common.iosmgv.c4.kafka.ap-southeast-1.amazonaws.com:9092,b-3.test-dse-common.iosmgv.c4.kafka.ap-southeast-1.amazonaws.com:9092
MERCHANT_DEMAND_SURGE_SINK_TOPIC = test-merchant-demand-v1

#Watermark Idleness Configs
IDLENESS_TIME_IN_SECS = 10

# Window Configs
SLIDING_WINDOW_SIZE_IN_MINS = 15
SLIDING_WINDOW_SLIDE_IN_SECS = 10
