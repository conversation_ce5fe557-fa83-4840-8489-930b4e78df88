package com.grofers.corestreams.merchantdemand.utils;

import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.JsonNode;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.node.ObjectNode;

import com.grofers.corestreams.merchantdemand.datatypes.MerchantOnlineDevices;

import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

public class DataGenerator {
    public static ObjectNode impressionEvent(
            Long merchant_id, String device_id, Instant eventTimestamp) {
        /*
        ObjectNode skeleton ->
        "value":{
                "context":{
                  "traits": {
                    "merchant_id": 123
                  },
                  "device": {
                    "id": 10001
                  }
                }
              }
         */
        final ObjectMapper mapper = new ObjectMapper();
        final ObjectNode root = mapper.createObjectNode();
        ObjectNode value = root.putObject("value");
        ObjectNode context = value.putObject("context");

        value.set(
                "timestamp",
                mapper.convertValue(
                        Instant.ofEpochMilli(eventTimestamp.toEpochMilli()).toString(),
                        JsonNode.class));
        context.putObject("traits")
                .set("merchant_id", mapper.convertValue(merchant_id, JsonNode.class));
        context.putObject("device").set("id", mapper.convertValue(device_id, JsonNode.class));
        return root;
    }

    public static List<ObjectNode> getImpressionEventList() throws InterruptedException {
        List<ObjectNode> impressionEventList = new ArrayList<>();
        Instant midnight = Instant.now().truncatedTo(ChronoUnit.DAYS);
        ObjectNode m1 = DataGenerator.impressionEvent(123L, "ABC101", midnight);
        ObjectNode m2 = DataGenerator.impressionEvent(345L, "ABC102", midnight.plusSeconds(10));
        ObjectNode m3 = DataGenerator.impressionEvent(123L, "ABC103", midnight.plusSeconds(20));
        ObjectNode m4 = DataGenerator.impressionEvent(123L, "ABC104", midnight.plusSeconds(30));
        ObjectNode m5 = DataGenerator.impressionEvent(345L, "ABC105", midnight.plusSeconds(40));
        ObjectNode m6 = DataGenerator.impressionEvent(123L, "ABC106", midnight.plusSeconds(50));
        ObjectNode m7 = DataGenerator.impressionEvent(123L, "ABC106", midnight.plusSeconds(60));
        ObjectNode m8 = DataGenerator.impressionEvent(789L, "ABC107", midnight.plusSeconds(60));
        ObjectNode m9 =
                DataGenerator.impressionEvent(789L, "ABC108", Instant.now().plusSeconds(3600));

        impressionEventList.addAll(Arrays.asList(m1, m2, m3, m4, m5, m6, m7, m8, m9));
        return impressionEventList;
    }

    public static List<MerchantOnlineDevices> getMerchantOnlineDevicesList() {
        List<MerchantOnlineDevices> merchantOnlineDevicesList = new ArrayList<>();
        MerchantOnlineDevices m1 = new MerchantOnlineDevices(123L, 5, Instant.now().toEpochMilli());
        MerchantOnlineDevices m2 = new MerchantOnlineDevices(789L, 2, Instant.now().toEpochMilli());

        merchantOnlineDevicesList.addAll(Arrays.asList(m1, m2));
        return merchantOnlineDevicesList;
    }

    public static String[] getMerchantConversionsStrings() {
        Instant midnight = Instant.now().truncatedTo(ChronoUnit.DAYS);

        class SampleMerchantConversionsStrings {
            public String generateMerchantConversions(
                    Long merchantId,
                    Integer hourlyVisits,
                    Integer hourlyCheckouts,
                    Float conversionPercent,
                    Long eventTimestamp) {
                return merchantId
                        + ","
                        + hourlyVisits
                        + ","
                        + hourlyCheckouts
                        + ","
                        + conversionPercent
                        + ","
                        + eventTimestamp;
            }

            public String generateMerchantConversions() {
                return "123,10,2,20.00," + midnight.toEpochMilli();
            }
        }

        String[] merchantConversions =
                new String[] {
                    "merchant_id,hourly_dau,hourly_checkouts,hourly_conversion",
                    new SampleMerchantConversionsStrings().generateMerchantConversions(),
                    new SampleMerchantConversionsStrings()
                            .generateMerchantConversions(
                                    234L, 100, 10, 10.00f, midnight.toEpochMilli()),
                    new SampleMerchantConversionsStrings()
                            .generateMerchantConversions(
                                    345L, 30, 10, 33.3333f, midnight.toEpochMilli()),
                    new SampleMerchantConversionsStrings().generateMerchantConversions(),
                    "merchant_id,hourly_dau,hourly_checkouts,hourly_conversion",
                    new SampleMerchantConversionsStrings().generateMerchantConversions(),
                    new SampleMerchantConversionsStrings()
                            .generateMerchantConversions(
                                    896L, 80, 10, 12.50f, midnight.toEpochMilli()),
                    new SampleMerchantConversionsStrings()
                            .generateMerchantConversions(
                                    123L, 40, 2, 5.00f, midnight.plusSeconds(50).toEpochMilli())
                };

        return merchantConversions;
    }
}
