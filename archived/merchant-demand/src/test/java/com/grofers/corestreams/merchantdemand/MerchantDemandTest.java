package com.grofers.corestreams.merchantdemand;

import org.apache.flink.annotation.VisibleForTesting;
import org.apache.flink.api.common.JobExecutionResult;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.runtime.testutils.MiniClusterResourceConfiguration;
import org.apache.flink.streaming.api.datastream.DataStreamSource;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.streaming.api.functions.sink.SinkFunction;
import org.apache.flink.streaming.api.functions.source.SourceFunction;
import org.apache.flink.streaming.api.watermark.Watermark;
import org.apache.flink.test.util.MiniClusterWithClientResource;

import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.node.ObjectNode;

import com.grofers.corestreams.merchantdemand.configs.JobConfigs;
import com.grofers.corestreams.merchantdemand.configs.JobConfigsManager;
import com.grofers.corestreams.merchantdemand.datatypes.MerchantDemandConversion;
import com.grofers.corestreams.merchantdemand.utils.DataGenerator;
import org.junit.Test;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.extension.BeforeAllCallback;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.api.extension.ExtensionContext;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.NullAndEmptySource;
import org.junit.jupiter.params.provider.ValueSource;

import java.io.IOException;
import java.time.Instant;
import java.util.*;
import java.util.stream.Collectors;

public class MerchantDemandTest {

    private static StreamExecutionEnvironment streamExecutionTestEnvironment;

    /** Creating a mini cluster to run test-cases * */
    public class CreateFlinkTestCLuster implements BeforeAllCallback {

        @Override
        public void beforeAll(ExtensionContext extensionContext) throws Exception {
            MiniClusterWithClientResource flinkTestCluster =
                    new MiniClusterWithClientResource(
                            new MiniClusterResourceConfiguration.Builder()
                                    .setNumberSlotsPerTaskManager(2)
                                    .setNumberTaskManagers(1)
                                    .build());
        }
    }

    @BeforeAll
    /** Function to set the configs for testing */
    public static void setEnvironmentForTestCases()
            throws JobConfigsManager.InvalidEnvironmentException, IOException {

        // Initialise stream environment
        streamExecutionTestEnvironment = StreamExecutionEnvironment.getExecutionEnvironment();
        streamExecutionTestEnvironment.setParallelism(1);
        String userEnv = "stage";
        // Set PropertiesPath for userEnv
        JobConfigsManager.setJobConfigsPath(userEnv);
        // Get PropertiesPath for userEnv
        String jobPropertiesPath = JobConfigsManager.getJobConfigsPath(userEnv);
        // Get Global Job Properties
        ParameterTool flinkGlobalProperties =
                JobConfigsManager.setGlobalJobConfigs(
                        streamExecutionTestEnvironment, jobPropertiesPath);

        // Create a JobConfigs object to access globally set flink properties
        JobConfigs jobConfigs = new JobConfigs(flinkGlobalProperties);
    }

    /** Flink Sink to collect values for testing */
    public static class CollectMerchantDemandConversionSink
            implements SinkFunction<MerchantDemandConversion> {
        // Used CopyOnWriteArrayList for parallelism
        public static final List<MerchantDemandConversion> values = new ArrayList<>();

        @Override
        public synchronized void invoke(MerchantDemandConversion value, Context context)
                throws Exception {
            values.add(value);
        }
    }

    @AfterEach
    @VisibleForTesting
    /** Function to clear jobPropertiesPath */
    public void clearJobPropertiesPath() {
        JobConfigsManager.jobConfigsPath = null;
    }

    @ParameterizedTest(name = "#{index} - Run test with args={0}")
    @NullAndEmptySource
    /** Function to test null and empty environments */
    public void testEnvironmentPropertiesForNullEnvirons(String userEnv) {
        // Throws an exception while setting null environments
        Assertions.assertTrue(
                Assertions.assertThrows(
                                JobConfigsManager.InvalidEnvironmentException.class,
                                () -> JobConfigsManager.setJobConfigsPath(userEnv))
                        .getMessage()
                        .contains("USER ENVIRONMENT IS INVALID"));
    }

    @ParameterizedTest(name = "#{index} - Run test with args={0}")
    @ValueSource(strings = {"prod", "stage", "xyz", "123", "@#$78"})
    /** Function to check whether variables are passed according to environment * */
    public void testEnvironmentPropertiesForNonNullEnvirons(String userEnv)
            throws JobConfigsManager.InvalidEnvironmentException {

        // Adding elements to env HashMap
        Map<String, String> envMap = new HashMap<>();
        envMap.put("prod", "src/main/resources/prod.properties");
        envMap.put("stage", "src/main/resources/stage.properties");
        envMap.put("xyz", "src/main/resources/stage.properties");
        envMap.put("123", "src/main/resources/stage.properties");
        envMap.put("@#$78", "src/main/resources/stage.properties");

        // Check whether properties are set before calling setter for JobPropertiesManager
        Assertions.assertNull(JobConfigsManager.getJobConfigsPath(userEnv));
        // Setting jobProperties
        JobConfigsManager.setJobConfigsPath(userEnv);
        Assertions.assertEquals(envMap.get(userEnv), JobConfigsManager.getJobConfigsPath(userEnv));
    }

    /** Source Function replicating kafka source behavior */
    private static class ImpressionEventsSource implements SourceFunction<ObjectNode> {

        private final ObjectNode[] events;

        public ImpressionEventsSource(ObjectNode[] events) {
            this.events = events;
        }

        @Override
        public void run(SourceContext<ObjectNode> ctx) throws Exception {
            for (int i = 0; i < events.length; i++) {
                ctx.collectWithTimestamp(
                        events[i],
                        Instant.parse(events[i].get("value").get("timestamp").asText())
                                .toEpochMilli());
                ctx.emitWatermark(
                        new Watermark(
                                Instant.parse(events[i].get("value").get("timestamp").asText())
                                        .toEpochMilli()));
            }
        }

        @Override
        public void cancel() {}
    }

    @Test
    @ExtendWith(CreateFlinkTestCLuster.class)
    public void merchantDemandConversionEnrinchmentFunctionSuccessfulTest() throws Exception {

        String[] merchantConversions = DataGenerator.getMerchantConversionsStrings();
        DataStreamSource<String> merchantConversionsSource =
                streamExecutionTestEnvironment.fromElements(merchantConversions);

        List<ObjectNode> impressionEvents = DataGenerator.getImpressionEventList();

        DataStreamSource<ObjectNode> impressionEventsSource =
                streamExecutionTestEnvironment.addSource(
                        new ImpressionEventsSource(impressionEvents.toArray(new ObjectNode[0])));

        CollectMerchantDemandConversionSink merchantDemandSink =
                new CollectMerchantDemandConversionSink();

        JobExecutionResult jobExecutionResult =
                new MerchantDemand(
                                merchantConversionsSource,
                                impressionEventsSource,
                                merchantDemandSink)
                        .execute(streamExecutionTestEnvironment);

        Assertions.assertEquals(286, CollectMerchantDemandConversionSink.values.size());

        List<MerchantDemandConversion> eventsAtStartOfWindowWithZeroOnlineDevices =
                CollectMerchantDemandConversionSink.values.stream()
                        .filter(e -> e.getOnlineDevices() == 0)
                        .collect(Collectors.toList());
        List<MerchantDemandConversion> eventsWithoutZeroOnlineDevices =
                CollectMerchantDemandConversionSink.values.stream()
                        .filter(e -> e.getOnlineDevices() != 0)
                        .collect(Collectors.toList());
        Assertions.assertEquals(7, eventsAtStartOfWindowWithZeroOnlineDevices.size());
        Assertions.assertEquals(279, eventsWithoutZeroOnlineDevices.size());
    }
}
