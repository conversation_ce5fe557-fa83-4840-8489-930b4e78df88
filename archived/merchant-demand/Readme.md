## Merchant Demand Stream

This core stream is an indicator of how many orders a merchant can get based on previous conversions & the current demand on app. THe idea is to use real-time traffic and conversion rates per store to have a leading metric drive surge charge alongside the current capacity utilisation at stores.

This core stream is makes use of the merchant demand & merchant conversion to calculate the surge in demand at a merchant level

**Merchant Demand**

Merchant demand is calculated using a sliding window of App Launches happening over a period of 15 mins (Avg session time of our users to reduce the jitters ) and window being moved every minute.

- Source: Rudder events Kafka Topic
- Sink: Core streams Kafka Topic

**Merchant Conversion**

Merchant conversion will be calculated using our event data and facts over a large period of time to calculate a base line conversion. This can be stored in a db since it is just a mapping of a merchant and its conversion.

- Source: S3 Bucket
- Sink: Core streams Kafka Topic

We join these two streams by `MerchantId` to get a real-time merchant level view on the **Surge in Demand**

**Window Size:** We use a sliding window of 15mins that slides every 10 seconds

**Sample Response:**

```
key: 26383 value: {"merchantId": 26383, "onlineDevices": 102, "estimatedConversionPercent": 6.714286, "eventTimestamp": 1633602830000}
```
