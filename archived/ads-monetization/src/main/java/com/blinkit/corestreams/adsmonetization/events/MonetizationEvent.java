package com.blinkit.corestreams.adsmonetization.events;

import java.time.Instant;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class MonetizationEvent {
    String messageId;
    Instant eventTimestamp;
    String eventName;
    Integer adsCampaignId;
    Integer adsSubcampaignId;
    String adsType;
    String pageName;
    String osName;

    public MonetizationEvent(RudderMonetizationEvent rudderMonetizationEvent) {
        this.messageId = rudderMonetizationEvent.getMessageId();
        this.adsCampaignId =
                Integer.parseInt(rudderMonetizationEvent.getProperties().getAdsCampaignId());
        this.adsSubcampaignId =
                Integer.parseInt(rudderMonetizationEvent.getProperties().getAdsSubcampaignId());
        this.adsType = rudderMonetizationEvent.getProperties().getAdsType();
        this.pageName = rudderMonetizationEvent.getProperties().getPageName();
        // Using receivedAt to reduce delay drop of events
        this.eventTimestamp = rudderMonetizationEvent.getReceivedAt().toInstant();
        this.eventName = rudderMonetizationEvent.getEventName();
        this.osName = getOsName(rudderMonetizationEvent.getContext().getOs().getName());
    }

    private String getOsName(String osName) {
        if (osName == null) {
            return "";
        } else {
            return osName.toLowerCase();
        }
    }
}
