package com.blinkit.corestreams.adsmonetization.events;

import com.grofers.gandalf.events.RudderEvent;
import lombok.Getter;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.annotation.JsonProperty;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.annotation.JsonSerialize;

@JsonSerialize
@Getter
@JsonIgnoreProperties(ignoreUnknown = true)
public class RudderMonetizationEvent extends RudderEvent {
    @JsonProperty(value = "properties")
    EventProperties properties = null;
}
