/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.blinkit.corestreams.adsmonetization.serdes;

import com.blinkit.corestreams.adsmonetization.events.MonetizationEvent;
import com.blinkit.corestreams.adsmonetization.events.RudderMonetizationEvent;
import java.io.IOException;
import org.apache.flink.api.common.serialization.DeserializationSchema;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class MonetizationEventDeserializer implements DeserializationSchema<MonetizationEvent> {
    private static final long serialVersionUID = 1L;
    private static final Logger LOG = LoggerFactory.getLogger(MonetizationEventDeserializer.class);
    private static final ObjectMapper objectMapper = new ObjectMapper();

    @Override
    public MonetizationEvent deserialize(byte[] bytes) throws IOException {
        try {
            return new MonetizationEvent(
                    objectMapper.readValue(bytes, RudderMonetizationEvent.class));
        } catch (Exception exception) {
            exception.printStackTrace();
            LOG.info("Failed to deserialize rudder message: {}", new String(bytes));
            return null;
        }
    }

    @Override
    public boolean isEndOfStream(MonetizationEvent monetizationEvent) {
        return false;
    }

    @Override
    public TypeInformation<MonetizationEvent> getProducedType() {
        return TypeInformation.of(MonetizationEvent.class);
    }
}
