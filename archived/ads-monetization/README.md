# Ads Monetization

We show advertisements on our platform which is monetized based on the number of impressions so we do not want to keep showing the same ad once we cross the promised number impressions, this stream helps the ads team do exactly that.

In this job we calculate the number of distinct impressions at a campaign and sub-campaign level with a [Cummulative window)](https://nightlies.apache.org/flink/flink-docs-master/docs/dev/table/sql/queries/window-tvf/#cumulate) of 30mins with 1min steps, The aggregate data is now pushed to Kafka which is then ingested by the Ads team
