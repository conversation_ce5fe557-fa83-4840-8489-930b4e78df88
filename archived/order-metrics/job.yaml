name: Order Metrics
image: 442534439095.dkr.ecr.ap-southeast-1.amazonaws.com/data/flink-streams/observability/order-metrics
flink_config:
  taskmanager.memory.process.size: 8192m
  jobmanager.memory.process.size: 1024m
  taskmanager.memory.managed.size: 0
  restart-strategy: exponential-delay
  state.backend: hashmap
  state.checkpoint-storage: filesystem
  state.checkpoints.dir: s3a://prod-data-flink-states/flink-streams/observability/order-metrics/checkpoints/
  state.backend.incremental: true
  execution.checkpointing.unaligned: true
  execution.checkpointing.mode: EXACTLY_ONCE
  execution.checkpointing.max-concurrent-checkpoints: 1
  execution.checkpointing.externalized-checkpoint-retention: DELETE_ON_CANCELLATION
  execution.checkpointing.interval: 10 min
  execution.checkpointing.min-pause: 5 min
  execution.checkpointing.timeout: 3 min
  execution.checkpointing.tolerable-failed-checkpoints: 2
deployment_config:
  kubernetes.rest-service.exposed.type: NodePort
  metrics.reporters: prom
  metrics.reporter.prom.class: org.apache.flink.metrics.prometheus.PrometheusReporter
allow_non_restored_state: False # Set to True when changes are made to Operator
codeartifact_access: True
