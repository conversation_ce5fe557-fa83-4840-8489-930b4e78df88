CDC_SOURCE_BROKERS = b-2.test-dse-common.iosmgv.c4.kafka.ap-southeast-1.amazonaws.com:9092,b-3.test-dse-common.iosmgv.c4.kafka.ap-southeast-1.amazonaws.com:9092,b-1.test-dse-common.iosmgv.c4.kafka.ap-southeast-1.amazonaws.com:9092
CDC_SOURCE_CONSUMER_GROUP_ID = flink-observability-order_metrics-consumer_group-v1
CDC_SOURCE_SCHEMA_REGISTRY_URL = http://172.31.84.206:8081
OMS_ORDER_TOPIC = postgres.oms_bifrost.public.oms_order
OMS_ORDER_ITEM_TOPIC = postgres.oms_bifrost.public.oms_order_item
OMS_MERCHANT_TOPIC = postgres.oms_bifrost.public.oms_merchant
ORDER_METRICS_SINK_BROKERS = b-2.test-dse-common.iosmgv.c4.kafka.ap-southeast-1.amazonaws.com:9092,b-3.test-dse-common.iosmgv.c4.kafka.ap-southeast-1.amazonaws.com:9092,b-1.test-dse-common.iosmgv.c4.kafka.ap-southeast-1.amazonaws.com:9092
ORDER_METRICS_SINK_TOPIC = observability.metrics.order-metrics
FIRST_ORDER_COMPLETED_SOURCE_TOPIC = cdp.track.backend.ordercompleted
DATA_EVENTS_SOURCE_BROKERS = b-2.test-dse-common.iosmgv.c4.kafka.ap-southeast-1.amazonaws.com:9092,b-3.test-dse-common.iosmgv.c4.kafka.ap-southeast-1.amazonaws.com:9092,b-1.test-dse-common.iosmgv.c4.kafka.ap-southeast-1.amazonaws.com:9092
FIRST_ORDER_COMPLETED_SOURCE_CONSUMER_GROUP_ID = flink-observability-first-order_metrics-consumer_group-v1