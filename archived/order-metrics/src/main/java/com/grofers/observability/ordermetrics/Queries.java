package com.grofers.observability.ordermetrics;

public class Queries {
    public static final String OMS_ORDER_CREATE_SQL =
            "CREATE TABLE IF NOT EXISTS oms_order ( id INT PRIMARY KEY,"
                    + " install_ts STRING,"
                    + " update_ts STRING,"
                    + " customer_id INT,"
                    + " slot_properties STRING,"
                    + " current_status VARCHAR(50),"
                    + " total_cost DOUBLE,"
                    + " delivery_cost DOUBLE,"
                    + " discount DOUBLE,"
                    + " net_cost DOUBLE,"
                    + " procurement_amount DOUBLE,"
                    + " type VARCHAR(63),"
                    + " direction VARCHAR(31),"
                    + " cart_id BIGINT,"
                    + " merchant_id INT,"
                    + " wallet_amount DOUBLE,"
                    + " additional_charges_amount DOUBLE,"
                    + " flink_install_ts AS stringToTimestamp(install_ts),"
                    + " flink_update_ts AS stringToTimestamp(update_ts),"
                    + " WATERMARK FOR flink_update_ts AS flink_update_ts - INTERVAL '60' seconds "
                    + ") WITH ( "
                    + " 'connector' = 'kafka',"
                    + " 'format' = 'debezium-avro-confluent',"
                    + " 'topic' = '%s',"
                    + " 'properties.bootstrap.servers' = '%s',"
                    + " 'properties.group.id' = '%s',"
                    + " 'scan.startup.mode' = 'group-offsets', "
                    + " 'debezium-avro-confluent.url' = '%s'"
                    + "  )";

    public static final String OMS_MERCHANT_CREATE_SQL =
            "CREATE TABLE IF NOT EXISTS oms_merchant ("
                    + " id INT PRIMARY KEY,"
                    + " external_id BIGINT,"
                    + " city_id INT,"
                    + " city_name STRING,"
                    + " name STRING,"
                    + " update_ts STRING,"
                    + " flink_update_ts AS stringToTimestamp(update_ts),"
                    + " WATERMARK FOR flink_update_ts AS flink_update_ts - INTERVAL '60' seconds "
                    + ") WITH (  "
                    + " 'connector' = 'kafka',"
                    + " 'format' = 'debezium-avro-confluent',"
                    + " 'topic' = '%s', "
                    + " 'properties.bootstrap.servers' = '%s',"
                    + " 'properties.group.id' = '%s', "
                    + " 'scan.startup.mode' = 'group-offsets',   "
                    + " 'debezium-avro-confluent.url' = '%s'"
                    + " )";

    public static final String OMS_ORDER_ITEM_CREATE_SQL =
            "CREATE TABLE IF NOT EXISTS oms_order_item ("
                    + " id INT PRIMARY KEY,"
                    + " order_id INT,"
                    + " update_ts STRING,"
                    + " quantity INT,"
                    + " flink_update_ts AS stringToTimestamp(update_ts),"
                    + " WATERMARK FOR flink_update_ts AS flink_update_ts - INTERVAL '60' seconds "
                    + ") WITH ("
                    + " 'connector' = 'kafka',"
                    + " 'format' = 'debezium-avro-confluent',"
                    + " 'topic' = '%s', "
                    + " 'properties.bootstrap.servers' = '%s',"
                    + " 'properties.group.id' = '%s', "
                    + " 'scan.startup.mode' = 'group-offsets', "
                    + " 'debezium-avro-confluent.url' = '%s'"
                    + " )";

    public static final String OMS_FIRST_ORDER_COMPLETED_CREATE_SQL =
            "CREATE TABLE IF NOT EXISTS oms_first_order_completed_details ( order_id INT,"
                + " is_first_order_completed BOOLEAN, is_first_order_delivered BOOLEAN,"
                + " event_time_epoch BIGINT, flink_event_time_epoch AS"
                + " TO_TIMESTAMP_LTZ(event_time_epoch, 0), WATERMARK FOR flink_event_time_epoch AS"
                + " flink_event_time_epoch - INTERVAL '60' seconds ) WITH ( 'connector' = 'kafka',"
                + " 'format' = 'json', 'topic' = '%s',  'properties.bootstrap.servers' = '%s',"
                + " 'properties.group.id' = '%s',  'scan.startup.mode' = 'group-offsets'  )";

    public static final String ORDER_FACT_METRIC =
            "INSERT INTO order_metrics WITH orders_with_item_count AS (     SELECT count(id) as"
                + " item_count, sum(quantity) as total_items_quantity,      order_id     FROM"
                + " oms_order_item     GROUP BY order_id ) SELECT o.id,"
                + " UNIX_TIMESTAMP(DATE_FORMAT(o.flink_install_ts, 'yyyy-MM-dd HH:mm:ss')) as"
                + " insert_timestamp_epoch,        UNIX_TIMESTAMP(DATE_FORMAT(o.flink_update_ts,"
                + " 'yyyy-MM-dd HH:mm:ss')) as update_timestamp_epoch,        o.customer_id,      "
                + "  o.current_status,        o.total_cost,        o.delivery_cost,       "
                + " o.discount,        o.net_cost,        o.procurement_amount,        o.type,    "
                + "    o.direction,        o.cart_id,        om.external_id as merchant_id,       "
                + " o.wallet_amount,        o.additional_charges_amount,        om.city_id,       "
                + " om.city_name, om.name as merchant_name, oic.item_count,"
                + " oic.total_items_quantity, CAST(JSON_VALUE(o.slot_properties, '$.slot_charge'"
                + " DEFAULT 0.0 ON EMPTY DEFAULT 0.0 ON ERROR) AS DOUBLE) as slot_charge,"
                + " CAST(JSON_VALUE(o.slot_properties, '$.checkout_properties.slot_charge' DEFAULT"
                + " 0.0 ON EMPTY DEFAULT 0.0 ON ERROR) AS DOUBLE) as checkout_slot_charge,"
                + " ofo.is_first_order_completed, ofo.is_first_order_delivered FROM oms_order as o"
                + " INNER JOIN oms_merchant as om on o.merchant_id = om.id INNER JOIN"
                + " orders_with_item_count as oic on o.id = oic.order_id LEFT JOIN"
                + " oms_first_order_completed_details as ofo on o.id = ofo.order_id WHERE (o.type"
                + " is null or o.type in"
                + " ('RetailForwardOrder','RetailSuborder','DigitalForwardOrder','DropShippingForwardOrder'))"
                + " and om.city_name not in ('Not in service area','Hapur','test1207898732') and"
                + " om.city_name not like '%B2B%'";

    public static final String ORDER_FACT_CREATE_TABLE =
            "CREATE TABLE IF NOT EXISTS order_metrics ( "
                    + "    id INT PRIMARY KEY, "
                    + "    insert_timestamp_epoch BIGINT, "
                    + "    update_timestamp_epoch BIGINT, "
                    + "    customer_id INT, "
                    + "    current_status VARCHAR(50), "
                    + "    total_cost DOUBLE, "
                    + "    delivery_cost DOUBLE, "
                    + "    discount DOUBLE, "
                    + "    net_cost DOUBLE, "
                    + "    procurement_amount DOUBLE, "
                    + "    type VARCHAR(63), "
                    + "    direction VARCHAR(31), "
                    + "    cart_id BIGINT, "
                    + "    merchant_id BIGINT, "
                    + "    wallet_amount DOUBLE, "
                    + "    additional_charges_amount DOUBLE, "
                    + "    city_id INT, "
                    + "    city_name STRING, "
                    + "    merchant_name STRING, "
                    + "    item_count BIGINT, "
                    + "    total_items_quantity BIGINT, "
                    + "    slot_charge DOUBLE, "
                    + "    checkout_slot_charge DOUBLE, "
                    + "    is_first_order_completed BOOLEAN, "
                    + "    is_first_order_delivered BOOLEAN "
                    + ") WITH ( "
                    + "   'connector' = 'upsert-kafka', "
                    + "   'topic' = '%s', "
                    + "   'properties.bootstrap.servers' = '%s', "
                    + "   'key.format' = 'json', "
                    + "   'value.format' = 'json' "
                    + ")";
}
