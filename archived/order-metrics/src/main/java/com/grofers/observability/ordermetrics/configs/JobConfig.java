package com.grofers.observability.ordermetrics.configs;

public class JobConfig {
    public static String CDC_SOURCE_BROKERS;
    public static String CDC_SOURCE_CONSUMER_GROUP_ID;
    public static String CDC_SOURCE_SCHEMA_REGISTRY_URL;
    public static String OMS_ORDER_TOPIC;
    public static String OMS_ORDER_ITEM_TOPIC;
    public static String OMS_MERCHANT_TOPIC;
    public static String ORDER_METRICS_SINK_BROKERS;
    public static String ORDER_METRICS_SINK_TOPIC;
    public static String FIRST_ORDER_COMPLETED_SOURCE_TOPIC;
    public static String DATA_EVENTS_SOURCE_BROKERS;
    public static String FIRST_ORDER_COMPLETED_SOURCE_CONSUMER_GROUP_ID;

    public JobConfig(
            String CDC_SOURCE_BROKERS,
            String CDC_SOURCE_CONSUMER_GROUP_ID,
            String CDC_SOURCE_SCHEMA_REGISTRY_URL,
            String OMS_ORDER_TOPIC,
            String OMS_ORDER_ITEM_TOPIC,
            String OMS_MERCHANT_TOPIC,
            String ORDER_METRICS_SINK_BROKERS,
            String ORDER_METRICS_SINK_TOPIC,
            String FIRST_ORDER_COMPLETED_SOURCE_TOPIC,
            String DATA_EVENTS_SOURCE_BROKERS,
            String FIRST_ORDER_COMPLETED_SOURCE_CONSUMER_GROUP_ID) {
        JobConfig.CDC_SOURCE_BROKERS = CDC_SOURCE_BROKERS;
        JobConfig.CDC_SOURCE_CONSUMER_GROUP_ID = CDC_SOURCE_CONSUMER_GROUP_ID;
        JobConfig.CDC_SOURCE_SCHEMA_REGISTRY_URL = CDC_SOURCE_SCHEMA_REGISTRY_URL;
        JobConfig.OMS_ORDER_TOPIC = OMS_ORDER_TOPIC;
        JobConfig.OMS_ORDER_ITEM_TOPIC = OMS_ORDER_ITEM_TOPIC;
        JobConfig.OMS_MERCHANT_TOPIC = OMS_MERCHANT_TOPIC;
        JobConfig.ORDER_METRICS_SINK_BROKERS = ORDER_METRICS_SINK_BROKERS;
        JobConfig.ORDER_METRICS_SINK_TOPIC = ORDER_METRICS_SINK_TOPIC;
        JobConfig.FIRST_ORDER_COMPLETED_SOURCE_TOPIC = FIRST_ORDER_COMPLETED_SOURCE_TOPIC;
        JobConfig.DATA_EVENTS_SOURCE_BROKERS = DATA_EVENTS_SOURCE_BROKERS;
        JobConfig.FIRST_ORDER_COMPLETED_SOURCE_CONSUMER_GROUP_ID =
                FIRST_ORDER_COMPLETED_SOURCE_CONSUMER_GROUP_ID;
    }
}
