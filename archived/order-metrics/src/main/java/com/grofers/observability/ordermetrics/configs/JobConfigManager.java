package com.grofers.observability.ordermetrics.configs;

import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.table.api.TableEnvironment;

import java.io.IOException;
import java.util.Map;

public class JobConfigManager {
    public static String jobConfigsPath;

    public static class InvalidEnvironmentException extends Exception {
        public InvalidEnvironmentException(String errorMsg) {
            super(errorMsg);
        }
    }

    public static void setJobConfigsPath(String userEnv) throws InvalidEnvironmentException {

        if (userEnv != null && !userEnv.isEmpty()) {
            if (userEnv.equalsIgnoreCase("prod")) {
                jobConfigsPath = "src/main/resources/prod.properties";
            } else {
                jobConfigsPath = "src/main/resources/stage.properties";
            }
        } else {
            throw new InvalidEnvironmentException("USER ENVIRONMENT IS INVALID");
        }
    }

    public static String getJobConfigsPath(String userEnv) throws InvalidEnvironmentException {
        if (userEnv != null && !userEnv.isEmpty()) {
            return jobConfigsPath;
        }
        throw new InvalidEnvironmentException("USER ENVIRONMENT IS INVALID");
    }

    public static void setJobConfigs(TableEnvironment tableEnvironment, String jobConfigsPath)
            throws IOException {
        ParameterTool params = ParameterTool.fromPropertiesFile(jobConfigsPath);
        tableEnvironment.getConfig().addConfiguration(params.getConfiguration());
    }

    public static JobConfig getJobConfigs(TableEnvironment tableEnvironment) {
        Map<String, String> globalJobConfigsMap =
                tableEnvironment.getConfig().getConfiguration().toMap();
        return new JobConfig(
                globalJobConfigsMap.get("CDC_SOURCE_BROKERS"),
                globalJobConfigsMap.get("CDC_SOURCE_CONSUMER_GROUP_ID"),
                globalJobConfigsMap.get("CDC_SOURCE_SCHEMA_REGISTRY_URL"),
                globalJobConfigsMap.get("OMS_ORDER_TOPIC"),
                globalJobConfigsMap.get("OMS_ORDER_ITEM_TOPIC"),
                globalJobConfigsMap.get("OMS_MERCHANT_TOPIC"),
                globalJobConfigsMap.get("ORDER_METRICS_SINK_BROKERS"),
                globalJobConfigsMap.get("ORDER_METRICS_SINK_TOPIC"),
                globalJobConfigsMap.get("FIRST_ORDER_COMPLETED_SOURCE_TOPIC"),
                globalJobConfigsMap.get("DATA_EVENTS_SOURCE_BROKERS"),
                globalJobConfigsMap.get("FIRST_ORDER_COMPLETED_SOURCE_CONSUMER_GROUP_ID"));
    }
}
