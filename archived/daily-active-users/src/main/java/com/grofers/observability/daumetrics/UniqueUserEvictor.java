package com.grofers.observability.daumetrics;

import org.apache.flink.streaming.api.windowing.evictors.Evictor;
import org.apache.flink.streaming.api.windowing.windows.Window;
import org.apache.flink.streaming.runtime.operators.windowing.TimestampedValue;

import com.grofers.gandalf.events.RudderEvent;

import java.util.HashSet;
import java.util.Iterator;
import java.util.Set;

public class UniqueUserEvictor<W extends Window> implements Evictor<RudderEvent, W> {
    @Override
    public void evictBefore(
            Iterable<TimestampedValue<RudderEvent>> iterable,
            int i,
            W w,
            EvictorContext evictorContext) {}

    @Override
    public void evictAfter(
            Iterable<TimestampedValue<RudderEvent>> iterable,
            int i,
            W w,
            EvictorContext evictorContext) {
        this.evict(iterable, evictorContext);
    }

    private void evict(Iterable<TimestampedValue<RudderEvent>> elements, EvictorContext ctx) {
        Set<String> uniqueIds = new HashSet<>();
        Iterator<TimestampedValue<RudderEvent>> iterator = elements.iterator();
        while (iterator.hasNext()) {
            RudderEvent event = iterator.next().getValue();
            String uniqueId = DAUMetrics.getUniqueId(event);
            if (uniqueIds.contains(uniqueId)) {
                iterator.remove();
            } else {
                uniqueIds.add(uniqueId);
            }
        }
    }

    public static <W extends Window> UniqueUserEvictor<W> create() {
        return new UniqueUserEvictor<>();
    }
}
