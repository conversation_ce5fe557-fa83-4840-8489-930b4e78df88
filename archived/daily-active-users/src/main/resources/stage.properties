
# Properties for stage environment

#Checkpoints
CHECKPOINTS_STORAGE_LOCATION = s3a://grofers-test-dse-singapore/flink-streams/observability/daily-active-users/checkpoints/

# Events Source
EVENTS_SOURCE_BROKERS = b-2.prod-data-events.771kbr.c4.kafka.ap-southeast-1.amazonaws.com:9092,b-1.prod-data-events.771kbr.c4.kafka.ap-southeast-1.amazonaws.com:9092,b-3.prod-data-events.771kbr.c4.kafka.ap-southeast-1.amazonaws.com:9092
EVENTS_SOURCE_CONSUMER_GROUP_ID = test-flink-observability-daily-active-users-consumer_group-v1
EVENTS_SOURCE_TOPIC = rudder.track.mobile.core_ordering

#Watermark Idleness Configs
IDLENESS_TIME_IN_SECS = 600

# DAU Window Configs
DAU_WINDOW_SIZE_IN_DAYS = 1
MERCHANT_DAU_METRICS_SINK_BROKERS = b-2.testdatacommon.vkkz3t.c5.kafka.ap-southeast-1.amazonaws.com:9092,b-1.testdatacommon.vkkz3t.c5.kafka.ap-southeast-1.amazonaws.com:9092
MERCHANT_DAU_METRICS_SINK_TOPIC = observability.metrics.merchant-daily-active-users-v1

CITY_DAU_METRICS_SINK_BROKERS = b-2.testdatacommon.vkkz3t.c5.kafka.ap-southeast-1.amazonaws.com:9092,b-1.testdatacommon.vkkz3t.c5.kafka.ap-southeast-1.amazonaws.com:9092
CITY_DAU_METRICS_SINK_TOPIC = observability.metrics.city-daily-active-users-v1
