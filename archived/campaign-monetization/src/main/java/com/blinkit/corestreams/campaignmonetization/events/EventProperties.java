package com.blinkit.corestreams.campaignmonetization.events;

import lombok.Getter;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.annotation.JsonProperty;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.annotation.JsonSerialize;

@JsonSerialize
@Getter
@JsonIgnoreProperties(ignoreUnknown = true)
public class EventProperties {

    @JsonProperty("ads_campaign_id")
    String adsCampaignId = "-1";

    @JsonProperty("ads_type")
    String adsType = "";

    @JsonProperty("page_name")
    String pageName = "";
}
