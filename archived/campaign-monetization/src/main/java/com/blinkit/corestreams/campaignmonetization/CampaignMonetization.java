/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.blinkit.corestreams.campaignmonetization;

import static java.time.Duration.ofSeconds;

import com.blinkit.corestreams.campaignmonetization.configs.JobConfig;
import com.blinkit.corestreams.campaignmonetization.configs.JobConfigManager;
import com.blinkit.corestreams.campaignmonetization.events.MonetizationEvent;
import com.blinkit.corestreams.campaignmonetization.serdes.MonetizationEventDeserializer;
import org.apache.flink.api.common.ExecutionConfig;
import org.apache.flink.api.common.eventtime.WatermarkStrategy;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.connector.kafka.source.KafkaSource;
import org.apache.flink.connector.kafka.source.enumerator.initializer.OffsetsInitializer;
import org.apache.flink.streaming.api.CheckpointingMode;
import org.apache.flink.streaming.api.datastream.DataStreamSource;
import org.apache.flink.streaming.api.environment.CheckpointConfig;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.table.api.DataTypes;
import org.apache.flink.table.api.Schema;
import org.apache.flink.table.api.Table;
import org.apache.flink.table.api.bridge.java.StreamTableEnvironment;
import org.apache.kafka.clients.consumer.OffsetResetStrategy;

/**
 * Skeleton for a Flink DataStream Job.
 *
 * <p>For a tutorial how to write a Flink application, check the tutorials and examples on the <a
 * href="https://flink.apache.org">Flink Website</a>.
 *
 * <p>To package your application into a JAR file for execution, run 'mvn clean package' on the
 * command line.
 *
 * <p>If you change the name of the main class (with the public static void main(String[] args))
 * method, change the respective entry in the POM.xml file (simply search for 'mainClass').
 */
public class CampaignMonetization {

    private final DataStreamSource<MonetizationEvent> monetizationEventsSource;

    public CampaignMonetization(DataStreamSource<MonetizationEvent> monetizationEventsSource) {
        this.monetizationEventsSource = monetizationEventsSource;
    }

    public static void main(String[] args) throws Exception {
        // set up the streaming execution environment
        final StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment();

        // Parameters for environment
        ParameterTool params = ParameterTool.fromArgs(args);
        String userEnv = params.getRequired("environment");

        // Set PropertiesPath for userEnv
        JobConfigManager.setJobConfigsPath(userEnv);
        // Get PropertiesPath for userEnv
        String jobPropertiesPath = JobConfigManager.getJobConfigsPath(userEnv);
        // Fetch job config
        JobConfigManager.setJobConfigs(env, jobPropertiesPath);
        JobConfigManager.getJobConfigs(env);

        // Checkpoint Configs
        // start a checkpoint every 240000 ms
        env.enableCheckpointing(240000);
        // set mode to exactly-once (default)
        env.getCheckpointConfig().setCheckpointingMode(CheckpointingMode.EXACTLY_ONCE);
        // make sure 120000 ms of progress happen between checkpoints
        env.getCheckpointConfig().setMinPauseBetweenCheckpoints(240000);
        // checkpoints have to complete within a minute, or are discarded
        env.getCheckpointConfig().setCheckpointTimeout(300000);
        // only two consecutive checkpoint failures are tolerated
        env.getCheckpointConfig().setTolerableCheckpointFailureNumber(2);
        // allow only one checkpoint to be in progress at the same time
        env.getCheckpointConfig().setMaxConcurrentCheckpoints(1);
        // enable externalized checkpoints which are retained after job cancellation
        env.getCheckpointConfig()
                .setExternalizedCheckpointCleanup(
                        CheckpointConfig.ExternalizedCheckpointCleanup.RETAIN_ON_CANCELLATION);
        // enables the experimental unaligned checkpoints
        env.getCheckpointConfig().enableUnalignedCheckpoints();
        // sets the checkpoint storage where checkpoint snapshots will be written
        env.getCheckpointConfig().setCheckpointStorage(JobConfig.CHECKPOINTS_STORAGE_LOCATION);
        // Enable checkpoint compression
        ExecutionConfig executionConfig = new ExecutionConfig();
        executionConfig.setUseSnapshotCompression(true);

        KafkaSource<MonetizationEvent> monetizationEventsSource =
                KafkaSource.<MonetizationEvent>builder()
                        .setBootstrapServers(JobConfig.EVENTS_SOURCE_BROKERS)
                        .setGroupId(JobConfig.EVENTS_SOURCE_CONSUMER_GROUP_ID)
                        .setValueOnlyDeserializer(new MonetizationEventDeserializer())
                        .setStartingOffsets(
                                OffsetsInitializer.committedOffsets(OffsetResetStrategy.LATEST))
                        .setTopics(JobConfig.EVENTS_SOURCE_TOPIC)
                        .setProperty("partition.discovery.interval.ms", "600000")
                        .setProperty("reconnect.backoff.max.ms", "300000")
                        .setProperty("reconnect.backoff.ms", "60000")
                        .build();

        WatermarkStrategy<MonetizationEvent> dauEventsSourceWatermarkStrategy =
                WatermarkStrategy.<MonetizationEvent>forBoundedOutOfOrderness(ofSeconds(60))
                        .withIdleness(ofSeconds(JobConfig.IDLENESS_TIME_IN_SECS))
                        .withTimestampAssigner(
                                (event, timestamp) -> event.getEventTimestamp().toEpochMilli());

        DataStreamSource<MonetizationEvent> impressionEventsStreamSource =
                env.fromSource(
                        monetizationEventsSource,
                        dauEventsSourceWatermarkStrategy,
                        "impressionEventsSource");

        CampaignMonetization campaignMonetization =
                new CampaignMonetization(impressionEventsStreamSource);

        campaignMonetization.execute(env);
    }

    public void execute(StreamExecutionEnvironment env) throws Exception {
        StreamTableEnvironment tableEnv = StreamTableEnvironment.create(env);

        Configuration configuration = tableEnv.getConfig().getConfiguration();
        configuration.setString("table.exec.source.idle-timeout", "60 s");
        configuration.setString("table.exec.state.ttl", "7200 s");

        configuration.setString("table.exec.mini-batch.enabled", "true");
        configuration.setString("table.exec.mini-batch.allow-latency", "1 s");
        configuration.setString("table.exec.mini-batch.size", "5000");

        configuration.setString("table.optimizer.agg-phase-strategy", "TWO_PHASE");
        configuration.setString("table.optimizer.distinct-agg.split.enabled", "true");

        Table monetizationEvents =
                tableEnv.fromDataStream(
                        this.monetizationEventsSource,
                        Schema.newBuilder()
                                .column("messageId", DataTypes.STRING())
                                .column("eventTimestamp", DataTypes.TIMESTAMP_LTZ(3))
                                .column("adsCampaignId", DataTypes.INT())
                                .column("adsType", DataTypes.STRING())
                                .column("eventName", DataTypes.STRING())
                                .column("pageName", DataTypes.STRING())
                                .columnByExpression("rowtime", "eventTimestamp")
                                .watermark("rowtime", "rowtime - INTERVAL '1' MINUTE")
                                .build());

        tableEnv.createTemporaryView("MonetizationEvents", monetizationEvents);

        Table adsCampaginMonetization =
                tableEnv.sqlQuery(
                        "SELECT adsCampaignId as campaign_id, adsType as ads_type, COUNT(DISTINCT(messageId)) as impressions,  "
                                + "    UNIX_TIMESTAMP(DATE_FORMAT(window_start, 'yyyy-MM-dd HH:mm:ss'))*1000 as window_start,  "
                                + "    UNIX_TIMESTAMP(DATE_FORMAT(window_end, 'yyyy-MM-dd HH:mm:ss'))*1000 as window_end  "
                                + "     FROM TABLE(  "
                                + "       CUMULATE(TABLE MonetizationEvents, DESCRIPTOR(rowtime), INTERVAL '1' MINUTES, INTERVAL '30' MINUTES))  "
                                + "     WHERE adsCampaignId IS NOT NULL AND adsCampaignId <> -1 AND adsType IS NOT NULL AND adsType <> '' "
                                + "     AND pageName IS NOT NULL AND pageName = 'feed' "
                                + "     GROUP BY adsCampaignId, adsType, window_start, window_end");

        tableEnv.executeSql(
                "CREATE TABLE IF NOT EXISTS ads_campaign_monetization ("
                        + "    campaign_id INT,"
                        + "    ads_type STRING,"
                        + "    impressions BIGINT NOT NULL,"
                        + "    window_start BIGINT,"
                        + "    window_end BIGINT"
                        + "    )"
                        + "     WITH ("
                        + "    'connector' = 'kafka',"
                        + "    'topic' = '"
                        + JobConfig.AD_MONETIZATION_SINK_TOPIC
                        + "', "
                        + "    'properties.bootstrap.servers' = '"
                        + JobConfig.AD_MONETIZATION_SINK_BROKERS
                        + "', "
                        + "    'format' = 'json'"
                        + "    )");

        adsCampaginMonetization.executeInsert("ads_campaign_monetization").print();

        env.execute("ad-campaign-monetization");
    }
}
