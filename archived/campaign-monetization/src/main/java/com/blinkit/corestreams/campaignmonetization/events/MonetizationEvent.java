package com.blinkit.corestreams.campaignmonetization.events;

import java.time.Instant;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class MonetizationEvent {
    String messageId;
    Instant eventTimestamp;
    String eventName;
    Integer adsCampaignId;
    String adsType;
    String pageName;

    public MonetizationEvent(RudderMonetizationEvent rudderMonetizationEvent) {
        this.messageId = rudderMonetizationEvent.getMessageId();
        this.adsCampaignId =
                Integer.parseInt(rudderMonetizationEvent.getProperties().getAdsCampaignId());
        this.adsType = rudderMonetizationEvent.getProperties().getAdsType();
        this.pageName = rudderMonetizationEvent.getProperties().getPageName();
        // Using receivedAt to reduce delay drop of events
        this.eventTimestamp = rudderMonetizationEvent.getReceivedAt().toInstant();
        this.eventName = rudderMonetizationEvent.getEventName();
    }
}
