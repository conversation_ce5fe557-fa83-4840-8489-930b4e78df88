# Properties for stage environment

#Checkpoints
CHECKPOINTS_STORAGE_LOCATION = s3a://prod-data-flink-states/flink-streams/core-streams/campaign-monetization/checkpoints/

# Events Source
EVENTS_SOURCE_BROKERS = b-2.prod-data-events.771kbr.c4.kafka.ap-southeast-1.amazonaws.com:9092,b-1.prod-data-events.771kbr.c4.kafka.ap-southeast-1.amazonaws.com:9092,b-3.prod-data-events.771kbr.c4.kafka.ap-southeast-1.amazonaws.com:9092
EVENTS_SOURCE_CONSUMER_GROUP_ID = flink-core_streams-campaign_monetization-consumer_group-v1
EVENTS_SOURCE_TOPIC = rudder.impression.mobile.monetization

#Watermark Idleness Configs
IDLENESS_TIME_IN_SECS = 60

# Ads Window Configs
WINDOW_SIZE_IN_MINS = 5
ALLOWED_LATENESS_IN_SECS = 1800
AD_MONETIZATION_SINK_BROKERS = b-1.prod-data-corestr.uynfnv.c4.kafka.ap-southeast-1.amazonaws.com:9092,b-2.prod-data-corestr.uynfnv.c4.kafka.ap-southeast-1.amazonaws.com:9092,b-3.prod-data-corestr.uynfnv.c4.kafka.ap-southeast-1.amazonaws.com:9092
AD_MONETIZATION_SINK_TOPIC = corestreams.ads.campaign-monetization
