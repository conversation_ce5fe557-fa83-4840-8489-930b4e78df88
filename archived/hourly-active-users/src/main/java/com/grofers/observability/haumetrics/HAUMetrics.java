/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.grofers.observability.haumetrics;

import org.apache.flink.api.common.ExecutionConfig;
import org.apache.flink.api.common.JobExecutionResult;
import org.apache.flink.api.common.eventtime.WatermarkStrategy;
import org.apache.flink.api.common.functions.FilterFunction;
import org.apache.flink.api.connector.sink2.Sink;
import org.apache.flink.api.java.functions.KeySelector;
import org.apache.flink.api.java.tuple.Tuple2;
import org.apache.flink.api.java.tuple.Tuple3;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.connector.base.DeliveryGuarantee;
import org.apache.flink.connector.kafka.sink.KafkaSink;
import org.apache.flink.connector.kafka.source.KafkaSource;
import org.apache.flink.connector.kafka.source.enumerator.initializer.OffsetsInitializer;
import org.apache.flink.contrib.streaming.state.EmbeddedRocksDBStateBackend;
import org.apache.flink.streaming.api.CheckpointingMode;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.datastream.DataStreamSource;
import org.apache.flink.streaming.api.environment.CheckpointConfig;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.streaming.api.functions.windowing.ProcessWindowFunction;
import org.apache.flink.streaming.api.windowing.assigners.TumblingEventTimeWindows;
import org.apache.flink.streaming.api.windowing.time.Time;
import org.apache.flink.streaming.api.windowing.triggers.ContinuousEventTimeTrigger;
import org.apache.flink.streaming.api.windowing.windows.TimeWindow;
import org.apache.flink.util.Collector;

import com.grofers.gandalf.events.RudderEvent;
import com.grofers.gandalf.serdes.RudderEventDeserializer;
import com.grofers.observability.haumetrics.configs.JobConfig;
import com.grofers.observability.haumetrics.configs.JobConfigManager;
import com.grofers.observability.haumetrics.datatypes.CityHAUMetric;
import com.grofers.observability.haumetrics.datatypes.MerchantHAUMetric;
import com.grofers.observability.haumetrics.serdes.CityHAUMetricRecordSerializationSchema;
import com.grofers.observability.haumetrics.serdes.MerchantHAUMetricRecordSerializationSchema;
import org.apache.kafka.clients.consumer.OffsetResetStrategy;

import java.util.HashSet;
import java.util.Set;

import static java.time.Duration.ofSeconds;

/** Flink Streaming Job for calculating Hourly Active Users */
public class HAUMetrics {

    private final DataStreamSource<RudderEvent> impressionEventsSource;
    private final Sink<MerchantHAUMetric> merchantHauMetricSink;
    private final Sink<CityHAUMetric> cityHauMetricSink;

    public HAUMetrics(
            DataStreamSource<RudderEvent> impressionEventsSource,
            Sink<MerchantHAUMetric> merchantHauMetricSink,
            Sink<CityHAUMetric> cityHauMetricSink) {
        this.impressionEventsSource = impressionEventsSource;
        this.merchantHauMetricSink = merchantHauMetricSink;
        this.cityHauMetricSink = cityHauMetricSink;
    }

    public static void main(String[] args) throws Exception {
        // set up the streaming execution environment
        final StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment();

        // Parameters for environment
        ParameterTool params = ParameterTool.fromArgs(args);
        String userEnv = params.getRequired("environment");

        // Set PropertiesPath for userEnv
        JobConfigManager.setJobConfigsPath(userEnv);
        // Get PropertiesPath for userEnv
        String jobPropertiesPath = JobConfigManager.getJobConfigsPath(userEnv);
        // Fetch job config
        JobConfigManager.setJobConfigs(env, jobPropertiesPath);
        JobConfigManager.getJobConfigs(env);

        // Checkpoint Configs
        // start a checkpoint every 240000 ms
        env.enableCheckpointing(240000);
        // set mode to exactly-once (default)
        env.getCheckpointConfig().setCheckpointingMode(CheckpointingMode.EXACTLY_ONCE);
        // make sure 120000 ms of progress happen between checkpoints
        env.getCheckpointConfig().setMinPauseBetweenCheckpoints(240000);
        // checkpoints have to complete within a minute, or are discarded
        env.getCheckpointConfig().setCheckpointTimeout(600000);
        // only two consecutive checkpoint failures are tolerated
        env.getCheckpointConfig().setTolerableCheckpointFailureNumber(2);
        // allow only one checkpoint to be in progress at the same time
        env.getCheckpointConfig().setMaxConcurrentCheckpoints(1);
        // enable externalized checkpoints which are retained after job cancellation
        env.getCheckpointConfig()
                .setExternalizedCheckpointCleanup(
                        CheckpointConfig.ExternalizedCheckpointCleanup.RETAIN_ON_CANCELLATION);
        // enables the experimental unaligned checkpoints
        env.getCheckpointConfig().enableUnalignedCheckpoints();
        // sets the checkpoint storage where checkpoint snapshots will be written
        env.getCheckpointConfig().setCheckpointStorage(JobConfig.CHECKPOINTS_STORAGE_LOCATION);
        env.setStateBackend(new EmbeddedRocksDBStateBackend(true));
        env.setParallelism(20);
        // Enable checkpoint compression
        ExecutionConfig executionConfig = new ExecutionConfig();
        executionConfig.setUseSnapshotCompression(true);

        KafkaSource<RudderEvent> impressionEventsSource =
                KafkaSource.<RudderEvent>builder()
                        .setBootstrapServers(JobConfig.EVENTS_SOURCE_BROKERS)
                        .setGroupId(JobConfig.EVENTS_SOURCE_CONSUMER_GROUP_ID)
                        .setClientIdPrefix("hau_metrics-")
                        .setValueOnlyDeserializer(new RudderEventDeserializer())
                        .setStartingOffsets(
                                OffsetsInitializer.committedOffsets(OffsetResetStrategy.LATEST))
                        .setTopics(JobConfig.EVENTS_SOURCE_TOPIC)
                        .setProperty("partition.discovery.interval.ms", "600000")
                        .setProperty("reconnect.backoff.max.ms", "300000")
                        .setProperty("reconnect.backoff.ms", "60000")
                        .build();

        WatermarkStrategy<RudderEvent> hauEventsSourceWatermarkStrategy =
                WatermarkStrategy.<RudderEvent>forBoundedOutOfOrderness(ofSeconds(300))
                        .withIdleness(ofSeconds(JobConfig.IDLENESS_TIME_IN_SECS))
                        .withTimestampAssigner(
                                (event, timestamp) ->
                                        event.getEventTimestamp().toInstant().toEpochMilli());

        DataStreamSource<RudderEvent> impressionEventsStreamSource =
                env.fromSource(
                        impressionEventsSource,
                        hauEventsSourceWatermarkStrategy,
                        "impressionEventsSource");

        KafkaSink<CityHAUMetric> cityHauMetricsKafkaSink =
                KafkaSink.<CityHAUMetric>builder()
                        .setBootstrapServers(JobConfig.CITY_HAU_METRICS_SINK_BROKERS)
                        .setDeliverGuarantee(DeliveryGuarantee.AT_LEAST_ONCE)
                        .setTransactionalIdPrefix("city_hau_metrics-")
                        .setRecordSerializer(
                                new CityHAUMetricRecordSerializationSchema(
                                        JobConfig.CITY_HAU_METRICS_SINK_TOPIC))
                        .build();

        KafkaSink<MerchantHAUMetric> merchantHauMetricsKafkaSink =
                KafkaSink.<MerchantHAUMetric>builder()
                        .setBootstrapServers(JobConfig.MERCHANT_HAU_METRICS_SINK_BROKERS)
                        .setDeliverGuarantee(DeliveryGuarantee.AT_LEAST_ONCE)
                        .setTransactionalIdPrefix("merchant_hau_metrics-")
                        .setRecordSerializer(
                                new MerchantHAUMetricRecordSerializationSchema(
                                        JobConfig.MERCHANT_HAU_METRICS_SINK_TOPIC))
                        .build();

        HAUMetrics hauMetrics =
                new HAUMetrics(
                        impressionEventsStreamSource,
                        merchantHauMetricsKafkaSink,
                        cityHauMetricsKafkaSink);
        hauMetrics.execute(env);
    }

    private FilterFunction<RudderEvent> getFilterEventFunction() {
        return new FilterFunction<>() {
            private final String[] FILTER_EVENTS = {
                "App Launch",
                "Checkout Step Viewed",
                "Order Acknowledged",
                "Cart Viewed",
                "Search Results Viewed",
                "Product Added",
                "Homepage Visit"
            };

            @Override
            public boolean filter(RudderEvent event) throws Exception {
                if (event == null) {
                    return false;
                }
                if (event.getContext().getTraits().getCityName() == null) {
                    return false;
                }
                for (String allowedEvent : FILTER_EVENTS) {
                    if (allowedEvent.equalsIgnoreCase(event.getEventName())) {
                        return true;
                    }
                }
                return false;
            }
        };
    }

    private static String getChannel(String eventChannel) {
        return eventChannel != null ? eventChannel : "BLINKIT";
    }

    public static String getUniqueId(RudderEvent event) {
        String advertisingId = event.getContext().getDevice().getAdvertisingId();
        String deviceId = event.getContext().getDevice().getId();
        if (event.getContext().getOs().getName().equalsIgnoreCase("Android")) {
            return deviceId;
        } else if (event.getContext().getOs().getName().equalsIgnoreCase("iOS")) {
            return advertisingId;
        }
        return deviceId != null ? deviceId : advertisingId != null ? advertisingId : "null";
    }

    public JobExecutionResult execute(StreamExecutionEnvironment environment) throws Exception {

        DataStream<RudderEvent> filteredEvents =
                this.impressionEventsSource
                        .name("impression-event-source")
                        .uid("impression-events-source")
                        .filter(getFilterEventFunction())
                        .uid("filter-null-city-dau-event")
                        .name("filter-null-city-dau-event");

        filteredEvents
                .keyBy(
                        new KeySelector<RudderEvent, Tuple2<String, String>>() {
                            @Override
                            public Tuple2<String, String> getKey(RudderEvent event)
                                    throws Exception {
                                return Tuple2.of(
                                        getChannel(event.getContext().getTraits().getChannel()),
                                        event.getContext().getTraits().getCityName());
                            }
                        })
                .window(TumblingEventTimeWindows.of(Time.hours(1), Time.minutes(-30)))
                .allowedLateness(Time.seconds(JobConfig.IDLENESS_TIME_IN_SECS))
                .trigger(ContinuousEventTimeTrigger.of(Time.seconds(60)))
                .evictor(UniqueUserEvictor.create())
                .process(new CityHAUMetricsProcessFunction())
                .name("city-hourly-active-users-window")
                .uid("city-hourly-active-users-window")
                .sinkTo(cityHauMetricSink)
                .name("city-hourly-active-users-sink")
                .uid("city-hourly-active-users-sink");

        filteredEvents
                .filter(event -> event.getContext().getTraits().getMerchantId() != null)
                .uid("filter-null-merchant-hau-event")
                .name("filter-null-merchant-hau-event")
                .keyBy(
                        new KeySelector<RudderEvent, Tuple3<String, String, Integer>>() {
                            @Override
                            public Tuple3<String, String, Integer> getKey(RudderEvent event)
                                    throws Exception {
                                return Tuple3.of(
                                        getChannel(event.getContext().getTraits().getChannel()),
                                        event.getContext().getTraits().getCityName(),
                                        event.getContext().getTraits().getMerchantId());
                            }
                        })
                .window(TumblingEventTimeWindows.of(Time.hours(1), Time.minutes(-30)))
                .allowedLateness(Time.seconds(JobConfig.IDLENESS_TIME_IN_SECS))
                .trigger(ContinuousEventTimeTrigger.of(Time.seconds(60)))
                .evictor(UniqueUserEvictor.create())
                .process(new MerchantHAUMetricsProcessFunction())
                .name("merchant-hourly-active-users-window")
                .uid("merchant-hourly-active-users-window")
                .sinkTo(merchantHauMetricSink)
                .name("merchant-hourly-active-users-sink")
                .uid("merchant-hourly-active-users-sink");

        return environment.execute("observability.hourly-active-users");
    }

    public static class CityHAUMetricsProcessFunction
            extends ProcessWindowFunction<
                    RudderEvent, CityHAUMetric, Tuple2<String, String>, TimeWindow> {

        @Override
        public void process(
                Tuple2<String, String> key,
                ProcessWindowFunction<
                                        RudderEvent,
                                        CityHAUMetric,
                                        Tuple2<String, String>,
                                        TimeWindow>
                                .Context
                        context,
                Iterable<RudderEvent> iterable,
                Collector<CityHAUMetric> collector) {
            Set<String> deviceIDs = new HashSet<>();
            for (RudderEvent e : iterable) {
                deviceIDs.add(getUniqueId(e));
            }

            collector.collect(
                    new CityHAUMetric(
                            key.f0,
                            key.f1,
                            context.window().getStart(),
                            context.currentWatermark(),
                            deviceIDs.size()));
        }
    }

    public static class MerchantHAUMetricsProcessFunction
            extends ProcessWindowFunction<
                    RudderEvent, MerchantHAUMetric, Tuple3<String, String, Integer>, TimeWindow> {

        @Override
        public void process(
                Tuple3<String, String, Integer> key,
                ProcessWindowFunction<
                                        RudderEvent,
                                        MerchantHAUMetric,
                                        Tuple3<String, String, Integer>,
                                        TimeWindow>
                                .Context
                        context,
                Iterable<RudderEvent> iterable,
                Collector<MerchantHAUMetric> collector) {
            Set<String> deviceIDs = new HashSet<>();
            for (RudderEvent e : iterable) {
                deviceIDs.add(getUniqueId(e));
            }

            collector.collect(
                    new MerchantHAUMetric(
                            key.f0,
                            key.f1,
                            key.f2,
                            context.window().getStart(),
                            context.currentWatermark(),
                            deviceIDs.size()));
        }
    }
}
