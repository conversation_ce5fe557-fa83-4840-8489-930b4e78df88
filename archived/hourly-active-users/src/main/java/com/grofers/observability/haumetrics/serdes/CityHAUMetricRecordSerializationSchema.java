package com.grofers.observability.haumetrics.serdes;

import org.apache.flink.api.common.serialization.SerializationSchema;
import org.apache.flink.connector.kafka.sink.KafkaRecordSerializationSchema;

import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.core.JsonProcessingException;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.ObjectMapper;

import com.grofers.observability.haumetrics.datatypes.CityHAUMetric;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class CityHAUMetricRecordSerializationSchema
        implements KafkaRecordSerializationSchema<CityHAUMetric> {

    private static final long serialVersionUID = 1L;
    private static final Logger LOG =
            LoggerFactory.getLogger(MerchantHAUMetricRecordSerializationSchema.class);
    private static final ObjectMapper objectMapper = new ObjectMapper();
    private final String topic;

    public CityHAUMetricRecordSerializationSchema(String topic) {
        this.topic = topic;
    }

    @Override
    public void open(
            SerializationSchema.InitializationContext context, KafkaSinkContext sinkContext)
            throws Exception {
        KafkaRecordSerializationSchema.super.open(context, sinkContext);
    }

    @Override
    public ProducerRecord<byte[], byte[]> serialize(
            CityHAUMetric metric, KafkaSinkContext kafkaSinkContext, Long aLong) {
        try {
            return new ProducerRecord<>(this.topic, objectMapper.writeValueAsBytes(metric));
        } catch (JsonProcessingException e) {
            LOG.info("Failed to serialize sink message: {}", metric.toString());
            throw new RuntimeException(String.format("Failed to serialize message: %s", metric));
        }
    }
}
