
# Properties for stage environment

#Checkpoints
CHECKPOINTS_STORAGE_LOCATION = s3a://grofers-test-dse-singapore/dau-metrics

# Events Source
EVENTS_SOURCE_BROKERS = b-1.test-dse-common.iosmgv.c4.kafka.ap-southeast-1.amazonaws.com:9092,b-2.test-dse-common.iosmgv.c4.kafka.ap-southeast-1.amazonaws.com:9092,b-3.test-dse-common.iosmgv.c4.kafka.ap-southeast-1.amazonaws.com:9092
EVENTS_SOURCE_CONSUMER_GROUP_ID = test-flink-observability-dau_metrics-consumer_group-v1
EVENTS_SOURCE_TOPIC = rudder.track.mobile.core_ordering

#Watermark Idleness Configs
IDLENESS_TIME_IN_SECS = 600

# DAU Window Configs
DAU_WINDOW_SIZE_IN_DAYS = 1
MERCHANT_DAU_METRICS_SINK_BROKERS = b-1.test-dse-common.iosmgv.c4.kafka.ap-southeast-1.amazonaws.com:9092,b-2.test-dse-common.iosmgv.c4.kafka.ap-southeast-1.amazonaws.com:9092,b-3.test-dse-common.iosmgv.c4.kafka.ap-southeast-1.amazonaws.com:9092
MERCHANT_DAU_METRICS_SINK_TOPIC = observability.metrics.merchant-dau-metrics

CITY_DAU_METRICS_SINK_BROKERS = b-1.test-dse-common.iosmgv.c4.kafka.ap-southeast-1.amazonaws.com:9092,b-2.test-dse-common.iosmgv.c4.kafka.ap-southeast-1.amazonaws.com:9092,b-3.test-dse-common.iosmgv.c4.kafka.ap-southeast-1.amazonaws.com:9092
CITY_DAU_METRICS_SINK_TOPIC = observability.metrics.city-dau-metrics
