package com.grofers.observability.daumetrics;

import org.apache.flink.streaming.api.windowing.evictors.Evictor;
import org.apache.flink.streaming.api.windowing.windows.Window;
import org.apache.flink.streaming.runtime.operators.windowing.TimestampedValue;

import com.grofers.observability.daumetrics.datatypes.events.Event;

import java.util.HashSet;
import java.util.Iterator;
import java.util.Set;

public class UniqueUserEvictor<W extends Window> implements Evictor<Event, W> {
    @Override
    public void evictBefore(
            Iterable<TimestampedValue<Event>> iterable,
            int i,
            W w,
            EvictorContext evictorContext) {}

    @Override
    public void evictAfter(
            Iterable<TimestampedValue<Event>> iterable, int i, W w, EvictorContext evictorContext) {
        this.evict(iterable, evictorContext);
    }

    private void evict(Iterable<TimestampedValue<Event>> elements, EvictorContext ctx) {
        Set<String> uniqueIds = new HashSet<>();
        Iterator<TimestampedValue<Event>> iterator = elements.iterator();
        while (iterator.hasNext()) {
            Event event = iterator.next().getValue();
            String uniqueId =
                    event.accessContext()
                            .accessDevice()
                            .accessUniqueId(event.accessContext().accessOs().accessOsName());
            if (uniqueIds.contains(uniqueId)) {
                iterator.remove();
            } else {
                uniqueIds.add(uniqueId);
            }
        }
    }

    public static <W extends Window> UniqueUserEvictor<W> create() {
        return new UniqueUserEvictor<>();
    }
}
