package com.grofers.observability.daumetrics.datatypes;

public class MerchantDAUMetric {
    private final String city;
    private final Integer merchantId;
    private final Long windowStart;
    private final Long windowEnd;
    private final Integer uniqueDeviceCount;

    public MerchantDAUMetric(
            String city,
            Integer merchantId,
            Long windowStart,
            Long windowEnd,
            Integer uniqueDeviceCount) {
        this.city = city;
        this.merchantId = merchantId;
        this.windowStart = windowStart;
        this.windowEnd = windowEnd;
        this.uniqueDeviceCount = uniqueDeviceCount;
    }

    @Override
    public String toString() {
        return "{"
                + "\"city\":\""
                + city
                + '\"'
                + ", \"merchantId\":\""
                + merchantId
                + '\"'
                + ", \"uniqueDeviceCount\":\""
                + uniqueDeviceCount
                + '\"'
                + ", \"windowStart\":"
                + windowStart
                + ", \"windowEnd\":"
                + windowEnd
                + "}";
    }
}
