/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.grofers.observability.daumetrics;

import org.apache.flink.api.common.ExecutionConfig;
import org.apache.flink.api.common.JobExecutionResult;
import org.apache.flink.api.common.eventtime.WatermarkStrategy;
import org.apache.flink.api.common.functions.FilterFunction;
import org.apache.flink.api.connector.sink.Sink;
import org.apache.flink.api.java.tuple.Tuple2;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.connector.base.DeliveryGuarantee;
import org.apache.flink.connector.kafka.sink.KafkaSink;
import org.apache.flink.connector.kafka.source.KafkaSource;
import org.apache.flink.connector.kafka.source.enumerator.initializer.OffsetsInitializer;
import org.apache.flink.streaming.api.CheckpointingMode;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.datastream.DataStreamSource;
import org.apache.flink.streaming.api.environment.CheckpointConfig;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.streaming.api.functions.windowing.ProcessWindowFunction;
import org.apache.flink.streaming.api.windowing.assigners.TumblingEventTimeWindows;
import org.apache.flink.streaming.api.windowing.time.Time;
import org.apache.flink.streaming.api.windowing.windows.TimeWindow;
import org.apache.flink.util.Collector;

import com.grofers.observability.daumetrics.configs.JobConfig;
import com.grofers.observability.daumetrics.configs.JobConfigManager;
import com.grofers.observability.daumetrics.datatypes.CityDAUMetric;
import com.grofers.observability.daumetrics.datatypes.MerchantDAUMetric;
import com.grofers.observability.daumetrics.datatypes.events.Event;
import com.grofers.observability.daumetrics.serdes.CityDAUMetricRecordSerializationSchema;
import com.grofers.observability.daumetrics.serdes.JsonDeserializationSchema;
import com.grofers.observability.daumetrics.serdes.MerchantDAUMetricRecordSerializationSchema;
import org.apache.kafka.clients.consumer.OffsetResetStrategy;

import java.util.*;

import static java.time.Duration.ofSeconds;

/**
 * Skeleton for a Flink Streaming Job.
 *
 * <p>For a tutorial how to write a Flink streaming application, check the tutorials and examples on
 * the <a href="https://flink.apache.org/docs/stable/">Flink Website</a>.
 *
 * <p>To package your application into a JAR file for execution, run 'mvn clean package' on the
 * command line.
 *
 * <p>If you change the name of the main class (with the public static void main(String[] args))
 * method, change the respective entry in the POM.xml file (simply search for 'mainClass').
 */
public class DAUMetrics {

    private final DataStreamSource<Event> impressionEventsSource;
    private final Sink<MerchantDAUMetric, ?, ?, ?> merchantDauMetricSink;
    private final Sink<CityDAUMetric, ?, ?, ?> cityDauMetricSink;

    public DAUMetrics(
            DataStreamSource<Event> impressionEventsSource,
            Sink<MerchantDAUMetric, ?, ?, ?> merchantDauMetricSink,
            Sink<CityDAUMetric, ?, ?, ?> cityDauMetricSink) {
        this.impressionEventsSource = impressionEventsSource;
        this.merchantDauMetricSink = merchantDauMetricSink;
        this.cityDauMetricSink = cityDauMetricSink;
    }

    public static void main(String[] args) throws Exception {
        // set up the streaming execution environment
        final StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment();

        // Parameters for environment
        ParameterTool params = ParameterTool.fromArgs(args);
        String userEnv = params.getRequired("environment");

        // Set PropertiesPath for userEnv
        JobConfigManager.setJobConfigsPath(userEnv);
        // Get PropertiesPath for userEnv
        String jobPropertiesPath = JobConfigManager.getJobConfigsPath(userEnv);
        // Fetch job config
        JobConfigManager.setJobConfigs(env, jobPropertiesPath);
        JobConfigManager.getJobConfigs(env);

        // Checkpoint Configs
        // start a checkpoint every 240000 ms
        env.enableCheckpointing(240000);
        // set mode to exactly-once (default)
        env.getCheckpointConfig().setCheckpointingMode(CheckpointingMode.EXACTLY_ONCE);
        // make sure 120000 ms of progress happen between checkpoints
        env.getCheckpointConfig().setMinPauseBetweenCheckpoints(240000);
        // checkpoints have to complete within a minute, or are discarded
        env.getCheckpointConfig().setCheckpointTimeout(300000);
        // only two consecutive checkpoint failures are tolerated
        env.getCheckpointConfig().setTolerableCheckpointFailureNumber(2);
        // allow only one checkpoint to be in progress at the same time
        env.getCheckpointConfig().setMaxConcurrentCheckpoints(1);
        // enable externalized checkpoints which are retained after job cancellation
        env.getCheckpointConfig()
                .setExternalizedCheckpointCleanup(
                        CheckpointConfig.ExternalizedCheckpointCleanup.RETAIN_ON_CANCELLATION);
        // enables the experimental unaligned checkpoints
        env.getCheckpointConfig().enableUnalignedCheckpoints();
        // sets the checkpoint storage where checkpoint snapshots will be written
        env.getCheckpointConfig().setCheckpointStorage(JobConfig.CHECKPOINTS_STORAGE_LOCATION);
        // Enable checkpoint compression
        ExecutionConfig executionConfig = new ExecutionConfig();
        executionConfig.setUseSnapshotCompression(true);

        KafkaSource<Event> impressionEventsSource =
                KafkaSource.<Event>builder()
                        .setBootstrapServers(JobConfig.EVENTS_SOURCE_BROKERS)
                        .setGroupId(JobConfig.EVENTS_SOURCE_CONSUMER_GROUP_ID)
                        .setClientIdPrefix("dau_metrics-")
                        .setValueOnlyDeserializer(new JsonDeserializationSchema())
                        .setStartingOffsets(
                                OffsetsInitializer.committedOffsets(OffsetResetStrategy.LATEST))
                        .setTopics(JobConfig.EVENTS_SOURCE_TOPIC)
                        .setProperty("partition.discovery.interval.ms", "600000")
                        .setProperty("reconnect.backoff.max.ms", "300000")
                        .setProperty("reconnect.backoff.ms", "60000")
                        .build();

        WatermarkStrategy<Event> dauEventsSourceWatermarkStrategy =
                WatermarkStrategy.<Event>forBoundedOutOfOrderness(ofSeconds(300))
                        .withIdleness(ofSeconds(JobConfig.IDLENESS_TIME_IN_SECS))
                        .withTimestampAssigner(
                                (event, timestamp) -> event.accessEventTimestampEpochMilli());

        DataStreamSource<Event> impressionEventsStreamSource =
                env.fromSource(
                        impressionEventsSource,
                        dauEventsSourceWatermarkStrategy,
                        "impressionEventsSource");

        KafkaSink<MerchantDAUMetric> merchantDauMetricsKafkaSink =
                KafkaSink.<MerchantDAUMetric>builder()
                        .setBootstrapServers(JobConfig.MERCHANT_DAU_METRICS_SINK_BROKERS)
                        .setDeliverGuarantee(DeliveryGuarantee.AT_LEAST_ONCE)
                        .setTransactionalIdPrefix("merchant_dau_metrics-")
                        .setRecordSerializer(
                                new MerchantDAUMetricRecordSerializationSchema(
                                        JobConfig.MERCHANT_DAU_METRICS_SINK_TOPIC))
                        .build();

        KafkaSink<CityDAUMetric> cityDauMetricsKafkaSink =
                KafkaSink.<CityDAUMetric>builder()
                        .setBootstrapServers(JobConfig.CITY_DAU_METRICS_SINK_BROKERS)
                        .setDeliverGuarantee(DeliveryGuarantee.AT_LEAST_ONCE)
                        .setTransactionalIdPrefix("city_dau_metrics-")
                        .setRecordSerializer(
                                new CityDAUMetricRecordSerializationSchema(
                                        JobConfig.CITY_DAU_METRICS_SINK_TOPIC))
                        .build();

        DAUMetrics dauMetrics =
                new DAUMetrics(
                        impressionEventsStreamSource,
                        merchantDauMetricsKafkaSink,
                        cityDauMetricsKafkaSink);
        dauMetrics.execute(env);
    }

    private FilterFunction<Event> getFilterEventFunction() {
        return new FilterFunction<Event>() {
            private final String[] FILTER_EVENTS = {
                "App Launch",
                "Checkout Step Viewed",
                "Order Acknowledged",
                "Cart Viewed",
                "Search Results Viewed",
                "Product Added",
                "Homepage Visit"
            };

            @Override
            public boolean filter(Event event) throws Exception {
                if (event == null) {
                    return false;
                }
                if (event.accessContext().accessTraits().accessCity() == null) {
                    return false;
                }
                for (String allowedEvent : FILTER_EVENTS) {
                    if (allowedEvent.equalsIgnoreCase(event.accessEventName())) {
                        return true;
                    }
                }
                return false;
            }
        };
    }

    public JobExecutionResult execute(StreamExecutionEnvironment environment) throws Exception {
        DataStream<Event> filteredEvents =
                this.impressionEventsSource
                        .name("impression-event-source")
                        .uid("impression-events-source")
                        .setParallelism(4)
                        .filter(getFilterEventFunction())
                        .uid("filter-null-city-dau-event")
                        .name("filter-null-city-dau-event")
                        .setParallelism(4);

        filteredEvents
                .filter(event -> event.accessContext().accessTraits().accessMerchantId() != null)
                .setParallelism(4)
                .uid("filter-null-merchant-dau-event")
                .name("filter-null-merchant-dau-event")
                .keyBy(Event::accessDAUKey)
                .window(
                        TumblingEventTimeWindows.of(
                                Time.days(JobConfig.DAU_WINDOW_SIZE_IN_DAYS), Time.minutes(-330)))
                .allowedLateness(Time.seconds(JobConfig.IDLENESS_TIME_IN_SECS))
                .trigger(EventPeriodicTimeTrigger.create(60000L))
                .evictor(UniqueUserEvictor.create())
                .process(new MerchantDAUMetricsProcessFunction())
                .setParallelism(4)
                .name("merchant-dau-metrics-window")
                .uid("merchant-dau-metrics-window")
                .sinkTo(merchantDauMetricSink)
                .setParallelism(4)
                .name("merchant-dau-metrics-sink")
                .uid("merchant-dau-metrics-sink");

        filteredEvents
                .keyBy(e -> e.accessContext().accessTraits().accessCity())
                .window(
                        TumblingEventTimeWindows.of(
                                Time.days(JobConfig.DAU_WINDOW_SIZE_IN_DAYS), Time.minutes(-330)))
                .allowedLateness(Time.seconds(JobConfig.IDLENESS_TIME_IN_SECS))
                .trigger(EventPeriodicTimeTrigger.create(60000L))
                .evictor(UniqueUserEvictor.create())
                .process(new CityDAUMetricsProcessFunction())
                .setParallelism(4)
                .name("city-dau-metrics-window")
                .uid("city-dau-metrics-window")
                .sinkTo(cityDauMetricSink)
                .setParallelism(4)
                .name("city-dau-metrics-sink")
                .uid("city-dau-metrics-sink");

        return environment.execute("observability.dau-metrics");
    }

    public static class MerchantDAUMetricsProcessFunction
            extends ProcessWindowFunction<
                    Event, MerchantDAUMetric, Tuple2<String, Integer>, TimeWindow> {

        @Override
        public void process(
                Tuple2<String, Integer> key,
                ProcessWindowFunction<Event, MerchantDAUMetric, Tuple2<String, Integer>, TimeWindow>
                                .Context
                        context,
                Iterable<Event> iterable,
                Collector<MerchantDAUMetric> collector) {
            Set<String> deviceIDs = new HashSet<>();
            for (Event e : iterable) {
                deviceIDs.add(
                        e.accessContext()
                                .accessDevice()
                                .accessUniqueId(e.accessContext().accessOs().accessOsName()));
            }

            collector.collect(
                    new MerchantDAUMetric(
                            key.f0,
                            key.f1,
                            context.window().getStart(),
                            context.currentWatermark(),
                            deviceIDs.size()));
        }
    }

    public static class CityDAUMetricsProcessFunction
            extends ProcessWindowFunction<Event, CityDAUMetric, String, TimeWindow> {

        @Override
        public void process(
                String key,
                ProcessWindowFunction<Event, CityDAUMetric, String, TimeWindow>.Context context,
                Iterable<Event> iterable,
                Collector<CityDAUMetric> collector) {
            Set<String> deviceIDs = new HashSet<>();
            for (Event e : iterable) {
                deviceIDs.add(
                        e.accessContext()
                                .accessDevice()
                                .accessUniqueId(e.accessContext().accessOs().accessOsName()));
            }

            collector.collect(
                    new CityDAUMetric(
                            key,
                            context.window().getStart(),
                            context.currentWatermark(),
                            deviceIDs.size()));
        }
    }
}
