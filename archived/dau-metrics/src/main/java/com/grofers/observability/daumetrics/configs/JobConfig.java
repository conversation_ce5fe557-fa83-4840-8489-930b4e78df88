package com.grofers.observability.daumetrics.configs;

public final class JobConfig {
    public static String CHECKPOINTS_STORAGE_LOCATION;
    public static String EVENTS_SOURCE_BROKERS;
    public static String EVENTS_SOURCE_CONSUMER_GROUP_ID;
    public static String[] EVENTS_SOURCE_TOPIC;
    public static int IDLENESS_TIME_IN_SECS;
    public static int DAU_WINDOW_SIZE_IN_DAYS;
    public static String MERCHANT_DAU_METRICS_SINK_BROKERS;
    public static String MERCHANT_DAU_METRICS_SINK_TOPIC;
    public static String CITY_DAU_METRICS_SINK_BROKERS;
    public static String CITY_DAU_METRICS_SINK_TOPIC;

    public JobConfig(
            String CHECKPOINTS_STORAGE_LOCATION,
            String EVENTS_SOURCE_BROKERS,
            String EVENTS_SOURCE_CONSUMER_GROUP_ID,
            String EVENTS_SOURCE_TOPIC,
            int IDLENESS_TIME_IN_SECS,
            int DAU_WINDOW_SIZE_IN_DAYS,
            String MERCHANT_DAU_METRICS_SINK_BROKERS,
            String MERCHANT_DAU_METRICS_SINK_TOPIC,
            String CITY_DAU_METRICS_SINK_BROKERS,
            String CITY_DAU_METRICS_SINK_TOPIC) {

        JobConfig.CHECKPOINTS_STORAGE_LOCATION = CHECKPOINTS_STORAGE_LOCATION;
        JobConfig.EVENTS_SOURCE_BROKERS = EVENTS_SOURCE_BROKERS;
        JobConfig.EVENTS_SOURCE_CONSUMER_GROUP_ID = EVENTS_SOURCE_CONSUMER_GROUP_ID;
        JobConfig.EVENTS_SOURCE_TOPIC = EVENTS_SOURCE_TOPIC.split(",");
        JobConfig.IDLENESS_TIME_IN_SECS = IDLENESS_TIME_IN_SECS;
        JobConfig.DAU_WINDOW_SIZE_IN_DAYS = DAU_WINDOW_SIZE_IN_DAYS;
        JobConfig.MERCHANT_DAU_METRICS_SINK_BROKERS = MERCHANT_DAU_METRICS_SINK_BROKERS;
        JobConfig.MERCHANT_DAU_METRICS_SINK_TOPIC = MERCHANT_DAU_METRICS_SINK_TOPIC;
        JobConfig.CITY_DAU_METRICS_SINK_BROKERS = CITY_DAU_METRICS_SINK_BROKERS;
        JobConfig.CITY_DAU_METRICS_SINK_TOPIC = CITY_DAU_METRICS_SINK_TOPIC;
    }
}
