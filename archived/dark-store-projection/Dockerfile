FROM public.ecr.aws/zomato/flink:1.14.4-scala_2.12

RUN wget -P $FLINK_HOME/lib/ https://repo1.maven.org/maven2/org/apache/kafka/kafka-clients/2.8.0/kafka-clients-2.8.0.jar; \
    wget -P $FLINK_HOME/lib/ https://repo.maven.apache.org/maven2/org/apache/flink/flink-s3-fs-hadoop/1.13.0/flink-s3-fs-hadoop-1.13.0.jar; \
    wget -P $FLINK_HOME/lib/ https://repo.maven.apache.org/maven2/org/apache/flink/flink-sql-avro-confluent-registry/1.13.0/flink-sql-avro-confluent-registry-1.13.0.jar; \
    wget -P $FLINK_HOME/lib/ https://repo1.maven.org/maven2/org/apache/flink/flink-connector-jdbc_2.11/1.13.2/flink-connector-jdbc_2.11-1.13.2.jar;

RUN mkdir -p $FLINK_HOME/usrlib

COPY src/main/resources /opt/flink/src/main/resources

COPY target/dark-store-projection-1.0-SNAPSHOT.jar /opt/flink/usrlib/dark-store-projection.jar
