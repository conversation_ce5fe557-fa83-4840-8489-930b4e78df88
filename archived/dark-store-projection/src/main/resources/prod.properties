#Configs for production environment

#Checkpoints
CHECKPOINTS_STORAGE_LOCATION = s3a://prod-data-flink-states/core-streams/dark-store-projection/checkpoints/

#Kafka
EVENTS_SOURCE_BROKERS = b-1.proddatasrp.wv3mhc.c5.kafka.ap-southeast-1.amazonaws.com:9092,b-2.proddatasrp.wv3mhc.c5.kafka.ap-southeast-1.amazonaws.com:9092,b-3.proddatasrp.wv3mhc.c5.kafka.ap-southeast-1.amazonaws.com:9092
EVENTS_SOURCE_CONSUMER_GROUP_ID = corestreams.logistics.dark-store-projection.group
EVENTS_SOURCE_TOPIC = postgres.logistics_server.public.logistics_dark_store_projection
EVENTS_SOURCE_CONFLUENT_SCHEMA_REGISTRY = http://source-replication-common-schema-registry-external-data.prod-sgp-k8s.grofer.io:8081
DARKSTORE_PROJECTION_SINK_BROKERS = b-1.prod-data-corestr.uynfnv.c4.kafka.ap-southeast-1.amazonaws.com:9092,b-2.prod-data-corestr.uynfnv.c4.kafka.ap-southeast-1.amazonaws.com:9092,b-3.prod-data-corestr.uynfnv.c4.kafka.ap-southeast-1.amazonaws.com:9092
DARKSTORE_PROJECTION_SINK_TOPIC = corestreams.logistics.dark-store-projection
