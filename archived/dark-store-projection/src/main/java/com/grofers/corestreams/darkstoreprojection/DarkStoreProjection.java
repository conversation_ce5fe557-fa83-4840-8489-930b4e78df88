/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.grofers.corestreams.darkstoreprojection;

import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.CheckpointingMode;
import org.apache.flink.streaming.api.environment.ExecutionCheckpointingOptions;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.table.api.Table;
import org.apache.flink.table.api.bridge.java.StreamTableEnvironment;

import com.grofers.corestreams.darkstoreprojection.configs.JobConfigs;
import com.grofers.corestreams.darkstoreprojection.configs.JobConfigsManager;
import com.grofers.gandalf.core.udfs.IsoDateTimeStringToTimestamp3;

import static com.grofers.corestreams.darkstoreprojection.configs.JobConfigs.*;

public class DarkStoreProjection {

    public static void main(String[] args) throws Exception {

        // set up the streaming execution environment
        final StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment();
        // set up the table execution environment
        final StreamTableEnvironment tableEnv = StreamTableEnvironment.create(env);
        // Parameters for environ
        ParameterTool params = ParameterTool.fromArgs(args);
        String userEnv = params.getRequired("environment");

        // Set PropertiesPath for userEnv
        JobConfigsManager.setJobConfigsPath(userEnv);
        // Get PropertiesPath for userEnv
        String jobPropertiesPath = JobConfigsManager.getJobConfigsPath(userEnv);
        // Set Global Job Properties
        JobConfigsManager.setJobConfigs(env, jobPropertiesPath);
        // Get Global Job Properties
        JobConfigs globalJobConfigs = JobConfigsManager.getJobConfigs(env);

        env.enableCheckpointing(600000);
        env.getCheckpointConfig().setCheckpointingMode(CheckpointingMode.EXACTLY_ONCE);
        env.getCheckpointConfig().setMinPauseBetweenCheckpoints(300000);
        env.getCheckpointConfig().setCheckpointTimeout(120000);
        env.getCheckpointConfig().setTolerableCheckpointFailureNumber(3);
        env.getCheckpointConfig().setMaxConcurrentCheckpoints(1);
        env.getCheckpointConfig().enableUnalignedCheckpoints();
        env.getCheckpointConfig().setCheckpointStorage(CHECKPOINTS_STORAGE_LOCATION);
        // enable checkpointing with finished tasks
        Configuration config = new Configuration();
        config.set(ExecutionCheckpointingOptions.ENABLE_CHECKPOINTS_AFTER_TASKS_FINISH, true);
        env.configure(config);

        tableEnv.createTemporarySystemFunction(
                "stringToTimestamp", IsoDateTimeStringToTimestamp3.class);
        Configuration configuration = tableEnv.getConfig().getConfiguration();
        configuration.setString("table.exec.state.ttl", "3600s");

        String createSqlDarkStoreProjection =
                ("CREATE TABLE IF NOT EXISTS logistics_dark_store_projection ( "
                        + "id BIGINT, "
                        + "install_ts STRING, "
                        + "update_ts STRING, "
                        + "created_by_id INT, "
                        + "updated_by_id INT, "
                        + "order_id STRING, "
                        + "dark_store_id STRING, "
                        + "trip_id STRING, "
                        + "delivery_fe_id STRING, "
                        + "delivery_latitude DOUBLE, "
                        + "delivery_longitude DOUBLE, "
                        + "order_weight DOUBLE, "
                        + "current_delivery_start STRING, "
                        + "current_delivery_end STRING, "
                        + "reschedule_count INT, "
                        + "allocation_complete_timestamp STRING, "
                        + "billing_timestamp STRING, "
                        + "trip_start_timestamp STRING, "
                        + "delivery_timestamp STRING, "
                        + "total_item_count INT, "
                        + "total_procured_item_count INT, "
                        + "total_delivered_item_count INT, "
                        + "fill_rate DOUBLE, "
                        + "on_time BOOLEAN, "
                        + "before_time BOOLEAN, "
                        + "delay_time BOOLEAN, "
                        + "current_state STRING, "
                        + "state_log STRING, "
                        + "tags STRING, "
                        + "billed_polybags_count INT, "
                        + "drop_zone STRING, "
                        + "picker_fe_id STRING, "
                        + "scanned_polybags_count INT, "
                        + "delivery_current_state STRING, "
                        + "picking_current_state STRING, "
                        + "delivery_allocation_complete_timestamp STRING, "
                        + "picking_allocation_complete_timestamp STRING, "
                        + "latest_shipment_id STRING, "
                        + "serviceability STRING, "
                        + "flink_update_ts AS stringToTimestamp(update_ts), "
                        + "WATERMARK FOR flink_update_ts AS flink_update_ts, "
                        + "PRIMARY KEY(dark_store_id) NOT ENFORCED "
                        + " ) "
                        + "WITH ( "
                        + "'connector' = 'kafka', "
                        + "'topic' = '"
                        + EVENTS_SOURCE_TOPIC
                        + "', "
                        + "'properties.bootstrap.servers' = '"
                        + EVENTS_SOURCE_BROKERS
                        + "', "
                        + "'properties.group.id' = '"
                        + EVENTS_SOURCE_CONSUMER_GROUP_ID
                        + "', "
                        + "'format' = 'debezium-avro-confluent', "
                        + "'debezium-avro-confluent.schema-registry.url' = '"
                        + EVENTS_SOURCE_CONFLUENT_SCHEMA_REGISTRY
                        + "', "
                        + "'scan.startup.mode' = 'group-offsets' "
                        + ")");
        tableEnv.executeSql(createSqlDarkStoreProjection);

        String createSqlLogisticsNode =
                ("CREATE TABLE IF NOT EXISTS logistics_node ( "
                        + "id INT, "
                        + "install_ts TIMESTAMP(3), "
                        + "update_ts TIMESTAMP(3), "
                        + "external_id STRING, "
                        + "node_address_id INT, "
                        + "display_name STRING, "
                        + "store_type STRING, "
                        + "flink_update_ts AS update_ts, "
                        + "WATERMARK FOR flink_update_ts AS flink_update_ts, "
                        + "PRIMARY KEY(external_id) NOT ENFORCED "
                        + " ) "
                        + "WITH ( "
                        + "'connector' = 'jdbc', "
                        + "'url' = '"
                        + "jdbc:postgresql://"
                        + System.getenv().getOrDefault("LOGISTICS_HOSTNAME", "localhost")
                        + ":"
                        + System.getenv().getOrDefault("LOGISTICS_PORT", "5432")
                        + "/"
                        + System.getenv().getOrDefault("LOGISTICS_DB", "default")
                        + "?user="
                        + System.getenv().getOrDefault("LOGISTICS_DB_USER", "postgres")
                        + "&password="
                        + System.getenv().getOrDefault("LOGISTICS_DB_PASSWORD", "password")
                        + "', "
                        + "'table-name' = 'logistics_node', "
                        + "'lookup.cache.max-rows' = '10000', "
                        + "'lookup.cache.ttl' = '86400s' "
                        + ")");
        tableEnv.executeSql(createSqlLogisticsNode);

        String createSqlLogisticsNodeAddress =
                ("CREATE TABLE IF NOT EXISTS logistics_node_address ( "
                        + "id INT, "
                        + "install_ts TIMESTAMP(3), "
                        + "update_ts TIMESTAMP(3), "
                        + "city STRING "
                        + " ) "
                        + "WITH ( "
                        + "'connector' = 'jdbc', "
                        + "'url' = '"
                        + "jdbc:postgresql://"
                        + System.getenv().getOrDefault("LOGISTICS_HOSTNAME", "localhost")
                        + ":"
                        + System.getenv().getOrDefault("LOGISTICS_PORT", "5432")
                        + "/"
                        + System.getenv().getOrDefault("LOGISTICS_DB", "default")
                        + "?user="
                        + System.getenv().getOrDefault("LOGISTICS_DB_USER", "postgres")
                        + "&password="
                        + System.getenv().getOrDefault("LOGISTICS_DB_PASSWORD", "password")
                        + "', "
                        + "'table-name' = 'logistics_node_address', "
                        + "'lookup.cache.max-rows' = '10000', "
                        + "'lookup.cache.ttl' = '86400s' "
                        + ")");
        tableEnv.executeSql(createSqlLogisticsNodeAddress);

        tableEnv.executeSql(
                "create table if not exists darkstoreProjectionKafkaSink ( "
                        + "id BIGINT PRIMARY KEY, "
                        + "install_ts BIGINT, "
                        + "update_ts BIGINT, "
                        + "created_by_id INT, "
                        + "updated_by_id INT, "
                        + "order_id STRING, "
                        + "dark_store_id STRING, "
                        + "trip_id STRING, "
                        + "delivery_fe_id STRING, "
                        + "delivery_latitude DOUBLE, "
                        + "delivery_longitude DOUBLE, "
                        + "order_weight DOUBLE, "
                        + "current_delivery_start BIGINT, "
                        + "current_delivery_end BIGINT, "
                        + "reschedule_count INT, "
                        + "allocation_complete_timestamp BIGINT, "
                        + "billing_timestamp BIGINT, "
                        + "trip_start_timestamp BIGINT, "
                        + "delivery_timestamp BIGINT, "
                        + "total_item_count INT, "
                        + "total_delivered_item_count INT, "
                        + "total_procured_item_count INT, "
                        + "fill_rate DOUBLE, "
                        + "on_time BOOLEAN, "
                        + "before_time BOOLEAN, "
                        + "delay_time BOOLEAN, "
                        + "current_state STRING, "
                        + "state_log STRING, "
                        + "tags STRING, "
                        + "billed_polybags_count INT, "
                        + "drop_zone STRING, "
                        + "picker_fe_id STRING, "
                        + "scanned_polybags_count INT, "
                        + "delivery_current_state STRING, "
                        + "picking_current_state STRING, "
                        + "delivery_allocation_complete_timestamp BIGINT, "
                        + "picking_allocation_complete_timestamp BIGINT, "
                        + "latest_shipment_id STRING, "
                        + "serviceability STRING, "
                        + "city_name STRING, "
                        + "dark_store_name STRING, "
                        + "store_type STRING "
                        + " ) "
                        + " WITH ( "
                        + "'connector' = 'upsert-kafka', "
                        + "'topic' = '"
                        + DARKSTORE_PROJECTION_SINK_TOPIC
                        + "', "
                        + "'properties.bootstrap.servers' = '"
                        + DARKSTORE_PROJECTION_SINK_BROKERS
                        + "', "
                        + "'key.format' = 'json', "
                        + "'value.format' = 'json' "
                        + ")");

        // create an output Table
        Table enrichedDarkStoreProjection =
                tableEnv.sqlQuery(
                        "SELECT "
                                + "ldp.id, "
                                + "UNIX_TIMESTAMP(DATE_FORMAT(stringToTimestamp(ldp.install_ts), 'yyyy-MM-dd HH:mm:ss')) AS install_ts, "
                                + "UNIX_TIMESTAMP(DATE_FORMAT(stringToTimestamp(ldp.update_ts), 'yyyy-MM-dd HH:mm:ss')) AS update_ts, "
                                + "ldp.created_by_id, "
                                + "ldp.updated_by_id, "
                                + "ldp.order_id, "
                                + "ldp.dark_store_id, "
                                + "ldp.trip_id, "
                                + "ldp.delivery_fe_id, "
                                + "ldp.delivery_latitude, "
                                + "ldp.delivery_longitude, "
                                + "ldp.order_weight, "
                                + "UNIX_TIMESTAMP(DATE_FORMAT(stringToTimestamp(ldp.current_delivery_start), 'yyyy-MM-dd HH:mm:ss')) AS current_delivery_start, "
                                + "UNIX_TIMESTAMP(DATE_FORMAT(stringToTimestamp(ldp.current_delivery_end), 'yyyy-MM-dd HH:mm:ss')) AS current_delivery_end, "
                                + "ldp.reschedule_count, "
                                + "UNIX_TIMESTAMP(DATE_FORMAT(stringToTimestamp(ldp.allocation_complete_timestamp), 'yyyy-MM-dd HH:mm:ss')) AS allocation_complete_timestamp, "
                                + "UNIX_TIMESTAMP(DATE_FORMAT(stringToTimestamp(ldp.billing_timestamp), 'yyyy-MM-dd HH:mm:ss')) AS billing_timestamp, "
                                + "UNIX_TIMESTAMP(DATE_FORMAT(stringToTimestamp(ldp.trip_start_timestamp), 'yyyy-MM-dd HH:mm:ss')) AS trip_start_timestamp, "
                                + "UNIX_TIMESTAMP(DATE_FORMAT(stringToTimestamp(ldp.delivery_timestamp), 'yyyy-MM-dd HH:mm:ss')) AS delivery_timestamp, "
                                + "ldp.total_item_count, "
                                + "ldp.total_delivered_item_count, "
                                + "ldp.total_procured_item_count, "
                                + "ldp.fill_rate, "
                                + "ldp.on_time, "
                                + "ldp.before_time, "
                                + "ldp.delay_time, "
                                + "ldp.current_state, "
                                + "ldp.state_log, "
                                + "ldp.tags, "
                                + "ldp.billed_polybags_count, "
                                + "ldp.drop_zone, "
                                + "ldp.picker_fe_id, "
                                + "ldp.scanned_polybags_count, "
                                + "ldp.delivery_current_state, "
                                + "ldp.picking_current_state, "
                                + "UNIX_TIMESTAMP(DATE_FORMAT(stringToTimestamp(ldp.delivery_allocation_complete_timestamp), 'yyyy-MM-dd HH:mm:ss')) AS delivery_allocation_complete_timestamp, "
                                + "UNIX_TIMESTAMP(DATE_FORMAT(stringToTimestamp(ldp.picking_allocation_complete_timestamp), 'yyyy-MM-dd HH:mm:ss')) AS picking_allocation_complete_timestamp, "
                                + "ldp.latest_shipment_id, "
                                + "ldp.serviceability, "
                                + "lna.city AS city_name, "
                                + "lnode.display_name AS dark_store_name, "
                                + "lnode.store_type "
                                + "FROM logistics_dark_store_projection AS ldp "
                                + "LEFT JOIN logistics_node FOR SYSTEM_TIME AS OF ldp.flink_update_ts AS lnode "
                                + "ON ldp.dark_store_id = lnode.external_id "
                                + "LEFT JOIN logistics_node_address lna "
                                + "ON lnode.node_address_id = lna.id ");

        enrichedDarkStoreProjection.executeInsert("darkstoreProjectionKafkaSink").print();
        env.execute();
    }
}
