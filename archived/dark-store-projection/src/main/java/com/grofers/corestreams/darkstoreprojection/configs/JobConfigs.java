package com.grofers.corestreams.darkstoreprojection.configs;

public final class JobConfigs {
    public static String CHECKPOINTS_STORAGE_LOCATION;
    public static String EVENTS_SOURCE_BROKERS;
    public static String EVENTS_SOURCE_CONSUMER_GROUP_ID;
    public static String EVENTS_SOURCE_TOPIC;
    public static String EVENTS_SOURCE_CONFLUENT_SCHEMA_REGISTRY;
    public static String DARKSTORE_PROJECTION_SINK_BROKERS;
    public static String DARKSTORE_PROJECTION_SINK_TOPIC;

    public JobConfigs(
            String CHECKPOINTS_STORAGE_LOCATION,
            String EVENTS_SOURCE_BROKERS,
            String EVENTS_SOURCE_CONSUMER_GROUP_ID,
            String EVENTS_SOURCE_TOPIC,
            String EVENTS_SOURCE_CONFLUENT_SCHEMA_REGISTRY,
            String DARKSTORE_PROJECTION_SINK_BROKERS,
            String DARKSTORE_PROJECTION_SINK_TOPIC) {

        this.CHECKPOINTS_STORAGE_LOCATION = CHECKPOINTS_STORAGE_LOCATION;
        this.EVENTS_SOURCE_BROKERS = EVENTS_SOURCE_BROKERS;
        this.EVENTS_SOURCE_CONSUMER_GROUP_ID = EVENTS_SOURCE_CONSUMER_GROUP_ID;
        this.EVENTS_SOURCE_TOPIC = EVENTS_SOURCE_TOPIC;
        this.EVENTS_SOURCE_CONFLUENT_SCHEMA_REGISTRY = EVENTS_SOURCE_CONFLUENT_SCHEMA_REGISTRY;
        this.DARKSTORE_PROJECTION_SINK_BROKERS = DARKSTORE_PROJECTION_SINK_BROKERS;
        this.DARKSTORE_PROJECTION_SINK_TOPIC = DARKSTORE_PROJECTION_SINK_TOPIC;
    }
}
