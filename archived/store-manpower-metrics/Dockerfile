FROM public.ecr.aws/zomato/flink:1.13.0-scala_2.12

RUN wget -P $FLINK_HOME/lib/ https://repo.maven.apache.org/maven2/org/apache/flink/flink-json/1.13.2/flink-json-1.13.2.jar; \
    wget -P $FLINK_HOME/lib/ https://repo1.maven.org/maven2/com/fasterxml/jackson/core/jackson-databind/2.12.3/jackson-databind-2.12.3.jar; \
    wget -P $FLINK_HOME/lib/ https://repo1.maven.org/maven2/com/fasterxml/jackson/core/jackson-core/2.12.3/jackson-core-2.12.3.jar; \
    wget -P $FLINK_HOME/lib/ https://repo1.maven.org/maven2/com/fasterxml/jackson/core/jackson-annotations/2.12.3/jackson-annotations-2.12.3.jar; \
    wget -P $FLINK_HOME/lib/ https://repo1.maven.org/maven2/org/apache/kafka/kafka-clients/2.8.0/kafka-clients-2.8.0.jar; \
    wget -P $FLINK_HOME/lib/ https://repo.maven.apache.org/maven2/org/apache/flink/flink-s3-fs-hadoop/1.13.0/flink-s3-fs-hadoop-1.13.0.jar;

RUN mkdir -p $FLINK_HOME/usrlib

COPY src/main/resources /opt/flink/src/main/resources

COPY target/store-manpower-metrics-1.0-SNAPSHOT.jar /opt/flink/usrlib/store-manpower-metrics.jar
