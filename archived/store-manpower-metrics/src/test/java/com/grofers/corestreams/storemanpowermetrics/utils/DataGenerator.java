package com.grofers.corestreams.storemanpowermetrics.utils;

import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.JsonNode;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.node.ObjectNode;

import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

public class DataGenerator {

    public static ObjectNode storeDisruptionStateEvent(
            Long storeID,
            String storeName,
            Integer batchSize,
            Float disruptionIndexDelivery,
            Float disruptionIndexPicking,
            Long availableFieldExecutives,
            Long availablePickers,
            Long busyFieldExecutives,
            Long busyPickers,
            Long fieldExecutivesOrderBacklog,
            Long pickerOrderBacklog,
            Instant eventTimestamp) {
        /*
        storeDisruptionStateEvent Skeleton
        "value":{
          "store_id":123,
          "field_executives_order_backlog": 0,
          "busy_field_executives": 23,
          "available_field_executives": 14,
          "disruption_index_picking": 0,
          "modified_ts": "2021-10-26T12:00:09.130798Z",
          "busy_pickers": 4,
          "available_pickers": 0,
          "disruption_index_delivery": 0,
          "pickers_order_backlog": 0
         }
         */
        final ObjectMapper mapper = new ObjectMapper();
        final ObjectNode root = mapper.createObjectNode();
        ObjectNode value = root.putObject("value");
        value.set("store_id", mapper.convertValue(storeID, JsonNode.class));
        value.set("store_name", mapper.convertValue(storeName, JsonNode.class));
        value.set("batch_size", mapper.convertValue(batchSize, JsonNode.class));
        value.set(
                "field_executives_order_backlog",
                mapper.convertValue(fieldExecutivesOrderBacklog, JsonNode.class));
        value.set(
                "busy_field_executives", mapper.convertValue(busyFieldExecutives, JsonNode.class));
        value.set(
                "available_field_executives",
                mapper.convertValue(availableFieldExecutives, JsonNode.class));
        value.set(
                "disruption_index_picking",
                mapper.convertValue(disruptionIndexPicking, JsonNode.class));
        value.set(
                "modified_ts",
                mapper.convertValue(
                        Instant.ofEpochMilli(eventTimestamp.toEpochMilli()).toString(),
                        JsonNode.class));
        value.set("busy_pickers", mapper.convertValue(busyPickers, JsonNode.class));
        value.set("available_pickers", mapper.convertValue(availablePickers, JsonNode.class));
        value.set(
                "disruption_index_delivery",
                mapper.convertValue(disruptionIndexDelivery, JsonNode.class));
        value.set("pickers_order_backlog", mapper.convertValue(pickerOrderBacklog, JsonNode.class));

        return root;
    }

    public static List<ObjectNode> getStoreDisruptionStateEventList() throws InterruptedException {
        List<ObjectNode> storeDisruptionStateEventList = new ArrayList<>();
        Instant midnight = Instant.now().truncatedTo(ChronoUnit.DAYS);
        ObjectNode m1 =
                DataGenerator.storeDisruptionStateEvent(
                        123L, "123", 1, 0f, 0f, 14L, 0L, 23L, 4L, 0L, 0L, midnight.plusSeconds(12));
        ObjectNode m2 =
                DataGenerator.storeDisruptionStateEvent(
                        123L,
                        "123",
                        1,
                        0.567f,
                        0.234f,
                        14L,
                        0L,
                        23L,
                        4L,
                        0L,
                        0L,
                        midnight.plusSeconds(24));
        ObjectNode m3 =
                DataGenerator.storeDisruptionStateEvent(
                        123L,
                        "123",
                        1,
                        5f,
                        0.789f,
                        13L,
                        4L,
                        23L,
                        4L,
                        0L,
                        0L,
                        midnight.plusSeconds(36));
        ObjectNode m4 =
                DataGenerator.storeDisruptionStateEvent(
                        456L,
                        "456",
                        1,
                        0f,
                        0.789f,
                        10L,
                        10L,
                        5L,
                        7L,
                        5L,
                        3L,
                        midnight.plusSeconds(48));
        ObjectNode m5 =
                DataGenerator.storeDisruptionStateEvent(
                        789L,
                        "789",
                        1,
                        0.348f,
                        0f,
                        10L,
                        10L,
                        5L,
                        7L,
                        5L,
                        3L,
                        midnight.plusSeconds(60));
        ObjectNode m6 =
                DataGenerator.storeDisruptionStateEvent(
                        111L,
                        "111",
                        1,
                        2.2f,
                        1.2f,
                        10L,
                        10L,
                        5L,
                        7L,
                        5L,
                        3L,
                        midnight.plusSeconds(72));

        ObjectNode m7 =
                DataGenerator.storeDisruptionStateEvent(
                        111L, "111", 1, 1f, 1f, 10L, 10L, 5L, 7L, 5L, 3L, midnight.plusSeconds(84));
        ObjectNode m8 =
                DataGenerator.storeDisruptionStateEvent(
                        111L, "111", 1, 1f, 1f, 10L, 10L, 5L, 7L, 5L, 3L, midnight.plusSeconds(96));

        storeDisruptionStateEventList.addAll(Arrays.asList(m1, m2, m3, m4, m5, m6, m7, m8));
        return storeDisruptionStateEventList;
    }
}
