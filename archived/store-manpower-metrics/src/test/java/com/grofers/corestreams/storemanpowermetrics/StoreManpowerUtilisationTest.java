package com.grofers.corestreams.storemanpowermetrics;

import org.apache.flink.runtime.testutils.MiniClusterResourceConfiguration;
import org.apache.flink.test.util.MiniClusterWithClientResource;

import com.grofers.corestreams.storemanpowermetrics.flows.StoreManpowerMetricsIntegrationTest;
import com.grofers.corestreams.storemanpowermetrics.jobconfigs.StoreManpowerMetricsConfigsTest;
import com.grofers.corestreams.storemanpowermetrics.jobconfigs.StoreManpowerUtilisationEnvironmentTest;
import org.junit.BeforeClass;
import org.junit.runner.RunWith;
import org.junit.runners.Suite;

// Test Suite
@RunWith(Suite.class)
@Suite.SuiteClasses({
    StoreManpowerMetricsConfigsTest.class, // JobConfigs Test
    StoreManpowerUtilisationEnvironmentTest.class, // Environment test
    StoreManpowerMetricsIntegrationTest.class, // Integration test
})
public class StoreManpowerUtilisationTest {
    @BeforeClass
    public static void setUpFlinkCluster() {
        MiniClusterWithClientResource flinkTestCluster =
                new MiniClusterWithClientResource(
                        new MiniClusterResourceConfiguration.Builder()
                                .setNumberSlotsPerTaskManager(2)
                                .setNumberTaskManagers(1)
                                .build());
    }
}
