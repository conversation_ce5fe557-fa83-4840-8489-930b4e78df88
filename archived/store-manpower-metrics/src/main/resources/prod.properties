
# Properties for prod environment

#Checkpoints
CHECKPOINTS_STORAGE_LOCATION = s3a://prod-data-flink-states/core-streams/store-manpower-metrics/checkpoints/

# Events Source
EVENTS_SOURCE_BROKERS = b-1.prod-data-events.771kbr.c4.kafka.ap-southeast-1.amazonaws.com:9092,b-2.prod-data-events.771kbr.c4.kafka.ap-southeast-1.amazonaws.com:9092,b-3.prod-data-events.771kbr.c4.kafka.ap-southeast-1.amazonaws.com:9092
EVENTS_SOURCE_CONSUMER_GROUP_ID = corestreams.serviceability.store-manpower-metrics.group
EVENTS_SOURCE_TOPIC = serviceability.store.manpower.state

# Kafka Sink
STORE_MANPOWER_METRICS_SINK_BROKERS = b-1.prod-data-corestr.uynfnv.c4.kafka.ap-southeast-1.amazonaws.com:9092,b-2.prod-data-corestr.uynfnv.c4.kafka.ap-southeast-1.amazonaws.com:9092,b-3.prod-data-corestr.uynfnv.c4.kafka.ap-southeast-1.amazonaws.com:9092
STORE_MANPOWER_METRICS_SINK_TOPIC = corestreams.serviceability.storemanpowermetrics

#Watermark Idleness Configs
IDLENESS_TIME_IN_SECS = 10

# Window Configs
SLIDING_WINDOW_SIZE_IN_MINS = 10
SLIDING_WINDOW_SLIDE_IN_SECS = 10

#Disrupted Store Rules
DISRUPTION_INDEX_DELIVERY = 1f
DISRUPTION_INDEX_PICKING = 2f
