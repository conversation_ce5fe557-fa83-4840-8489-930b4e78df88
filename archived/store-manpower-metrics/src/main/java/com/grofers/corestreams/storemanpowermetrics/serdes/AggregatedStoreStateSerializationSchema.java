package com.grofers.corestreams.storemanpowermetrics.serdes;

import org.apache.flink.api.common.serialization.SerializationSchema;
import org.apache.flink.streaming.connectors.kafka.KafkaSerializationSchema;

import com.grofers.corestreams.storemanpowermetrics.datatypes.AggregatedStoreManpowerMetric;
import org.apache.kafka.clients.producer.ProducerRecord;

import javax.annotation.Nullable;

import java.nio.charset.StandardCharsets;

public class AggregatedStoreStateSerializationSchema
        implements KafkaSerializationSchema<AggregatedStoreManpowerMetric> {

    private static final long serialVersionUID = -1L;
    private final String topic;

    public AggregatedStoreStateSerializationSchema(String topic) {
        this.topic = topic;
    }

    @Override
    public void open(SerializationSchema.InitializationContext context) throws Exception {
        KafkaSerializationSchema.super.open(context);
    }

    @Override
    public ProducerRecord<byte[], byte[]> serialize(
            AggregatedStoreManpowerMetric aggregateStoreManpowerMetric, @Nullable Long aLong) {
        return new ProducerRecord<>(
                topic,
                aggregateStoreManpowerMetric
                        .getStoreID()
                        .toString()
                        .getBytes(StandardCharsets.UTF_8),
                aggregateStoreManpowerMetric.toString().getBytes(StandardCharsets.UTF_8));
    }
}
