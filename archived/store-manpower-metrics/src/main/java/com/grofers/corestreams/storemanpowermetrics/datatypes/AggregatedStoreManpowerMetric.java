package com.grofers.corestreams.storemanpowermetrics.datatypes;

import java.text.DecimalFormat;

public class AggregatedStoreManpowerMetric {

    private final Long storeID;
    private final String storeName;
    private final Boolean isStoreDisrupted;
    private final Integer currentBatchSize;
    private final Float averageDisruptionIndexDelivery;
    private final Float averageDisruptionIndexPicking;
    private final Float averageAvailableFieldExecutives;
    private final Float averageAvailablePickers;
    private final Float averageBusyFieldExecutives;
    private final Float averageBusyPickers;
    private final Float averageFieldExecutivesOrderBacklog;
    private final Float averagePickerOrderBacklog;
    private final Long eventTimestamp;
    private final Long eventDayEpochIST;

    // To round off the averages
    DecimalFormat decimalFormat = new DecimalFormat("#.##");

    public AggregatedStoreManpowerMetric(
            Long storeID,
            String storeName,
            Boolean isStoreDisrupted,
            Integer currentBatchSize,
            Float averageDisruptionIndexDelivery,
            Float averageDisruptionIndexPicking,
            Float averageAvailableFieldExecutives,
            Float averageAvailablePickers,
            Float averageBusyFieldExecutives,
            Float averageBusyPickers,
            Float averageFieldExecutivesOrderBacklog,
            Float averagePickerOrderBacklog,
            Long eventTimestamp,
            Long eventDayEpochIST) {
        this.storeID = storeID;
        this.storeName = storeName;
        this.isStoreDisrupted = isStoreDisrupted;
        this.currentBatchSize = currentBatchSize;
        this.averageDisruptionIndexDelivery = averageDisruptionIndexDelivery;
        this.averageDisruptionIndexPicking = averageDisruptionIndexPicking;
        this.averageAvailableFieldExecutives = averageAvailableFieldExecutives;
        this.averageAvailablePickers = averageAvailablePickers;
        this.averageBusyFieldExecutives = averageBusyFieldExecutives;
        this.averageBusyPickers = averageBusyPickers;
        this.averageFieldExecutivesOrderBacklog = averageFieldExecutivesOrderBacklog;
        this.averagePickerOrderBacklog = averagePickerOrderBacklog;
        this.eventTimestamp = eventTimestamp;
        this.eventDayEpochIST = eventDayEpochIST;
    }

    public Long getStoreID() {
        return storeID;
    }

    public Float getAverageDisruptionIndexDelivery() {
        return averageDisruptionIndexDelivery;
    }

    public Float getAverageDisruptionIndexPicking() {
        return averageDisruptionIndexPicking;
    }

    public Long getEventTimestamp() {
        return eventTimestamp;
    }

    public Boolean getIsStoreDisrupted() {
        return isStoreDisrupted;
    }

    public Integer getCurrentBatchSize() {
        return currentBatchSize;
    }

    public Float getAverageAvailableFieldExecutives() {
        return averageAvailableFieldExecutives;
    }

    public Float getAverageAvailablePickers() {
        return averageAvailablePickers;
    }

    public Float getAverageBusyFieldExecutives() {
        return averageBusyFieldExecutives;
    }

    public Float getAverageBusyPickers() {
        return averageBusyPickers;
    }

    public Float getAverageFieldExecutivesOrderBacklog() {
        return averageFieldExecutivesOrderBacklog;
    }

    public Float getAveragePickerOrderBacklog() {
        return averagePickerOrderBacklog;
    }

    @Override
    public String toString() {
        return "{"
                + "\"storeID\": "
                + storeID
                + ", \"storeName\": \""
                + storeName
                + "\""
                + ", \"isStoreDisrupted\": "
                + isStoreDisrupted
                + ", \"currentBatchSize\": "
                + currentBatchSize
                + ", \"averageDisruptionIndexDelivery\": "
                + decimalFormat.format(averageDisruptionIndexDelivery)
                + ", \"averageDisruptionIndexPicking\": "
                + decimalFormat.format(averageDisruptionIndexPicking)
                + ", \"averageAvailableFieldExecutives\": "
                + decimalFormat.format(averageAvailableFieldExecutives)
                + ", \"averageAvailablePickers\": "
                + decimalFormat.format(averageAvailablePickers)
                + ", \"averageBusyFieldExecutives\": "
                + decimalFormat.format(averageBusyFieldExecutives)
                + ", \"averageBusyPickers\": "
                + decimalFormat.format(averageBusyPickers)
                + ", \"averageFieldExecutivesOrderBacklog\": "
                + decimalFormat.format(averageFieldExecutivesOrderBacklog)
                + ", \"averagePickerOrderBacklog\": "
                + decimalFormat.format(averagePickerOrderBacklog)
                + ", \"eventTimestamp\": "
                + eventTimestamp
                + ", \"eventDayEpochIST\": "
                + eventDayEpochIST
                + '}';
    }
}
