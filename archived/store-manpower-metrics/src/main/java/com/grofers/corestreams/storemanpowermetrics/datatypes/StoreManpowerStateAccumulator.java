package com.grofers.corestreams.storemanpowermetrics.datatypes;

import org.apache.flink.api.common.accumulators.AverageAccumulator;

public class StoreManpowerStateAccumulator extends AverageAccumulator {
    public Long storeID;
    public Integer noOfEvents;
    public String storeName;
    public Integer currentBatchSize;
    public Float disruptionIndicesDelivery;
    public Float disruptionIndicesPicking;
    public Float sumAvailableFieldExecutives;
    public Float sumAvailablePickers;
    public Float sumBusyFieldExecutives;
    public Float sumBusyPickers;
    public Float sumFieldExecutivesOrderBacklog;
    public Float sumPickerOrderBacklog;
    public Long eventTimestamp;

    public StoreManpowerStateAccumulator(
            Long storeID,
            Integer noOfEvents,
            String storeName,
            Integer currentBatchSize,
            Float disruptionIndicesDelivery,
            Float disruptionIndicesPicking,
            Float sumAvailableFieldExecutives,
            Float sumAvailablePickers,
            Float sumBusyFieldExecutives,
            Float sumBusyPickers,
            Float sumFieldExecutivesOrderBacklog,
            Float sumPickerOrderBacklog,
            Long eventTimestamp) {
        this.storeID = storeID;
        this.noOfEvents = noOfEvents;
        this.storeName = storeName;
        this.currentBatchSize = currentBatchSize;
        this.disruptionIndicesDelivery = disruptionIndicesDelivery;
        this.disruptionIndicesPicking = disruptionIndicesPicking;
        this.sumAvailableFieldExecutives = sumAvailableFieldExecutives;
        this.sumAvailablePickers = sumAvailablePickers;
        this.sumBusyFieldExecutives = sumBusyFieldExecutives;
        this.sumBusyPickers = sumBusyPickers;
        this.sumFieldExecutivesOrderBacklog = sumFieldExecutivesOrderBacklog;
        this.sumPickerOrderBacklog = sumPickerOrderBacklog;
        this.eventTimestamp = eventTimestamp;
    }
}
