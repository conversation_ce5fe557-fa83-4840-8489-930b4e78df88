package com.grofers.corestreams.storemanpowermetrics;

import org.apache.flink.api.common.ExecutionConfig;
import org.apache.flink.api.common.JobExecutionResult;
import org.apache.flink.api.common.eventtime.WatermarkStrategy;
import org.apache.flink.api.common.functions.AggregateFunction;
import org.apache.flink.api.common.functions.MapFunction;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.connector.kafka.source.KafkaSource;
import org.apache.flink.connector.kafka.source.enumerator.initializer.OffsetsInitializer;
import org.apache.flink.streaming.api.CheckpointingMode;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.datastream.DataStreamSource;
import org.apache.flink.streaming.api.datastream.KeyedStream;
import org.apache.flink.streaming.api.datastream.WindowedStream;
import org.apache.flink.streaming.api.environment.CheckpointConfig;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.streaming.api.functions.sink.SinkFunction;
import org.apache.flink.streaming.api.windowing.assigners.SlidingEventTimeWindows;
import org.apache.flink.streaming.api.windowing.time.Time;
import org.apache.flink.streaming.api.windowing.windows.TimeWindow;
import org.apache.flink.streaming.connectors.kafka.FlinkKafkaProducer;

import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.node.ObjectNode;

import com.grofers.corestreams.storemanpowermetrics.configs.JobConfigs;
import com.grofers.corestreams.storemanpowermetrics.configs.JobConfigsManager;
import com.grofers.corestreams.storemanpowermetrics.datatypes.*;
import com.grofers.corestreams.storemanpowermetrics.serdes.AggregatedStoreStateSerializationSchema;
import com.grofers.corestreams.storemanpowermetrics.serdes.JSONValueDeserializationSchema;
import org.apache.kafka.clients.consumer.OffsetResetStrategy;

import java.time.Duration;
import java.time.Instant;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.util.*;

import static com.grofers.corestreams.storemanpowermetrics.configs.JobConfigs.*;
import static java.time.Duration.ofSeconds;

/**
 * Flink Streaming Job for manpower metrics at store.
 *
 * <p>For more information around use case refer <a
 * href="https://grofers.atlassian.net/wiki/spaces/DATA/pages/3410329753/2+-+Last+Mile+-+Store+Operations+Tracking">Inventory
 * Doc</a>.
 *
 * <p>To package your application into a JAR file for execution, run 'mvn clean package' on the
 * command line.
 *
 * <p>If you change the name of the job (with the public static void main(String[] args)) method,
 * change the respective entry in the POM.xml file (simply search for 'mainClass').
 */
public class StoreManpowerMetrics {
    // Source Object
    private final DataStreamSource<ObjectNode> storeManpowerStateDataStreamSource;
    // Sink Object(s)
    private final SinkFunction<AggregatedStoreManpowerMetric>
            aggregatedStoreManpowerMetricsSinkFunction;
    // Constructor
    public StoreManpowerMetrics(
            DataStreamSource<ObjectNode> storeManpowerStateDataStreamSource,
            SinkFunction<AggregatedStoreManpowerMetric>
                    aggregatedStoreManpowerMetricsSinkFunction) {
        this.storeManpowerStateDataStreamSource = storeManpowerStateDataStreamSource;
        this.aggregatedStoreManpowerMetricsSinkFunction =
                aggregatedStoreManpowerMetricsSinkFunction;
    }

    public static void checkIfNull(String name, Object value) {
        if (!Objects.nonNull(value))
            throw new IllegalArgumentException(String.format("%s cannot be Null", name));
    }

    /**
     * Function to parse the Kafka JSON payload into Flink's Keyed Stream
     *
     * @return KeyedStream<StoreManpowerState,Long>
     */
    public static KeyedStream<StoreManpowerState, Long> parseKafkaPayload(
            DataStreamSource<ObjectNode> kafkaDataStreamSource) {
        KeyedStream<StoreManpowerState, Long> storeManpowerStateLongKeyedStream =
                kafkaDataStreamSource
                        .name("store-state-event-stream")
                        .uid("store-state-event-stream")
                        .map(
                                (MapFunction<ObjectNode, StoreManpowerState>)
                                        jsonNodes ->
                                                new StoreManpowerState(
                                                        jsonNodes
                                                                .get("value")
                                                                .get("store_id")
                                                                .asLong(),
                                                        jsonNodes.get("value").has("store_name")
                                                                ? jsonNodes
                                                                        .get("value")
                                                                        .get("store_name")
                                                                        .asText()
                                                                : "null",
                                                        jsonNodes
                                                                .get("value")
                                                                .get("batch_size")
                                                                .asInt(),
                                                        Float.parseFloat(
                                                                jsonNodes
                                                                        .get("value")
                                                                        .get(
                                                                                "disruption_index_delivery")
                                                                        .asText()),
                                                        Float.parseFloat(
                                                                jsonNodes
                                                                        .get("value")
                                                                        .get(
                                                                                "disruption_index_picking")
                                                                        .asText()),
                                                        jsonNodes
                                                                .get("value")
                                                                .get("available_field_executives")
                                                                .asInt(),
                                                        jsonNodes
                                                                .get("value")
                                                                .get("available_pickers")
                                                                .asInt(),
                                                        jsonNodes
                                                                .get("value")
                                                                .get("busy_field_executives")
                                                                .asInt(),
                                                        jsonNodes
                                                                .get("value")
                                                                .get("busy_pickers")
                                                                .asInt(),
                                                        jsonNodes
                                                                .get("value")
                                                                .get(
                                                                        "field_executives_order_backlog")
                                                                .asInt(),
                                                        jsonNodes
                                                                .get("value")
                                                                .get("pickers_order_backlog")
                                                                .asInt(),
                                                        Instant.parse(
                                                                        jsonNodes
                                                                                .get("value")
                                                                                .get("modified_ts")
                                                                                .asText())
                                                                .toEpochMilli()))
                        .uid("store-state-event-map")
                        .name("store-state-event-map")
                        .keyBy(StoreManpowerState::getStoreID);
        return storeManpowerStateLongKeyedStream;
    }

    /**
     * Function to convert a keyedStream to a windowedStream
     *
     * @param storeManpowerStateLongKeyedStream
     * @return WindowedStream<StoreManpowerState,Long, TimeWindow>
     */
    public static WindowedStream<StoreManpowerState, Long, TimeWindow> executeWindowOperator(
            KeyedStream<StoreManpowerState, Long> storeManpowerStateLongKeyedStream) {
        return storeManpowerStateLongKeyedStream
                .window(
                        SlidingEventTimeWindows.of(
                                Time.minutes(SLIDING_WINDOW_SIZE_IN_MINS),
                                Time.seconds(SLIDING_WINDOW_SLIDE_IN_SECS)))
                // Fires processing of window for events having timestamp >= currentWatermark-10sec
                .allowedLateness(Time.seconds(10));
    }

    /**
     * Function to execute a custom Accumulator to find the average metric values
     *
     * @param storeManpowerStateWindowedStream
     * @return DataStream<AggregatedStoreManpowerMetric>
     */
    public DataStream<AggregatedStoreManpowerMetric> executeAggregateOperator(
            WindowedStream<StoreManpowerState, Long, TimeWindow> storeManpowerStateWindowedStream) {
        // Get flink's global params as a map
        Map<String, String> globalJobConfigsMap =
                storeManpowerStateWindowedStream
                        .getExecutionEnvironment()
                        .getConfig()
                        .getGlobalJobParameters()
                        .toMap();
        // Get disruption indices
        float DISRUPTION_INDEX_DELIVERY =
                Float.parseFloat(globalJobConfigsMap.get("DISRUPTION_INDEX_DELIVERY"));
        float DISRUPTION_INDEX_PICKING =
                Float.parseFloat(globalJobConfigsMap.get("DISRUPTION_INDEX_PICKING"));

        DataStream<AggregatedStoreManpowerMetric> aggregatedStoreManpowerMetricsDataStream =
                storeManpowerStateWindowedStream
                        .aggregate(
                                new AggregateStoreManpowerMetricsAccumulator(
                                        DISRUPTION_INDEX_DELIVERY,
                                        DISRUPTION_INDEX_PICKING)) // Average of all metrics
                        .name("aggregate-store-manpower-sliding-window")
                        .uid("aggregate-store-manpower-sliding-window");

        return aggregatedStoreManpowerMetricsDataStream;
    }

    /**
     * Sends all the store-related metrics to a Kafka sink
     *
     * @param env StreamExecutionEnvironment
     * @return {JobExecutionResult}
     * @throws Exception which occurs during job execution.
     */
    public JobExecutionResult execute(StreamExecutionEnvironment env) throws Exception {
        // Parse Kafka Payload to a Keyed Stream
        KeyedStream<StoreManpowerState, Long> storeManpowerStateKeyedStream =
                parseKafkaPayload(this.storeManpowerStateDataStreamSource);
        // Execute Window Operator
        WindowedStream<StoreManpowerState, Long, TimeWindow> storeManpowerStateWindowedStream =
                executeWindowOperator(storeManpowerStateKeyedStream);
        // Aggregate using Flink's Accumulator
        DataStream<AggregatedStoreManpowerMetric> aggregateStoreManpowerMetricsDataStream =
                executeAggregateOperator(storeManpowerStateWindowedStream);
        // Sink to store StoreDisruptionStates
        aggregateStoreManpowerMetricsDataStream
                .addSink(this.aggregatedStoreManpowerMetricsSinkFunction)
                .name("store-aggregated-state-kafka-sink")
                .uid("store-aggregated-state-kafka-sink");

        // execute program
        return env.execute("serviceability.store-manpower-metrics");
    }

    public static void main(String[] args) throws Exception {
        // set up the streaming execution environment
        final StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment();

        // Parameters for environ
        ParameterTool params = ParameterTool.fromArgs(args);
        String userEnv = params.getRequired("environment");

        // Set PropertiesPath for userEnv
        JobConfigsManager.setJobConfigsPath(userEnv);
        // Get PropertiesPath for userEnv
        String jobPropertiesPath = JobConfigsManager.getJobConfigsPath(userEnv);
        // Set Global Job Properties
        JobConfigsManager.setJobConfigs(env, jobPropertiesPath);
        // Get Global Job Properties
        JobConfigs globalJobConfigs = JobConfigsManager.getJobConfigs(env);

        // Checkpoint Configs
        // start a checkpoint every 300000 ms
        env.enableCheckpointing(300000);
        // set mode to exactly-once (default)
        env.getCheckpointConfig().setCheckpointingMode(CheckpointingMode.EXACTLY_ONCE);
        // make sure 300000 ms of progress happen between checkpoints
        env.getCheckpointConfig().setMinPauseBetweenCheckpoints(300000);
        // checkpoints have to complete within two minutes, or are discarded
        env.getCheckpointConfig().setCheckpointTimeout(120000);
        // only two consecutive checkpoint failures are tolerated
        env.getCheckpointConfig().setTolerableCheckpointFailureNumber(2);
        // allow only one checkpoint to be in progress at the same time
        env.getCheckpointConfig().setMaxConcurrentCheckpoints(1);
        // enable externalized checkpoints which are retained after job cancellation
        env.getCheckpointConfig()
                .enableExternalizedCheckpoints(
                        CheckpointConfig.ExternalizedCheckpointCleanup.RETAIN_ON_CANCELLATION);
        // enables the experimental unaligned checkpoints
        env.getCheckpointConfig().enableUnalignedCheckpoints();
        // sets the checkpoint storage where checkpoint snapshots will be written
        env.getCheckpointConfig().setCheckpointStorage(CHECKPOINTS_STORAGE_LOCATION);

        ExecutionConfig executionConfig = new ExecutionConfig();
        executionConfig.setUseSnapshotCompression(true);

        KafkaSource<ObjectNode> storeManpowerStateEventSource =
                KafkaSource.<ObjectNode>builder()
                        .setBootstrapServers(EVENTS_SOURCE_BROKERS)
                        .setGroupId(EVENTS_SOURCE_CONSUMER_GROUP_ID)
                        .setClientIdPrefix("manpower-metrics")
                        .setValueOnlyDeserializer(new JSONValueDeserializationSchema())
                        .setStartingOffsets(
                                OffsetsInitializer.committedOffsets(OffsetResetStrategy.EARLIEST))
                        .setTopics(EVENTS_SOURCE_TOPIC)
                        .setProperty("partition.discovery.interval.ms", "600000")
                        .build();

        WatermarkStrategy<ObjectNode> storeManpowerStateWatermarkStrategy =
                WatermarkStrategy.<ObjectNode>forBoundedOutOfOrderness(
                                Duration.ofSeconds(10)) // Marks events with timestamp >=
                        // currentWatermark-10sec as late
                        .withIdleness(ofSeconds(IDLENESS_TIME_IN_SECS))
                        .withTimestampAssigner(
                                (event, timestamp) ->
                                        Instant.parse(
                                                        event.get("value")
                                                                .get("modified_ts")
                                                                .asText())
                                                .toEpochMilli());

        DataStreamSource<ObjectNode> storeManpowerStateDataStreamSource =
                env.fromSource(
                        storeManpowerStateEventSource,
                        storeManpowerStateWatermarkStrategy,
                        "storeDisruptionStates");

        Properties properties = new Properties();
        properties.setProperty("bootstrap.servers", STORE_MANPOWER_METRICS_SINK_BROKERS);
        // Set timeout to deal with Semantic.EXACTLY_ONCE semantics
        properties.setProperty("transaction.timeout.ms", "900000");

        // Kafka Sink to store all store states
        FlinkKafkaProducer<AggregatedStoreManpowerMetric>
                aggregatedStoreDisruptionStateFlinkKafkaProducer =
                        new FlinkKafkaProducer<>(
                                STORE_MANPOWER_METRICS_SINK_TOPIC, // target topic
                                new AggregatedStoreStateSerializationSchema(
                                        STORE_MANPOWER_METRICS_SINK_TOPIC), // serializations
                                properties, // producer config
                                FlinkKafkaProducer.Semantic.EXACTLY_ONCE);

        StoreManpowerMetrics storeManpowerMetrics =
                new StoreManpowerMetrics(
                        storeManpowerStateDataStreamSource,
                        aggregatedStoreDisruptionStateFlinkKafkaProducer);

        storeManpowerMetrics.execute(env);
    }

    /**
     * Accumulator class for store-id-disruption-indices
     *
     * <p>Each store id receives multiple disruption indices. This class helps to return the average
     * of those indices per store-id
     */
    public static class AggregateStoreManpowerMetricsAccumulator
            implements AggregateFunction<
                    StoreManpowerState,
                    StoreManpowerStateAccumulator,
                    AggregatedStoreManpowerMetric> {
        private final float DISRUPTION_INDEX_DELIVERY;
        private final float DISRUPTION_INDEX_PICKING;

        public AggregateStoreManpowerMetricsAccumulator(
                float DISRUPTION_INDEX_DELIVERY, float DISRUPTION_INDEX_PICKING) {
            this.DISRUPTION_INDEX_DELIVERY = DISRUPTION_INDEX_DELIVERY;
            this.DISRUPTION_INDEX_PICKING = DISRUPTION_INDEX_PICKING;
        }

        @Override
        public StoreManpowerStateAccumulator createAccumulator() {
            return new StoreManpowerStateAccumulator(
                    0L, 0, null, 0, 0f, 0f, 0f, 0f, 0f, 0f, 0f, 0f, Instant.now().toEpochMilli());
        }

        @Override
        public StoreManpowerStateAccumulator add(
                StoreManpowerState storeDisruptionState,
                StoreManpowerStateAccumulator storeDisruptionStateAccumulator) {
            storeDisruptionStateAccumulator.storeID = storeDisruptionState.getStoreID();
            storeDisruptionStateAccumulator.noOfEvents += 1;
            storeDisruptionStateAccumulator.storeName = storeDisruptionState.getStoreName();
            storeDisruptionStateAccumulator.currentBatchSize = storeDisruptionState.getBatchSize();
            storeDisruptionStateAccumulator.disruptionIndicesDelivery +=
                    storeDisruptionState.getDisruptionIndexDelivery();
            storeDisruptionStateAccumulator.disruptionIndicesPicking +=
                    storeDisruptionState.getDisruptionIndexPicking();
            storeDisruptionStateAccumulator.sumAvailableFieldExecutives +=
                    storeDisruptionState.getAvailableFieldExecutives();
            storeDisruptionStateAccumulator.sumAvailablePickers +=
                    storeDisruptionState.getAvailablePickers();
            storeDisruptionStateAccumulator.sumBusyFieldExecutives +=
                    storeDisruptionState.getBusyFieldExecutives();
            storeDisruptionStateAccumulator.sumBusyPickers += storeDisruptionState.getBusyPickers();
            storeDisruptionStateAccumulator.sumFieldExecutivesOrderBacklog +=
                    storeDisruptionState.getFieldExecutivesOrderBacklog();
            storeDisruptionStateAccumulator.sumPickerOrderBacklog +=
                    storeDisruptionState.getPickerOrderBacklog();
            storeDisruptionStateAccumulator.eventTimestamp =
                    storeDisruptionState.getEventTimestamp();

            return storeDisruptionStateAccumulator;
        }

        @Override
        public AggregatedStoreManpowerMetric getResult(
                StoreManpowerStateAccumulator storeDisruptionStateAccumulator) {

            float avgDisruptionDeliveryIndex =
                    (storeDisruptionStateAccumulator.disruptionIndicesDelivery
                            / storeDisruptionStateAccumulator.noOfEvents);
            float avgDisruptionPickingIndex =
                    (storeDisruptionStateAccumulator.disruptionIndicesPicking
                            / storeDisruptionStateAccumulator.noOfEvents);
            try {
                checkIfNull("DISRUPTION_INDEX_DELIVERY", this.DISRUPTION_INDEX_DELIVERY);
                checkIfNull("DISRUPTION_INDEX_PICKING", this.DISRUPTION_INDEX_PICKING);
            } catch (Exception e) {
                e.printStackTrace();
            }
            return new AggregatedStoreManpowerMetric(
                    storeDisruptionStateAccumulator.storeID,
                    storeDisruptionStateAccumulator.storeName,
                    Float.compare(avgDisruptionDeliveryIndex, this.DISRUPTION_INDEX_DELIVERY) > 0
                            || Float.compare(
                                            avgDisruptionPickingIndex,
                                            this.DISRUPTION_INDEX_PICKING)
                                    > 0,
                    storeDisruptionStateAccumulator.currentBatchSize,
                    avgDisruptionDeliveryIndex,
                    avgDisruptionPickingIndex,
                    (storeDisruptionStateAccumulator.sumAvailableFieldExecutives
                            / storeDisruptionStateAccumulator.noOfEvents),
                    (storeDisruptionStateAccumulator.sumAvailablePickers
                            / storeDisruptionStateAccumulator.noOfEvents),
                    (storeDisruptionStateAccumulator.sumBusyFieldExecutives
                            / storeDisruptionStateAccumulator.noOfEvents),
                    (storeDisruptionStateAccumulator.sumBusyPickers
                            / storeDisruptionStateAccumulator.noOfEvents),
                    (storeDisruptionStateAccumulator.sumFieldExecutivesOrderBacklog
                            / storeDisruptionStateAccumulator.noOfEvents),
                    (storeDisruptionStateAccumulator.sumPickerOrderBacklog
                            / storeDisruptionStateAccumulator.noOfEvents),
                    storeDisruptionStateAccumulator.eventTimestamp,
                    Instant.ofEpochMilli(storeDisruptionStateAccumulator.eventTimestamp)
                            .atZone(ZoneId.of("Asia/Kolkata"))
                            .truncatedTo(ChronoUnit.DAYS)
                            .toInstant()
                            .toEpochMilli());
        }

        @Override
        public StoreManpowerStateAccumulator merge(
                StoreManpowerStateAccumulator a1, StoreManpowerStateAccumulator a2) {
            a1.storeID = a2.storeID;
            a1.noOfEvents += a2.noOfEvents;
            a1.storeName = a2.storeName;
            a1.currentBatchSize = a2.currentBatchSize;
            a1.disruptionIndicesDelivery += a2.disruptionIndicesDelivery;
            a1.disruptionIndicesPicking += a2.disruptionIndicesPicking;
            a1.sumAvailableFieldExecutives += a2.sumAvailableFieldExecutives;
            a1.sumAvailablePickers += a2.sumAvailablePickers;
            a1.sumBusyFieldExecutives += a2.sumBusyFieldExecutives;
            a1.sumBusyPickers += a2.sumBusyPickers;
            a1.sumFieldExecutivesOrderBacklog += a2.sumFieldExecutivesOrderBacklog;
            a1.sumPickerOrderBacklog += a2.sumPickerOrderBacklog;
            a1.eventTimestamp = a2.eventTimestamp;
            return a1;
        }
    }
}
