package com.grofers.observability.productfunnelmetrics;

import org.apache.flink.api.common.eventtime.WatermarkStrategy;
import org.apache.flink.runtime.testutils.MiniClusterResourceConfiguration;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.streaming.api.functions.sink.SinkFunction;
import org.apache.flink.streaming.api.windowing.assigners.SlidingEventTimeWindows;
import org.apache.flink.streaming.api.windowing.time.Time;
import org.apache.flink.test.util.MiniClusterWithClientResource;

import com.grofers.observability.productfunnelmetrics.datatypes.CityMetric;
import com.grofers.observability.productfunnelmetrics.datatypes.events.Event;
import com.grofers.observability.productfunnelmetrics.utils.DataGenerator;
import org.junit.ClassRule;
import org.junit.Test;
import org.junit.jupiter.api.Assertions;

import java.sql.Timestamp;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.IntStream;

import static java.time.Duration.ofSeconds;

public class ProductFunnelMetricsTest {

    @ClassRule
    public static MiniClusterWithClientResource flinkCluster =
            new MiniClusterWithClientResource(
                    new MiniClusterResourceConfiguration.Builder()
                            .setNumberSlotsPerTaskManager(2)
                            .setNumberTaskManagers(1)
                            .build());

    private static class CollectSink implements SinkFunction<CityMetric> {

        // must be static
        public static final List<CityMetric> values =
                Collections.synchronizedList(new ArrayList<>());

        @Override
        public void invoke(CityMetric value, SinkFunction.Context context) throws Exception {
            values.add(value);
        }
    }

    @Test
    public void testProductMetricsPipeline() throws Exception {
        StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment();
        env.setParallelism(2);

        CollectSink.values.clear();

        List<Event> events = new ArrayList<>();
        Instant eventTimestampBase = Instant.now().truncatedTo(ChronoUnit.DAYS);
        DataGenerator dataGenerator = new DataGenerator();
        String[] deviceIds = {"abc", "def", "jfk", "klm", "pqr", "xyz"};
        String[] advIds = {"abc", "def", "jfk", "klm", "pqr", "xyz"};
        String[] osNames = {"Android", "iOS"};
        String[] osVersions = {"8", "11"};
        String[] appVersions = {"13.3.1", "13.5.1"};
        String[] cities = {"Mumbai", "Hyderabad"};
        IntStream.range(1, 20)
                .forEach(
                        i ->
                                events.add(
                                        dataGenerator.createProductEvent(
                                                "Homepage Visit",
                                                appVersions[i % 2],
                                                cities[i % 2],
                                                osNames[i % 2],
                                                osVersions[i % 2],
                                                deviceIds[i % 6],
                                                advIds[i % 6],
                                                Timestamp.from(
                                                        eventTimestampBase.plusSeconds(10 * i)))));

        IntStream.range(1, 6)
                .forEach(
                        i ->
                                events.add(
                                        dataGenerator.createProductEvent(
                                                "Add to Cart",
                                                appVersions[i % 2],
                                                cities[i % 2],
                                                osNames[i % 2],
                                                osVersions[i % 2],
                                                deviceIds[i % 4],
                                                advIds[i % 4],
                                                Timestamp.from(
                                                        eventTimestampBase.plusSeconds(20 * i)))));

        WatermarkStrategy<Event> eventsWatermarkStrategy =
                WatermarkStrategy.<Event>forBoundedOutOfOrderness(ofSeconds(10))
                        .withIdleness(ofSeconds(1))
                        .withTimestampAssigner(
                                (event, timestamp) -> event.accessEventTimestampEpochMilli());

        env.fromElements(events.toArray(new Event[0]))
                .assignTimestampsAndWatermarks(eventsWatermarkStrategy)
                .keyBy(Event::accessEventKey)
                .window(SlidingEventTimeWindows.of(Time.minutes(1), Time.seconds(10)))
                .process(
                        new ProductFunnelMetrics
                                .CitiesPivotProductEventsMetricsGroupProcessFunction())
                .addSink(new CollectSink());

        env.execute();

        Assertions.assertEquals(70, CollectSink.values.size());
    }
}
