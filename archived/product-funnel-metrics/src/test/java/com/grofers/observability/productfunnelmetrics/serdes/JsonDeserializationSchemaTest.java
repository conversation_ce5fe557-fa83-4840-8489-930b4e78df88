package com.grofers.observability.productfunnelmetrics.serdes;

import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.exc.MismatchedInputException;

import com.grofers.observability.productfunnelmetrics.datatypes.events.Event;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;

import java.io.IOException;
import java.nio.charset.StandardCharsets;

public class JsonDeserializationSchemaTest {

    @Test
    public void deserializePositiveTest() throws IOException {
        String event =
                "{\"channel\":\"mobile\",\"context\":{\"app\":{\"build\":\"280120301\",\"name\":\"blinkit"
                    + " (grofers)\",\"namespace\":\"com.grofers.customerapp\",\"version\":\"12.3.1\"},\"device\":{\"adTrackingEnabled\":true,\"advertisingId\":\"30ce1c8f-20a6-4634-b787-817bc20094f6\",\"id\":\"c2ce1b0f4380aed6\",\"manufacturer\":\"Xiaomi\",\"model\":\"M2103K19PI\",\"name\":\"camellia\",\"type\":\"Android\"},\"library\":{\"name\":\"com.rudderstack.android.sdk.core\",\"version\":\"1.0.8\"},\"locale\":\"en-IN\",\"network\":{\"bluetooth\":false,\"carrier\":\"Vi"
                    + " India\",\"cellular\":false,\"wifi\":true},\"os\":{\"name\":\"Android\",\"version\":\"11\"},\"screen\":{\"density\":440,\"height\":2167,\"width\":1080},\"timezone\":\"Asia/Kolkata\",\"traits\":{\"city_id\":956,\"city_name\":\"Hyderabad\",\"merchant_id\":30436,\"merchant_name\":\"Super"
                    + " Store - Hyderabad Kukatapally ES4\"}},\"event\":\"Homepage"
                    + " Visit\",\"integrations\":{\"All\":true},\"messageId\":\"1641492840237-c6ee3653-cf2a-4ca4-96e0-f50a9192e61d\",\"originalTimestamp\":\"2022-01-06T18:14:00.237Z\",\"properties\":{\"entry_source_id\":\"#-NA\",\"entry_source_name\":\"#-NA\",\"entry_source_position\":-1,\"entry_source_title\":\"#-NA\",\"is_react_page\":true,\"last_page_name\":\"Homepage\",\"last_page_visit_id\":\"a7422c0c-3804-4570-ae43-4202785fffde\",\"page_id\":\"bottom_nav_home_feed_new_user\",\"page_name\":\"Homepage\",\"page_revision_id\":\"1004228\",\"page_title\":\"Bottom"
                    + " Nav home feed new"
                    + " user\",\"page_variation_id\":\"bottom_nav_home_feed_new_user\"},\"receivedAt\":\"2022-01-06T18:14:02.173Z\",\"rudderId\":\"9c397352-b1f7-4a32-93d2-cd4289de4406\",\"sentAt\":\"2022-01-06T18:14:00.763Z\",\"timestamp\":\"2022-01-06T18:14:01.647Z\",\"type\":\"screen\"}";
        JsonDeserializationSchema deserializationSchema = new JsonDeserializationSchema();
        Event eventObj = deserializationSchema.deserialize(event.getBytes(StandardCharsets.UTF_8));
        Assertions.assertEquals("Homepage Visit", eventObj.accessEventName());
        Assertions.assertEquals(1641492841, eventObj.accessEventTimestampEpochSecond());
        Assertions.assertEquals("Android 11", eventObj.accessContext().accessOs().accessOsInfo());
        Assertions.assertEquals("12.3.1", eventObj.accessContext().accessApp().accessVersion());
        Assertions.assertEquals(
                "c2ce1b0f4380aed6",
                eventObj.accessContext()
                        .accessDevice()
                        .accessUniqueId(eventObj.accessContext().accessOs().accessOsName()));
        Assertions.assertEquals("Hyderabad", eventObj.accessContext().accessTraits().accessCity());
    }

    @ParameterizedTest
    @ValueSource(
            strings = {
                "{\"channel\":\"mobile\",\"context\":{\"app\":{\"build\":\"280120301\",\"name\":\"blinkit"
                    + " (grofers)\",\"namespace\":\"com.grofers.customerapp\",\"version\":\"12.3.1\"},\"device\":{\"adTrackingEnabled\":true,\"id\":\"c2ce1b0asdf4380aefd\",\"manufacturer\":\"Xiaomi\",\"type\":\"Android\"},\"library\":{\"name\":\"com.rudderstack.android.sdk.core\",\"version\":\"1.0.8\"},\"locale\":\"en-IN\",\"network\":{\"bluetooth\":false,\"carrier\":\"Vi"
                    + " India\",\"cellular\":false,\"wifi\":true},\"os\":{\"name\":\"Android\",\"version\":\"11\"},\"screen\":{\"density\":440,\"height\":2167,\"width\":1080},\"timezone\":\"Asia/Kolkata\",\"traits\":{\"city_id\":956,\"city_name\":\"Hyderabad\",\"merchant_id\":30436,\"merchant_name\":\"Super"
                    + " Store - Hyderabad Kukatapally"
                    + " ES4\"}},\"integrations\":{\"All\":true},\"messageId\":\"1641492840237-c6ee3653-cf2a-4ca4-96e0-f50a9192e61d\",\"originalTimestamp\":\"2022-01-06T18:14:00.237Z\",\"properties\":{\"entry_source_id\":\"#-NA\",\"entry_source_name\":\"#-NA\",\"entry_source_position\":-1,\"entry_source_title\":\"#-NA\",\"is_react_page\":true,\"last_page_name\":\"Homepage\",\"last_page_visit_id\":\"a7422c0c-3804-4570-ae43-4202785fffde\",\"page_id\":\"bottom_nav_home_feed_new_user\",\"page_name\":\"Homepage\",\"page_revision_id\":\"1004228\",\"page_title\":\"Bottom"
                    + " Nav home feed new"
                    + " user\",\"page_variation_id\":\"bottom_nav_home_feed_new_user\"},\"receivedAt\":\"2022-01-06T18:14:02.173Z\",\"rudderId\":\"9c397352-b1f7-4a32-93d2-cd4289de4406\",\"sentAt\":\"2022-01-06T18:14:00.763Z\",\"timestamp\":\"2022-01-06T18:14:01.647Z\",\"type\":\"screen\"}",
                "{\"channel\":\"mobile\",\"context\":{\"app\":{\"build\":\"280120301\",\"name\":\"blinkit"
                    + " (grofers)\",\"namespace\":\"com.grofers.customerapp\",\"version\":\"12.3.1\"},\"library\":{\"name\":\"com.rudderstack.android.sdk.core\",\"version\":\"1.0.8\"},\"locale\":\"en-IN\",\"network\":{\"bluetooth\":false,\"carrier\":\"Vi"
                    + " India\",\"cellular\":false,\"wifi\":true},\"os\":{\"name\":\"Android\",\"version\":\"11\"},\"screen\":{\"density\":440,\"height\":2167,\"width\":1080},\"timezone\":\"Asia/Kolkata\",\"traits\":{\"city_id\":956,\"city_name\":\"Hyderabad\",\"merchant_id\":30436,\"merchant_name\":\"Super"
                    + " Store - Hyderabad Kukatapally ES4\"}},\"event\":\"Homepage"
                    + " Visit\",\"integrations\":{\"All\":true},\"messageId\":\"1641492840237-c6ee3653-cf2a-4ca4-96e0-f50a9192e61d\",\"originalTimestamp\":\"2022-01-06T18:14:00.237Z\",\"properties\":{\"entry_source_id\":\"#-NA\",\"entry_source_name\":\"#-NA\",\"entry_source_position\":-1,\"entry_source_title\":\"#-NA\",\"is_react_page\":true,\"last_page_name\":\"Homepage\",\"last_page_visit_id\":\"a7422c0c-3804-4570-ae43-4202785fffde\",\"page_id\":\"bottom_nav_home_feed_new_user\",\"page_name\":\"Homepage\",\"page_revision_id\":\"1004228\",\"page_title\":\"Bottom"
                    + " Nav home feed new"
                    + " user\",\"page_variation_id\":\"bottom_nav_home_feed_new_user\"},\"receivedAt\":\"2022-01-06T18:14:02.173Z\",\"rudderId\":\"9c397352-b1f7-4a32-93d2-cd4289de4406\",\"sentAt\":\"2022-01-06T18:14:00.763Z\",\"timestamp\":\"2022-01-06T18:14:01.647Z\",\"type\":\"screen\"}"
            })
    public void deserializeMissingFieldTest(String event) {
        JsonDeserializationSchema deserializationSchema = new JsonDeserializationSchema();
        Assertions.assertThrows(
                MismatchedInputException.class,
                () -> deserializationSchema.deserialize(event.getBytes(StandardCharsets.UTF_8)));
    }
}
