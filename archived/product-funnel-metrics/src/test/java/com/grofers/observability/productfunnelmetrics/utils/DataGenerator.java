package com.grofers.observability.productfunnelmetrics.utils;

import com.grofers.observability.productfunnelmetrics.datatypes.events.*;

import java.sql.Timestamp;

public class DataGenerator {
    public Event createProductEvent(
            String eventName,
            String appVersion,
            String city,
            String osName,
            String osVersion,
            String deviceId,
            String advertisingId,
            Timestamp eventTimestamp) {
        ContextApp app = new ContextApp(appVersion);
        ContextTraits traits = new ContextTraits(city, 123456);
        ContextDevice device = new ContextDevice(deviceId, advertisingId);
        ContextOs os = new ContextOs(osName, osVersion);
        EventContext eventContext = new EventContext(app, device, os, traits);
        Event event = new Event(eventName, eventContext, eventTimestamp);
        return event;
    }
}
