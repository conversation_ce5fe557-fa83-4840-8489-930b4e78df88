# Properties for stage environment

#Checkpoints
CHECKPOINTS_STORAGE_LOCATION = s3a://prod-data-flink-states/flink-streams/observability/product-funnel-metrics/checkpoints/

# Events Source
EVENTS_SOURCE_BROKERS = b-2.prod-data-events.771kbr.c4.kafka.ap-southeast-1.amazonaws.com:9092,b-1.prod-data-events.771kbr.c4.kafka.ap-southeast-1.amazonaws.com:9092,b-3.prod-data-events.771kbr.c4.kafka.ap-southeast-1.amazonaws.com:9092
EVENTS_SOURCE_CONSUMER_GROUP_ID = flink-observability-product_funnel_metrics-consumer_group-v1
EVENTS_SOURCE_TOPIC = rudder.track.mobile.core_ordering

PRODUCT_EVENT_METRICS_SINK_BROKERS = b-1.prod-data-corestr.uynfnv.c4.kafka.ap-southeast-1.amazonaws.com:9092,b-2.prod-data-corestr.uynfnv.c4.kafka.ap-southeast-1.amazonaws.com:9092,b-3.prod-data-corestr.uynfnv.c4.kafka.ap-southeast-1.amazonaws.com:9092
CITY_PRODUCT_EVENT_METRICS_SINK_TOPIC = observability.metrics.product-funnel-metrics
MERCHANT_PRODUCT_EVENT_METRICS_SINK_TOPIC = observability.metrics.merchant-product-funnel-metrics

#Watermark Idleness Configs
IDLENESS_TIME_IN_SECS = 600

# Window Configs
SLIDING_WINDOW_SIZE_IN_MINS = 10
SLIDING_WINDOW_SLIDE_IN_SECS = 60
