
# Properties for stage environment

#Checkpoints
CHECKPOINTS_STORAGE_LOCATION = s3a://grofers-test-dse-singapore/product-funnel-metrics

# Events Source
EVENTS_SOURCE_BROKERS = b-1.test-dse-common.iosmgv.c4.kafka.ap-southeast-1.amazonaws.com:9092,b-2.test-dse-common.iosmgv.c4.kafka.ap-southeast-1.amazonaws.com:9092,b-3.test-dse-common.iosmgv.c4.kafka.ap-southeast-1.amazonaws.com:9092
EVENTS_SOURCE_CONSUMER_GROUP_ID = test-rudder-product-metrics
EVENTS_SOURCE_TOPIC = rudder.track.mobile.homepage_visit,rudder.track.mobile.add_to_cart

PRODUCT_EVENT_METRICS_SINK_BROKERS = b-1.test-dse-common.iosmgv.c4.kafka.ap-southeast-1.amazonaws.com:9092,b-2.test-dse-common.iosmgv.c4.kafka.ap-southeast-1.amazonaws.com:9092,b-3.test-dse-common.iosmgv.c4.kafka.ap-southeast-1.amazonaws.com:9092
CITY_PRODUCT_EVENT_METRICS_SINK_TOPIC = observability.product-funnel-metrics-testing
MERCHANT_PRODUCT_EVENT_METRICS_SINK_TOPIC = observability.merchant-product-funnel-metrics-testing
#Watermark Idleness Configs
IDLENESS_TIME_IN_SECS = 600

# Window Configs
SLIDING_WINDOW_SIZE_IN_MINS = 10
SLIDING_WINDOW_SLIDE_IN_SECS = 60

