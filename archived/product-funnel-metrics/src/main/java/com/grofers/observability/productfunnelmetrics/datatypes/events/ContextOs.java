package com.grofers.observability.productfunnelmetrics.datatypes.events;

import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.annotation.JsonProperty;

@JsonIgnoreProperties(ignoreUnknown = true)
public class ContextOs {
    private final String name;
    private final String version;
    private static final String NULL_VALUE = "null";

    public ContextOs(
            @JsonProperty(value = "name", defaultValue = NULL_VALUE) String name,
            @JsonProperty(value = "version", defaultValue = NULL_VALUE) String version) {
        this.name = name;
        this.version = version;
    }

    public String accessOsInfo() {
        if (name.equalsIgnoreCase(NULL_VALUE) || version.equalsIgnoreCase(NULL_VALUE)) {
            return NULL_VALUE;
        }
        return String.format("%s %s", this.name, this.version);
    }

    public String accessOsName() {
        return name;
    }
}
