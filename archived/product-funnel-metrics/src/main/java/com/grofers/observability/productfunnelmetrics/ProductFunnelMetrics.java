/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.grofers.observability.productfunnelmetrics;

import org.apache.flink.api.common.ExecutionConfig;
import org.apache.flink.api.common.JobExecutionResult;
import org.apache.flink.api.common.eventtime.WatermarkStrategy;
import org.apache.flink.api.connector.sink.Sink;
import org.apache.flink.api.java.tuple.Tuple2;
import org.apache.flink.api.java.tuple.Tuple3;
import org.apache.flink.api.java.tuple.Tuple4;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.connector.base.DeliveryGuarantee;
import org.apache.flink.connector.kafka.sink.KafkaSink;
import org.apache.flink.connector.kafka.source.KafkaSource;
import org.apache.flink.connector.kafka.source.enumerator.initializer.OffsetsInitializer;
import org.apache.flink.streaming.api.CheckpointingMode;
import org.apache.flink.streaming.api.datastream.DataStreamSource;
import org.apache.flink.streaming.api.environment.CheckpointConfig;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.streaming.api.functions.windowing.ProcessWindowFunction;
import org.apache.flink.streaming.api.windowing.assigners.SlidingEventTimeWindows;
import org.apache.flink.streaming.api.windowing.time.Time;
import org.apache.flink.streaming.api.windowing.windows.TimeWindow;
import org.apache.flink.util.Collector;

import com.grofers.observability.productfunnelmetrics.configs.JobConfig;
import com.grofers.observability.productfunnelmetrics.configs.JobConfigManager;
import com.grofers.observability.productfunnelmetrics.datatypes.CityMetric;
import com.grofers.observability.productfunnelmetrics.datatypes.MerchantMetric;
import com.grofers.observability.productfunnelmetrics.datatypes.events.Event;
import com.grofers.observability.productfunnelmetrics.serdes.CityMetricRecordSerializationSchema;
import com.grofers.observability.productfunnelmetrics.serdes.JsonDeserializationSchema;
import com.grofers.observability.productfunnelmetrics.serdes.MerchantMetricRecordSerializationSchema;
import org.apache.kafka.clients.consumer.OffsetResetStrategy;

import java.util.*;

import static java.time.Duration.ofSeconds;

/**
 * Skeleton for a Flink Streaming Job.
 *
 * <p>For a tutorial how to write a Flink streaming application, check the tutorials and examples on
 * the <a href="https://flink.apache.org/docs/stable/">Flink Website</a>.
 *
 * <p>To package your application into a JAR file for execution, run 'mvn clean package' on the
 * command line.
 *
 * <p>If you change the name of the main class (with the public static void main(String[] args))
 * method, change the respective entry in the POM.xml file (simply search for 'mainClass').
 */
public class ProductFunnelMetrics {

    private final DataStreamSource<Event> productEventsSource;
    private final Sink<CityMetric, ?, ?, ?> cityProductMetricsSink;
    private final Sink<MerchantMetric, ?, ?, ?> merchantProductMetricsSink;

    public ProductFunnelMetrics(
            DataStreamSource<Event> productEventsSource,
            Sink<CityMetric, ?, ?, ?> cityProductMetricsSink,
            Sink<MerchantMetric, ?, ?, ?> merchantProductMetricsSink) {
        this.productEventsSource = productEventsSource;
        this.cityProductMetricsSink = cityProductMetricsSink;
        this.merchantProductMetricsSink = merchantProductMetricsSink;
    }

    public static void main(String[] args) throws Exception {
        // set up the streaming execution environment
        final StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment();

        // Parameters for environment
        ParameterTool params = ParameterTool.fromArgs(args);
        String userEnv = params.getRequired("environment");

        // Set PropertiesPath for userEnv
        JobConfigManager.setJobConfigsPath(userEnv);
        // Get PropertiesPath for userEnv
        String jobPropertiesPath = JobConfigManager.getJobConfigsPath(userEnv);
        // Fetch job config
        JobConfigManager.setJobConfigs(env, jobPropertiesPath);
        JobConfigManager.getJobConfigs(env);

        // Checkpoint Configs
        // start a checkpoint every 240000 ms
        env.enableCheckpointing(240000);
        // set mode to exactly-once (default)
        env.getCheckpointConfig().setCheckpointingMode(CheckpointingMode.EXACTLY_ONCE);
        // make sure 120000 ms of progress happen between checkpoints
        env.getCheckpointConfig().setMinPauseBetweenCheckpoints(240000);
        // checkpoints have to complete within a minute, or are discarded
        env.getCheckpointConfig().setCheckpointTimeout(210000);
        // only two consecutive checkpoint failures are tolerated
        env.getCheckpointConfig().setTolerableCheckpointFailureNumber(2);
        // allow only one checkpoint to be in progress at the same time
        env.getCheckpointConfig().setMaxConcurrentCheckpoints(1);
        // enable externalized checkpoints which are retained after job cancellation
        env.getCheckpointConfig()
                .enableExternalizedCheckpoints(
                        CheckpointConfig.ExternalizedCheckpointCleanup.RETAIN_ON_CANCELLATION);
        // enables the experimental unaligned checkpoints
        env.getCheckpointConfig().enableUnalignedCheckpoints();
        // sets the checkpoint storage where checkpoint snapshots will be written
        env.getCheckpointConfig().setCheckpointStorage(JobConfig.CHECKPOINTS_STORAGE_LOCATION);
        // Enable checkpoint compression
        ExecutionConfig executionConfig = new ExecutionConfig();
        executionConfig.setUseSnapshotCompression(true);

        KafkaSource<Event> productEventsSource =
                KafkaSource.<Event>builder()
                        .setBootstrapServers(JobConfig.EVENTS_SOURCE_BROKERS)
                        .setGroupId(JobConfig.EVENTS_SOURCE_CONSUMER_GROUP_ID)
                        .setClientIdPrefix("product_funnel_metrics-")
                        .setValueOnlyDeserializer(new JsonDeserializationSchema())
                        .setStartingOffsets(
                                OffsetsInitializer.committedOffsets(OffsetResetStrategy.LATEST))
                        .setTopics(JobConfig.EVENTS_SOURCE_TOPIC)
                        .setProperty("partition.discovery.interval.ms", "600000")
                        .setProperty("reconnect.backoff.max.ms", "300000")
                        .setProperty("reconnect.backoff.ms", "60000")
                        .build();

        WatermarkStrategy<Event> productEventsSourceWatermarkStrategy =
                WatermarkStrategy.<Event>forBoundedOutOfOrderness(ofSeconds(600))
                        .withIdleness(ofSeconds(JobConfig.IDLENESS_TIME_IN_SECS))
                        .withTimestampAssigner(
                                (event, timestamp) -> event.accessEventTimestampEpochMilli());

        DataStreamSource<Event> productEventsStreamSource =
                env.fromSource(
                        productEventsSource,
                        productEventsSourceWatermarkStrategy,
                        "productEventsSource");

        KafkaSink<CityMetric> cityProductMetricsKafkaSink =
                KafkaSink.<CityMetric>builder()
                        .setBootstrapServers(JobConfig.PRODUCT_EVENT_METRICS_SINK_BROKERS)
                        .setDeliverGuarantee(DeliveryGuarantee.AT_LEAST_ONCE)
                        .setTransactionalIdPrefix("product_funnel_metrics-")
                        .setRecordSerializer(
                                new CityMetricRecordSerializationSchema(
                                        JobConfig.CITY_PRODUCT_EVENT_METRICS_SINK_TOPIC))
                        .build();

        KafkaSink<MerchantMetric> merchantProductMetricsKafkaSink =
                KafkaSink.<MerchantMetric>builder()
                        .setBootstrapServers(JobConfig.PRODUCT_EVENT_METRICS_SINK_BROKERS)
                        .setDeliverGuarantee(DeliveryGuarantee.AT_LEAST_ONCE)
                        .setTransactionalIdPrefix("product_funnel_metrics-")
                        .setRecordSerializer(
                                new MerchantMetricRecordSerializationSchema(
                                        JobConfig.MERCHANT_PRODUCT_EVENT_METRICS_SINK_TOPIC))
                        .build();

        ProductFunnelMetrics productFunnelMetrics =
                new ProductFunnelMetrics(
                        productEventsStreamSource,
                        cityProductMetricsKafkaSink,
                        merchantProductMetricsKafkaSink);
        productFunnelMetrics.execute(env);
    }

    public JobExecutionResult execute(StreamExecutionEnvironment environment) throws Exception {
        this.productEventsSource
                .name("product-events-source")
                .uid("product-events-source")
                .setParallelism(1)
                .keyBy(Event::accessEventKey)
                .window(
                        SlidingEventTimeWindows.of(
                                Time.minutes(JobConfig.SLIDING_WINDOW_SIZE_IN_MINS),
                                Time.seconds(JobConfig.SLIDING_WINDOW_SLIDE_IN_SECS)))
                .allowedLateness(Time.seconds(JobConfig.IDLENESS_TIME_IN_SECS))
                .process(new CitiesPivotProductEventsMetricsGroupProcessFunction())
                .setParallelism(2)
                .name("city-product-events-metrics-window")
                .uid("city-product-events-metrics-window")
                .sinkTo(cityProductMetricsSink)
                .setParallelism(2)
                .name("city-product-events-metrics-sink")
                .uid("city-product-events-metrics-sink");

        this.productEventsSource
                .name("product-events-source")
                .uid("product-events-source")
                .setParallelism(1)
                .filter(event -> event.accessContext().accessTraits().accessMerchantId() != null)
                .name("filter-null-merchant-id")
                .uid("filter-null-merchant-id")
                .setParallelism(1)
                .keyBy(event -> event.accessContext().accessTraits().accessMerchantId())
                .window(
                        SlidingEventTimeWindows.of(
                                Time.minutes(JobConfig.SLIDING_WINDOW_SIZE_IN_MINS),
                                Time.seconds(JobConfig.SLIDING_WINDOW_SLIDE_IN_SECS)))
                .allowedLateness(Time.seconds(JobConfig.IDLENESS_TIME_IN_SECS))
                .process(new MerchantsPivotProductEventsMetricsGroupProcessFunction())
                .setParallelism(2)
                .name("merchant-product-events-metrics-window")
                .uid("merchant-product-events-metrics-window")
                .sinkTo(merchantProductMetricsSink)
                .setParallelism(2)
                .name("merchant-product-events-metrics-sink")
                .uid("merchant-product-events-metrics-sink");

        return environment.execute("observability.product-funnel-metrics");
    }

    public static class CitiesPivotProductEventsMetricsGroupProcessFunction
            extends ProcessWindowFunction<Event, CityMetric, Tuple2<String, String>, TimeWindow> {

        @Override
        public void process(
                Tuple2<String, String> tuple2,
                ProcessWindowFunction<Event, CityMetric, Tuple2<String, String>, TimeWindow>.Context
                        context,
                Iterable<Event> iterable,
                Collector<CityMetric> collector) {

            Map<Tuple4<String, String, String, String>, Set<String>> results = new HashMap<>();

            for (Event e : iterable) {
                Tuple4<String, String, String, String> mapKey =
                        Tuple4.of(
                                e.accessEventName(),
                                e.accessContext().accessOs().accessOsInfo(),
                                e.accessContext().accessApp().accessVersion(),
                                e.accessContext().accessTraits().accessCity());
                Set<String> deviceIds;
                if (results.containsKey(mapKey)) {
                    deviceIds = results.get(mapKey);
                } else {
                    deviceIds = new HashSet<>();
                }
                deviceIds.add(
                        e.accessContext()
                                .accessDevice()
                                .accessUniqueId(e.accessContext().accessOs().accessOsName()));
                results.put(mapKey, deviceIds);
            }

            for (Map.Entry<Tuple4<String, String, String, String>, Set<String>> result :
                    results.entrySet()) {
                collector.collect(
                        new CityMetric(
                                result.getKey().f0,
                                result.getKey().f1,
                                result.getKey().f2,
                                result.getKey().f3,
                                result.getValue().size(),
                                context.window().getStart(),
                                context.window().getEnd()));
            }
        }
    }

    public static class MerchantsPivotProductEventsMetricsGroupProcessFunction
            extends ProcessWindowFunction<Event, MerchantMetric, Integer, TimeWindow> {

        @Override
        public void process(
                Integer key,
                ProcessWindowFunction<Event, MerchantMetric, Integer, TimeWindow>.Context context,
                Iterable<Event> iterable,
                Collector<MerchantMetric> collector) {

            Map<Tuple3<String, String, Integer>, Set<String>> results = new HashMap<>();

            for (Event e : iterable) {
                Tuple3<String, String, Integer> mapKey =
                        Tuple3.of(
                                e.accessEventName(),
                                e.accessContext().accessTraits().accessCity(),
                                e.accessContext().accessTraits().accessMerchantId());
                Set<String> deviceIds;
                if (results.containsKey(mapKey)) {
                    deviceIds = results.get(mapKey);
                } else {
                    deviceIds = new HashSet<>();
                }
                deviceIds.add(
                        e.accessContext()
                                .accessDevice()
                                .accessUniqueId(e.accessContext().accessOs().accessOsName()));
                results.put(mapKey, deviceIds);
            }

            for (Map.Entry<Tuple3<String, String, Integer>, Set<String>> result :
                    results.entrySet()) {
                collector.collect(
                        new MerchantMetric(
                                result.getKey().f0,
                                result.getKey().f1,
                                result.getKey().f2,
                                result.getValue().size(),
                                context.window().getStart(),
                                context.window().getEnd()));
            }
        }
    }
}
