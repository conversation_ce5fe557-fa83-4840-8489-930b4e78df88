package com.grofers.observability.productfunnelmetrics.datatypes.events;

import org.apache.flink.api.java.tuple.Tuple2;

import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.annotation.JsonProperty;

import java.sql.Timestamp;

@JsonIgnoreProperties(ignoreUnknown = true)
public class Event {
    private final String eventName;

    private final EventContext context;

    private final Timestamp eventTimestamp;

    public Event(
            @JsonProperty(value = "event", required = true) String eventName,
            @JsonProperty(value = "context", required = true) EventContext context,
            @JsonProperty(value = "timestamp", required = true)
                    @JsonFormat(
                            shape = JsonFormat.Shape.STRING,
                            pattern = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'",
                            timezone = "UTC")
                    Timestamp eventTimestamp) {
        this.eventName = eventName;
        this.context = context;
        this.eventTimestamp = eventTimestamp;
    }

    public String accessEventName() {
        return eventName;
    }

    public EventContext accessContext() {
        return this.context;
    }

    public Long accessEventTimestampEpochMilli() {
        return this.eventTimestamp.toInstant().toEpochMilli();
    }

    public Long accessEventTimestampEpochSecond() {
        return this.eventTimestamp.toInstant().getEpochSecond();
    }

    public Tuple2<String, String> accessEventKey() {
        return new Tuple2<>(accessEventName(), accessContext().accessTraits().accessCity());
    }
}
