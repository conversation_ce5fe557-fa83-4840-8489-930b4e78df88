package com.grofers.observability.productfunnelmetrics.datatypes.events;

import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.annotation.JsonProperty;

@JsonIgnoreProperties(ignoreUnknown = true)
public class EventContext {

    private final ContextApp app;
    private final ContextDevice device;
    private final ContextOs os;
    private final ContextTraits traits;

    public EventContext(
            @JsonProperty(value = "app", required = true) ContextApp app,
            @JsonProperty(value = "device", required = true) ContextDevice device,
            @JsonProperty(value = "os", required = true) ContextOs os,
            @JsonProperty(value = "traits", required = true) ContextTraits traits) {
        this.app = app;
        this.device = device;
        this.os = os;
        this.traits = traits;
    }

    public ContextApp accessApp() {
        return this.app;
    }

    public ContextDevice accessDevice() {
        return this.device;
    }

    public ContextOs accessOs() {
        return this.os;
    }

    public ContextTraits accessTraits() {
        return this.traits;
    }
}
