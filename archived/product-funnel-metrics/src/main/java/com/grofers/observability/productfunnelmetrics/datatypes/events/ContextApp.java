package com.grofers.observability.productfunnelmetrics.datatypes.events;

import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.annotation.JsonProperty;

@JsonIgnoreProperties(ignoreUnknown = true)
public class ContextApp {
    private final String version;

    public ContextApp(@JsonProperty(value = "version", required = true) String version) {
        this.version = version;
    }

    public String accessVersion() {
        return this.version;
    }
}
