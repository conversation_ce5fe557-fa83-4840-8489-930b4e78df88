package com.grofers.observability.productfunnelmetrics.serdes;

import org.apache.flink.api.common.serialization.SerializationSchema;
import org.apache.flink.connector.kafka.sink.KafkaRecordSerializationSchema;

import com.grofers.observability.productfunnelmetrics.datatypes.MerchantMetric;
import org.apache.kafka.clients.producer.ProducerRecord;

import java.nio.charset.StandardCharsets;

public class MerchantMetricRecordSerializationSchema
        implements KafkaRecordSerializationSchema<MerchantMetric> {

    private static final long serialVersionUID = 1L;
    private final String topic;

    public MerchantMetricRecordSerializationSchema(String topic) {
        this.topic = topic;
    }

    @Override
    public void open(
            SerializationSchema.InitializationContext context, KafkaSinkContext sinkContext)
            throws Exception {
        KafkaRecordSerializationSchema.super.open(context, sinkContext);
    }

    @Override
    public ProducerRecord<byte[], byte[]> serialize(
            MerchantMetric metric, KafkaSinkContext kafkaSinkContext, Long aLong) {
        return new ProducerRecord<>(
                this.topic,
                metric.getMessageKey().toString().getBytes(StandardCharsets.UTF_8),
                metric.toString().getBytes(StandardCharsets.UTF_8));
    }
}
