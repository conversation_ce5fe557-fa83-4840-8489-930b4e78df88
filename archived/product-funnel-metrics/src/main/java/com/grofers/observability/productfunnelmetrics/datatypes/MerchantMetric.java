package com.grofers.observability.productfunnelmetrics.datatypes;

public class MerchantMetric {
    private final String eventName;
    private final String city;
    private final int merchantId;
    private final int deviceCount;
    private final Long windowStart;
    private final Long windowEnd;

    public MerchantMetric(
            String eventName,
            String city,
            int merchantId,
            int deviceCount,
            Long windowStart,
            Long windowEnd) {
        this.eventName = eventName;
        this.city = city;
        this.merchantId = merchantId;
        this.deviceCount = deviceCount;
        this.windowStart = windowStart;
        this.windowEnd = windowEnd;
    }

    @Override
    public String toString() {
        return "{"
                + "\"eventName\":\""
                + eventName
                + '\"'
                + ", \"city\":\""
                + city
                + '\"'
                + ", \"merchantId\":"
                + merchantId
                + ", \"deviceCount\":"
                + deviceCount
                + ", \"windowStart\":"
                + windowStart
                + ", \"windowEnd\":"
                + windowEnd
                + "}";
    }

    public Integer getMessageKey() {
        return this.merchantId;
    }
}
