package com.grofers.observability.productfunnelmetrics.serdes;

import org.apache.flink.api.common.serialization.SerializationSchema;
import org.apache.flink.connector.kafka.sink.KafkaRecordSerializationSchema;

import com.grofers.observability.productfunnelmetrics.datatypes.CityMetric;
import org.apache.kafka.clients.producer.ProducerRecord;

import java.nio.charset.StandardCharsets;

public class CityMetricRecordSerializationSchema
        implements KafkaRecordSerializationSchema<CityMetric> {

    private static final long serialVersionUID = 1L;
    private final String topic;

    public CityMetricRecordSerializationSchema(String topic) {
        this.topic = topic;
    }

    @Override
    public void open(
            SerializationSchema.InitializationContext context, KafkaSinkContext sinkContext)
            throws Exception {
        KafkaRecordSerializationSchema.super.open(context, sinkContext);
    }

    @Override
    public ProducerRecord<byte[], byte[]> serialize(
            CityMetric metric, KafkaSinkContext kafkaSinkContext, Long aLong) {
        return new ProducerRecord<>(this.topic, metric.toString().getBytes(StandardCharsets.UTF_8));
    }
}
