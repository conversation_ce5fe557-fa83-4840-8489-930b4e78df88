package com.grofers.observability.productfunnelmetrics.datatypes.events;

import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.annotation.JsonProperty;

@JsonIgnoreProperties(ignoreUnknown = true)
public class ContextDevice {
    private final String id;
    private final String advertisingId;

    public ContextDevice(
            @JsonProperty(value = "id") String id,
            @JsonProperty(value = "advertisingId") String advertisingId) {
        this.id = id;
        this.advertisingId = advertisingId;
    }

    public String accessUniqueId(String osName) {
        if (osName.equalsIgnoreCase("Android")) {
            return this.id;
        } else if (osName.equalsIgnoreCase("iOS")) {
            return this.advertisingId;
        }
        return this.id != null ? this.id : this.advertisingId != null ? this.advertisingId : "null";
    }
}
