package com.grofers.observability.productfunnelmetrics.configs;

public final class JobConfig {
    public static String CHECKPOINTS_STORAGE_LOCATION;
    public static String EVENTS_SOURCE_BROKERS;
    public static String EVENTS_SOURCE_CONSUMER_GROUP_ID;
    public static String[] EVENTS_SOURCE_TOPIC;
    public static int IDLENESS_TIME_IN_SECS;
    public static int SLIDING_WINDOW_SIZE_IN_MINS;
    public static int SLIDING_WINDOW_SLIDE_IN_SECS;
    public static String PRODUCT_EVENT_METRICS_SINK_BROKERS;
    public static String CITY_PRODUCT_EVENT_METRICS_SINK_TOPIC;
    public static String MERCHANT_PRODUCT_EVENT_METRICS_SINK_TOPIC;

    public JobConfig(
            String CHECKPOINTS_STORAGE_LOCATION,
            String EVENTS_SOURCE_BROKERS,
            String EVENTS_SOURCE_CONSUMER_GROUP_ID,
            String EVENTS_SOURCE_TOPIC,
            int IDLENESS_TIME_IN_SECS,
            int SLIDING_WINDOW_SIZE_IN_MINS,
            int SLIDING_WINDOW_SLIDE_IN_SECS,
            String PRODUCT_EVENT_METRICS_SINK_BROKERS,
            String CITY_PRODUCT_EVENT_METRICS_SINK_TOPIC,
            String MERCHANT_PRODUCT_EVENT_METRICS_SINK_TOPIC) {

        JobConfig.CHECKPOINTS_STORAGE_LOCATION = CHECKPOINTS_STORAGE_LOCATION;
        JobConfig.EVENTS_SOURCE_BROKERS = EVENTS_SOURCE_BROKERS;
        JobConfig.EVENTS_SOURCE_CONSUMER_GROUP_ID = EVENTS_SOURCE_CONSUMER_GROUP_ID;
        JobConfig.EVENTS_SOURCE_TOPIC = EVENTS_SOURCE_TOPIC.split(",");
        JobConfig.IDLENESS_TIME_IN_SECS = IDLENESS_TIME_IN_SECS;
        JobConfig.SLIDING_WINDOW_SIZE_IN_MINS = SLIDING_WINDOW_SIZE_IN_MINS;
        JobConfig.SLIDING_WINDOW_SLIDE_IN_SECS = SLIDING_WINDOW_SLIDE_IN_SECS;
        JobConfig.PRODUCT_EVENT_METRICS_SINK_BROKERS = PRODUCT_EVENT_METRICS_SINK_BROKERS;
        JobConfig.CITY_PRODUCT_EVENT_METRICS_SINK_TOPIC = CITY_PRODUCT_EVENT_METRICS_SINK_TOPIC;
        JobConfig.MERCHANT_PRODUCT_EVENT_METRICS_SINK_TOPIC =
                MERCHANT_PRODUCT_EVENT_METRICS_SINK_TOPIC;
    }
}
