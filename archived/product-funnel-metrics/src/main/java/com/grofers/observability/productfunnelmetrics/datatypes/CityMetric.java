package com.grofers.observability.productfunnelmetrics.datatypes;

public class CityMetric {
    private final String eventName;
    private final String osInfo;
    private final String appVersion;
    private final String city;
    private final int deviceCount;
    private final Long windowStart;
    private final Long windowEnd;

    public CityMetric(
            String eventName,
            String osInfo,
            String appVersion,
            String city,
            int deviceCount,
            Long windowStart,
            Long windowEnd) {
        this.eventName = eventName;
        this.osInfo = osInfo;
        this.appVersion = appVersion;
        this.city = city;
        this.deviceCount = deviceCount;
        this.windowStart = windowStart;
        this.windowEnd = windowEnd;
    }

    @Override
    public String toString() {
        return "{"
                + "\"eventName\":\""
                + eventName
                + '\"'
                + ", \"osInfo\":\""
                + osInfo
                + '\"'
                + ", \"appVersion\":\""
                + appVersion
                + '\"'
                + ", \"city\":\""
                + city
                + '\"'
                + ", \"deviceCount\":"
                + deviceCount
                + ", \"windowStart\":"
                + windowStart
                + ", \"windowEnd\":"
                + windowEnd
                + "}";
    }
}
