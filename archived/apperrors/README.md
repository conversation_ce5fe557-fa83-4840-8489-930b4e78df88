# App Error Metrics & Alerting

In this job we tail all the HTTP error events we emit from our consumer app, Aggregate the number of errors with a [Tumbling window](https://dzone.com/articles/deep-dive-into-apache-flinks-tumblingwindow) of a minute to avoid jitters and push this aggregated count into Kafka where eventually its scraped by prometheus using [metrics collector](https://github.com/Zomato/metrics-collector)

We have created a [Grafana Dashboard](https://grafana.grofers.com/d/IAUNVd37z/http-errors?orgId=1) to Monitor the app error aggregate metrics and send out alerts on slack `#bl-consumer-app-alerts` on abnormal spikes
