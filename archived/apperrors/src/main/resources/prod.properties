# Properties for stage environment

#Checkpoints
CHECKPOINTS_STORAGE_LOCATION = s3a://prod-data-flink-states/flink-streams/alerting/apperrors

# Events Source
EVENTS_SOURCE_BROKERS = b-2.prod-data-events.771kbr.c4.kafka.ap-southeast-1.amazonaws.com:9092,b-1.prod-data-events.771kbr.c4.kafka.ap-southeast-1.amazonaws.com:9092,b-3.prod-data-events.771kbr.c4.kafka.ap-southeast-1.amazonaws.com:9092
EVENTS_SOURCE_CONSUMER_GROUP_ID = flink-alerting-apperrors-consumer-group-v1
EVENTS_SOURCE_TOPIC = rudder.track.mobile.http_error

REMOTE_WRITE_METRICS_SINK_BROKERS = b-1.prod-data-corestr.uynfnv.c4.kafka.ap-southeast-1.amazonaws.com:9092,b-2.prod-data-corestr.uynfnv.c4.kafka.ap-southeast-1.amazonaws.com:9092,b-3.prod-data-corestr.uynfnv.c4.kafka.ap-southeast-1.amazonaws.com:9092
REMOTE_WRITE_METRICS_SINK_TOPIC = alerting.apperrors.remote-write-prometheus-metrics

METRICS_CONFIG_HTTP_ERROR_METRIC_NAME = consumer_http_error_metrics
METRICS_CONFIG_HTTP_ERROR_METRIC_TYPE = counter
METRICS_CONFIG_HTTP_ERROR_METRIC_HELP = Aggregated Http Error metrics per minute