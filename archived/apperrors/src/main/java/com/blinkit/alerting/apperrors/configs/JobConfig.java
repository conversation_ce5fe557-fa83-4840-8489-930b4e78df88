package com.blinkit.alerting.apperrors.configs;

public final class JobConfig {
    public static String CHECKPOINTS_STORAGE_LOCATION;
    public static String EVENTS_SOURCE_BROKERS;
    public static String EVENTS_SOURCE_CONSUMER_GROUP_ID;
    public static String[] EVENTS_SOURCE_TOPIC;
    public static String METRICS_CONFIG_HTTP_ERROR_METRIC_NAME;
    public static String METRICS_CONFIG_HTTP_ERROR_METRIC_TYPE;
    public static String METRICS_CONFIG_HTTP_ERROR_METRIC_HELP;
    public static String REMOTE_WRITE_METRICS_SINK_BROKERS;
    public static String REMOTE_WRITE_METRICS_SINK_TOPIC;

    public JobConfig(
            String CHECKPOINTS_STORAGE_LOCATION,
            String EVENTS_SOURCE_BROKERS,
            String EVENTS_SOURCE_CONSUMER_GROUP_ID,
            String EVENTS_SOURCE_TOPIC,
            String METRICS_CONFIG_HTTP_ERROR_METRIC_NAME,
            String METRICS_CONFIG_HTTP_ERROR_METRIC_TYPE,
            String METRICS_CONFIG_HTTP_ERROR_METRIC_HELP,
            String REMOTE_WRITE_METRICS_SINK_BROKERS,
            String REMOTE_WRITE_METRICS_SINK_TOPIC) {

        JobConfig.CHECKPOINTS_STORAGE_LOCATION = CHECKPOINTS_STORAGE_LOCATION;
        JobConfig.EVENTS_SOURCE_BROKERS = EVENTS_SOURCE_BROKERS;
        JobConfig.EVENTS_SOURCE_CONSUMER_GROUP_ID = EVENTS_SOURCE_CONSUMER_GROUP_ID;
        JobConfig.EVENTS_SOURCE_TOPIC = EVENTS_SOURCE_TOPIC.split(",");
        JobConfig.METRICS_CONFIG_HTTP_ERROR_METRIC_NAME = METRICS_CONFIG_HTTP_ERROR_METRIC_NAME;
        JobConfig.METRICS_CONFIG_HTTP_ERROR_METRIC_TYPE = METRICS_CONFIG_HTTP_ERROR_METRIC_TYPE;
        JobConfig.METRICS_CONFIG_HTTP_ERROR_METRIC_HELP = METRICS_CONFIG_HTTP_ERROR_METRIC_HELP;
        JobConfig.REMOTE_WRITE_METRICS_SINK_BROKERS = REMOTE_WRITE_METRICS_SINK_BROKERS;
        JobConfig.REMOTE_WRITE_METRICS_SINK_TOPIC = REMOTE_WRITE_METRICS_SINK_TOPIC;
    }
}
