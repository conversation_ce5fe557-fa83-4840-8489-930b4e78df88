package com.blinkit.alerting.apperrors.datatypes.events;

import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.annotation.JsonProperty;

@JsonIgnoreProperties(ignoreUnknown = true)
public class ContextOs {
    private final String name;
    private static final String NULL_VALUE = "null";

    public ContextOs(@JsonProperty(value = "name", defaultValue = NULL_VALUE) String name) {
        this.name = name;
    }

    public String accessOsName() {
        return name;
    }
}
