/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.blinkit.alerting.apperrors;

import org.apache.flink.api.common.ExecutionConfig;
import org.apache.flink.api.common.eventtime.WatermarkStrategy;
import org.apache.flink.api.connector.sink2.Sink;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.connector.base.DeliveryGuarantee;
import org.apache.flink.connector.kafka.sink.KafkaSink;
import org.apache.flink.connector.kafka.source.KafkaSource;
import org.apache.flink.connector.kafka.source.enumerator.initializer.OffsetsInitializer;
import org.apache.flink.streaming.api.CheckpointingMode;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.datastream.DataStreamSource;
import org.apache.flink.streaming.api.environment.CheckpointConfig;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.streaming.api.functions.windowing.ProcessWindowFunction;
import org.apache.flink.streaming.api.windowing.assigners.TumblingEventTimeWindows;
import org.apache.flink.streaming.api.windowing.time.Time;
import org.apache.flink.streaming.api.windowing.windows.TimeWindow;
import org.apache.flink.util.Collector;

import com.blinkit.alerting.apperrors.configs.*;
import com.blinkit.alerting.apperrors.datatypes.KafkaMetricMessage;
import com.blinkit.alerting.apperrors.datatypes.PrometheusMetric;
import com.blinkit.alerting.apperrors.datatypes.events.Event;
import com.blinkit.alerting.apperrors.serdes.JsonDeserializationSchema;
import com.blinkit.alerting.apperrors.serdes.KafkaMetricMessageSerializationSchema;

import java.time.Duration;
import java.time.Instant;
import java.util.Map;

import static java.time.Duration.ofSeconds;

/**
 * Skeleton for a Flink DataStream Job.
 *
 * <p>For a tutorial how to write a Flink application, check the tutorials and examples on the <a
 * href="https://flink.apache.org">Flink Website</a>.
 *
 * <p>To package your application into a JAR file for execution, run 'mvn clean package' on the
 * command line.
 *
 * <p>If you change the name of the main class (with the public static void main(String[] args))
 * method, change the respective entry in the POM.xml file (simply search for 'mainClass').
 */
public class AppErrorsJob {

    private final DataStreamSource<Event> appErrorEventsSource;
    private final Sink<KafkaMetricMessage> kafkaMetricMessageSink;

    public AppErrorsJob(
            DataStreamSource<Event> appErrorEventsSource,
            Sink<KafkaMetricMessage> kafkaMetricMessageSink) {
        this.appErrorEventsSource = appErrorEventsSource;
        this.kafkaMetricMessageSink = kafkaMetricMessageSink;
    }

    public static void main(String[] args) throws Exception {
        final StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment();

        // Parameters for environment
        ParameterTool params = ParameterTool.fromArgs(args);
        String userEnv = params.getRequired("environment");

        // Set PropertiesPath for userEnv
        JobConfigManager.setJobConfigsPath(userEnv);
        // Get PropertiesPath for userEnv
        String jobPropertiesPath = JobConfigManager.getJobConfigsPath(userEnv);
        // Fetch job config
        JobConfigManager.setJobConfigs(env, jobPropertiesPath);
        JobConfigManager.getJobConfigs(env);

        // Checkpoint Configs
        // start a checkpoint every 60000 ms
        env.enableCheckpointing(60000);
        // set mode to exactly-once (default)

        env.getCheckpointConfig().setCheckpointingMode(CheckpointingMode.EXACTLY_ONCE);
        // make sure 120000 ms of progress happen between checkpoints
        env.getCheckpointConfig().setMinPauseBetweenCheckpoints(60000);
        // checkpoints have to complete within a minute, or are discarded
        env.getCheckpointConfig().setCheckpointTimeout(300000);
        // only two consecutive checkpoint failures are tolerated
        env.getCheckpointConfig().setTolerableCheckpointFailureNumber(2);
        // allow only one checkpoint to be in progress at the same time
        env.getCheckpointConfig().setMaxConcurrentCheckpoints(1);
        // enable externalized checkpoints which are retained after job cancellation
        env.getCheckpointConfig()
                .setExternalizedCheckpointCleanup(
                        CheckpointConfig.ExternalizedCheckpointCleanup.RETAIN_ON_CANCELLATION);
        // enables the experimental unaligned checkpoints
        env.getCheckpointConfig().enableUnalignedCheckpoints();
        // sets the checkpoint storage where checkpoint snapshots will be written

        env.getCheckpointConfig().setCheckpointStorage(JobConfig.CHECKPOINTS_STORAGE_LOCATION);
        // Enable checkpoint compression
        ExecutionConfig executionConfig = new ExecutionConfig();
        executionConfig.setUseSnapshotCompression(true);

        KafkaSource<Event> appErrorsKafkaSource =
                KafkaSource.<Event>builder()
                        .setBootstrapServers(JobConfig.EVENTS_SOURCE_BROKERS)
                        .setGroupId(JobConfig.EVENTS_SOURCE_CONSUMER_GROUP_ID)
                        .setValueOnlyDeserializer(new JsonDeserializationSchema())
                        .setStartingOffsets(
                                OffsetsInitializer.timestamp(
                                        Instant.now().toEpochMilli()
                                                - Duration.ofMinutes(5).toMillis()))
                        .setTopics(JobConfig.EVENTS_SOURCE_TOPIC)
                        .setProperty("partition.discovery.interval.ms", "600000")
                        .setProperty("reconnect.backoff.max.ms", "300000")
                        .setProperty("reconnect.backoff.ms", "60000")
                        .build();

        WatermarkStrategy<Event> dauEventsSourceWatermarkStrategy =
                WatermarkStrategy.<Event>forBoundedOutOfOrderness(ofSeconds(300))
                        .withIdleness(ofSeconds(60))
                        .withTimestampAssigner(
                                (event, timestamp) -> event.accessEventTimestampEpochMilli());

        DataStreamSource<Event> appErrorEventsSource =
                env.fromSource(
                        appErrorsKafkaSource,
                        dauEventsSourceWatermarkStrategy,
                        "app-error-kafka-source");

        KafkaSink<KafkaMetricMessage> kafkaMetricsSink =
                KafkaSink.<KafkaMetricMessage>builder()
                        .setBootstrapServers(JobConfig.REMOTE_WRITE_METRICS_SINK_BROKERS)
                        .setRecordSerializer(
                                new KafkaMetricMessageSerializationSchema(
                                        JobConfig.REMOTE_WRITE_METRICS_SINK_TOPIC))
                        .setDeliverGuarantee(DeliveryGuarantee.AT_LEAST_ONCE)
                        .build();

        AppErrorsJob job = new AppErrorsJob(appErrorEventsSource, kafkaMetricsSink);
        job.execute(env);
    }

    private void execute(StreamExecutionEnvironment env) throws Exception {

        DataStream<PrometheusMetric> dataStream =
                appErrorEventsSource
                        .name("app-error-events-source")
                        .uid("app-error-events-source")
                        .setParallelism(4)
                        .keyBy(Event::getPrometheusLabel)
                        .window(TumblingEventTimeWindows.of(Time.minutes(1)))
                        .allowedLateness(Time.seconds(10))
                        .process(
                                new AlertWindowProcessingFunction(
                                        env.getConfig().getGlobalJobParameters().toMap()))
                        .setParallelism(4)
                        .name("app-error-counter-metrics")
                        .uid("app-error-counter-metrics");

        dataStream
                .map(
                        prometheusMetric ->
                                new KafkaMetricMessage(
                                        prometheusMetric.getMetricName(),
                                        "gauge",
                                        prometheusMetric.getMetricHelp(),
                                        prometheusMetric.getLabels(),
                                        Integer.valueOf(prometheusMetric.getMetricValue())
                                                .floatValue(),
                                        "blinkit",
                                        "app",
                                        prometheusMetric.getMetricTimestamp()))
                .setParallelism(4)
                .name("app-error-kafka-metric-map")
                .uid("app-error-kafka-metric-map")
                .sinkTo(kafkaMetricMessageSink)
                .name("kafka-prometheus-metrics-sink")
                .uid("kafka-prometheus-metrics-sink");

        env.execute("App Errors");
    }

    public static class AlertWindowProcessingFunction
            extends ProcessWindowFunction<
                    Event, PrometheusMetric, Map<String, String>, TimeWindow> {
        private final Map<String, String> jobConfigs;

        public AlertWindowProcessingFunction(Map<String, String> jobConfigs) {
            this.jobConfigs = jobConfigs;
        }

        @Override
        public void process(
                Map<String, String> labels,
                ProcessWindowFunction<Event, PrometheusMetric, Map<String, String>, TimeWindow>
                                .Context
                        context,
                Iterable<Event> iterable,
                Collector<PrometheusMetric> collector) {

            int counter = 0;
            for (Event ignored : iterable) counter++;

            collector.collect(
                    new PrometheusMetric(
                            jobConfigs.get(
                                    String.format(
                                            "METRICS_CONFIG_%s_METRIC_NAME",
                                            labels.get("event").toUpperCase().replace(' ', '_'))),
                            jobConfigs.get(
                                    String.format(
                                            "METRICS_CONFIG_%s_METRIC_TYPE",
                                            labels.get("event").toUpperCase().replace(' ', '_'))),
                            jobConfigs.get(
                                    String.format(
                                            "METRICS_CONFIG_%s_METRIC_HELP",
                                            labels.get("event").toUpperCase().replace(' ', '_'))),
                            labels,
                            labels,
                            Integer.toString(counter),
                            context.window().getEnd()));
        }
    }
}
