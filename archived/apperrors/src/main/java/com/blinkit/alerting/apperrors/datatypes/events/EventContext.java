package com.blinkit.alerting.apperrors.datatypes.events;

import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.annotation.JsonProperty;

@JsonIgnoreProperties(ignoreUnknown = true)
public class EventContext {

    private final ContextApp app;
    private final ContextOs os;

    public EventContext(
            @JsonProperty(value = "app", required = true) ContextApp app,
            @JsonProperty(value = "os", required = true) ContextOs os) {
        this.app = app;
        this.os = os;
    }

    public ContextApp accessApp() {
        return this.app;
    }

    public ContextOs accessOs() {
        return this.os;
    }
}
