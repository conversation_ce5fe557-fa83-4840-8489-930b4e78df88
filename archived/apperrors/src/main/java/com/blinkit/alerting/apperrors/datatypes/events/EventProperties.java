package com.blinkit.alerting.apperrors.datatypes.events;

import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.annotation.JsonProperty;

@JsonIgnoreProperties(ignoreUnknown = true)
public class EventProperties {
    private final String endpoint;
    private final String method;
    private final String statusCode;

    public EventProperties(
            @JsonProperty(value = "endpoint", defaultValue = "null") String endpoint,
            @JsonProperty(value = "method", defaultValue = "null") String method,
            @JsonProperty(value = "status_code", defaultValue = "null") String statusCode) {
        this.endpoint = endpoint;
        this.method = method;
        this.statusCode = statusCode;
    }

    public String accessEndpoint() {
        return this.endpoint;
    }

    public String accessMethod() {
        return this.method;
    }

    public String accessStatusCode() {
        return this.statusCode;
    }
}
