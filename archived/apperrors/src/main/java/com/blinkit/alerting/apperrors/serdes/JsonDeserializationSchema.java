package com.blinkit.alerting.apperrors.serdes;

import org.apache.flink.api.common.serialization.DeserializationSchema;
import org.apache.flink.api.common.typeinfo.TypeInformation;

import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.ObjectMapper;

import com.blinkit.alerting.apperrors.datatypes.events.Event;

import java.io.IOException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class JsonDeserializationSchema implements DeserializationSchema<Event> {
    private static final long serialVersionUID = 1L;
    private static final Logger LOG = LoggerFactory.getLogger(JsonDeserializationSchema.class);
    private static final ObjectMapper objectMapper = new ObjectMapper();

    @Override
    public Event deserialize(byte[] message) throws IOException {
        try {
        return objectMapper.readValue(message, Event.class);
        } catch (Exception exception) {
            exception.printStackTrace();
            LOG.info("Failed to deserialize message: {}", new String(message));
            return null;
        }
    }

    @Override
    public boolean isEndOfStream(Event event) {
        return false;
    }

    @Override
    public TypeInformation<Event> getProducedType() {
        return TypeInformation.of(Event.class);
    }
}
