package com.blinkit.alerting.apperrors.datatypes.events;

import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.annotation.JsonProperty;

import java.net.URI;
import java.net.URISyntaxException;
import java.sql.Timestamp;
import java.util.HashMap;
import java.util.Map;

@JsonIgnoreProperties(ignoreUnknown = true)
public class Event {
    private final String eventName;

    private final EventContext context;

    private final EventProperties properties;

    private final Timestamp eventTimestamp;

    public Event(
            @JsonProperty(value = "event", required = true) String eventName,
            @JsonProperty(value = "context", required = true) EventContext context,
            @JsonProperty(value = "properties", required = true) EventProperties properties,
            @JsonProperty(value = "timestamp", required = true)
                    @JsonFormat(
                            shape = JsonFormat.Shape.STRING,
                            pattern = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'",
                            timezone = "UTC")
                    Timestamp eventTimestamp) {
        this.eventName = eventName;
        this.context = context;
        this.properties = properties;
        this.eventTimestamp = eventTimestamp;
    }

    public String accessEventName() {
        return eventName;
    }

    public EventContext accessContext() {
        return this.context;
    }

    public EventProperties accessProperties() {
        return this.properties;
    }

    public Long accessEventTimestampEpochMilli() {
        return this.eventTimestamp.toInstant().toEpochMilli();
    }

    public Map<String, String> getPrometheusLabel() {
        Map<String, String> promLabels = new HashMap<>();
        promLabels.put("event", eventName);
        promLabels.put("app_version", context.accessApp().accessVersion());
        promLabels.put("os", context.accessOs().accessOsName());
        if (properties.accessMethod() != null) {
            promLabels.put("method", properties.accessMethod());
        }
        if (properties.accessEndpoint() != null) {
            try {
                URI uri = new URI(properties.accessEndpoint());
                promLabels.put("url_host", uri.getHost());
                promLabels.put(
                        "url_segment",
                        uri.getPath()
                                .replaceAll(
                                        "[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}",
                                        "<id>")
                                .replaceAll("\\/GR[0-9A-Z]{14}", "/<id>")
                                .replaceAll("\\/[0-9]+", "/<id>"));
            } catch (URISyntaxException exc) {
                promLabels.put("url_host", "unknown");
                promLabels.put("url_segment", "unknown");
            }
        }
        if (properties.accessStatusCode() != null) {
            promLabels.put("status_code", properties.accessStatusCode());
        }
        return promLabels;
    }
}
