FROM public.ecr.aws/zomato/flink:1.15.0-scala_2.12

RUN wget -P $FLINK_HOME/lib/ https://repo.maven.apache.org/maven2/org/apache/flink/flink-json/1.15.0/flink-json-1.15.0.jar; \
    wget -P $FLINK_HOME/lib/ https://repo.maven.apache.org/maven2/org/apache/flink/flink-s3-fs-hadoop/1.15.0/flink-s3-fs-hadoop-1.15.0.jar; \
    wget -P $FLINK_HOME/lib/ https://repo1.maven.org/maven2/org/apache/kafka/kafka-clients/2.8.0/kafka-clients-2.8.0.jar;

RUN mkdir -p $FLINK_HOME/usrlib

COPY src/main/resources /opt/flink/src/main/resources

COPY target/apperrors-1.0.jar /opt/flink/usrlib/apperrors.jar