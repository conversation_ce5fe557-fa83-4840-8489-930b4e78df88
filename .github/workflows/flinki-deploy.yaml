name: Deploy Flink Pipeline

on:
  workflow_dispatch:
    inputs:
      environment:
        description: 'K8s cluster environment'
        required: true
        default: 'stage'
        type: choice
        options: ['stage', 'prod']
      flink_binary:
        description: 'Binary to use to deploy the job'
        required: true
        default: '1.15'
        type: choice
        options: ['1.13', '1.14', '1.15', '1.13.0-scala_2.12', '1.14.4-scala_2.12', '1.17.1', '1.19.0']
      folder:
        description: 'Job folder'
        required: true
        default: 'alerting'
        type: choice
        options: ['alerting', 'core-streams', 'observability']
      name:
        description: 'Job name'
        required: true
        type: string
      command:
        description: 'Flink command to run to deploy the job'
        required: true
        default: 'docker-build-push'
        type: choice
        options: [
          'docker-build-push',
          'deploy-job',
          'delete-job',
          'delete-deployment',
          'stop-job-with-savepoint',
          'restart-job-from-savepoint',
          'deploy-podmonitor',
          'deploy-service-account'
        ]
      last_arg:
        description: 'Optional last argument, e.g. savepoint_path for restart-job-from-savepoint'
        required: false
        type: string

env:
    VAULT_URL: "https://vault-ui-prod.grofer.io"
    SLACK_CHANNEL: "#bl-data-deployments"

jobs:
  deploy-flink-pipeline:
    runs-on: [ self-hosted ]
    steps:
      - name: Checkout Code
        uses: actions/checkout@v3

      - name: Set Environment Variables
        run: |
          echo "PROJECT=${GITHUB_REPOSITORY##*/}" >> $GITHUB_ENV
        env:
          VAULT_TOKEN: ${{ secrets.GH_ACCESS_TOKEN }}
          VAULT_AUTH_GITHUB_TOKEN: ${{ secrets.GH_ACCESS_TOKEN }}
          
      - name: Build and Deploy
        if: github.event.inputs.environment == 'stage' || github.ref == 'refs/heads/master'
        run: |
          docker build --file Dockerfile.build \
            --build-arg FLINK_VERSION=${{ github.event.inputs.flink_binary }} \
            --tag flink-build .
          
          docker run --rm \
            -v /var/run/docker.sock:/var/run/docker.sock \
            flink-build \
            python3 scripts/commands.py \
            ${{ github.event.inputs.command }} \
            ${{ github.event.inputs.name }} \
            ${{ github.event.inputs.folder }} \
            ${{ github.event.inputs.environment }} \
            ${{ github.event.inputs.last_arg }}

      - name: Clean Workspace
        run: |
          rm -rf ${{ github.workspace }}/

      - name: Notify on Success
        if: ${{ success() && github.ref == 'refs/heads/master' }}
        run: |
          curl -X POST --data-urlencode "payload={\"channel\": \"${{ env.SLACK_CHANNEL }}\", \"text\": \"Pipeline ${{ github.workflow }} completed successfully.\"}" ${{ secrets.SLACK_WEBHOOK_URL }}

      - name: Notify on Failure
        if: ${{ failure() }}
        run: |
          curl -X POST --data-urlencode "payload={\"channel\": \"${{ env.SLACK_CHANNEL }}\", \"text\": \"Pipeline ${{ github.workflow }} failed. Please check.\"}" ${{ secrets.SLACK_WEBHOOK_URL }}
