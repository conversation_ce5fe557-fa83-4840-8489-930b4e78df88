pipeline {
  agent any

  options {
    buildDiscarder(logRotator(numToKeepStr: "15", daysToKeepStr: "5"))
    disableConcurrentBuilds()
    skipStagesAfterUnstable()
  }

  environment {
    VAULT_TOKEN = credentials("jenkins-github-token")
    VAULT_AUTH_GITHUB_TOKEN = credentials("jenkins-github-token")
    VAULT_URL = "https://vault-ui-prod.grofer.io"
    SLACK_CHANNEL = "#bl-data-deployments"
    PROJECT = env.JOB_NAME.toLowerCase()
  }

  parameters {
    choice(name: 'environment', choices:['stage', 'prod'], description: 'k8s cluster environment')
    choice(name: 'flink_binary', choices:['1.13', '1.14', '1.15','1.13.0-scala_2.12','1.14.4-scala_2.12', '1.17.1', '1.19.0'], description: 'binary to use to deploy the job')
    choice(name: 'folder', choices:[
      "alerting",
      "core-streams",
      "observability",
    ], description: 'job folder')
    string(name: 'name', description: 'job name')
    choice(name: 'command', choices: [
      "docker-build-push",
      "deploy-job",
      "delete-job",
      "delete-deployment",
      "stop-job-with-savepoint",
      "restart-job-from-savepoint",
      "deploy-podmonitor",
      "deploy-service-account",
    ], description: 'flink command to run to deploy the job')
    booleanParam(name: 'deployOnDemand', defaultValue: false, description: 'Used during deployment to deploy Task Manager on On-Demand Nodes')
    string(name: 'last_arg', description: 'can we be used to pass last arg based on job. for eg pass savepoint_path for restart-job-from-savepoint')
  }

  stages {
    stage("Deploy the flink pipeline") {
      when {
          anyOf {
            expression { env.BRANCH_NAME == "master" }
            expression { env.environment=='stage' }
          }
      }
      agent {
        dockerfile {
          filename 'Dockerfile.build'
          additionalBuildArgs  "--build-arg FLINK_VERSION=${env.flink_binary}"
          args "-v /var/run/docker.sock:/var/run/docker.sock --entrypoint '' -u root --privileged"
        }
      }
      steps {
        withKubeConfig([credentialsId:'prod-eks-kubeconfig']) {
          sh(script: "FLINK_BINARY_VERSION=${env.flink_binary} ON_DEMAND=${env.deployOnDemand} python3 scripts/commands.py ${env.command} ${env.name} ${env.folder} ${env.environment} ${env.last_arg}")
        }
      }
    }

    // stage ("Clean workspace") {
    //   steps {
    //     cleanWs(cleanWhenAborted: true, cleanWhenFailure: true, cleanWhenNotBuilt: true, cleanWhenSuccess: true, cleanupMatrixParent: true, deleteDirs: true, notFailBuild: true)
    //   }
    // }
  }

  post {
        success {
          script {
            if (env.BRANCH_NAME == "master") {
              slackSend(color: "good", message: "[${PROJECT}]: pipeline ${currentBuild.fullDisplayName} completed successfully. See ${env.RUN_DISPLAY_URL} for details.", channel: "$SLACK_CHANNEL")
            }
          }
        }
        failure {
          script {
            slackSend(color: "danger", message: "[${PROJECT}]: pipeline ${currentBuild.fullDisplayName} failed. Please contact data on call. See ${env.RUN_DISPLAY_URL} for details.", channel: "$SLACK_CHANNEL")
          }
        }
    }
}
